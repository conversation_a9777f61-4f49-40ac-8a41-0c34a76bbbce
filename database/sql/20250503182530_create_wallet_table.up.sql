CREATE TABLE `wallet` (
    `id`        varchar(30)             NOT NULL COMMENT '唯一ID',
    `uid`       varchar(20)             NOT NULL,
    `currency`  varchar(20)             NOT NULL COMMENT '币种',
    `balance`   decimal(30,15)          DEFAULT 0 NOT NULL COMMENT '账户余额',
    `frozen`    decimal(30,15)          DEFAULT 0 NOT NULL COMMENT '冻结余额',
    `create_time` bigint                DEFAULT 0 NOT NULL COMMENT '创建时间',
    `update_time` bigint                DEFAULT 0 NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uid_currency` (`uid`, `currency`),
    KEY `idx_uid` (`uid`)
) ENGINE=InnoDB COMMENT ='资产' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
