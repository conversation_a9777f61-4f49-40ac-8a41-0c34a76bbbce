package monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/message"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/sqllib"

	"github.com/jinzhu/gorm"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

func SyncTrialAssetDb(ctx context.Context, wg *sync.WaitGroup) {
	_amqp, err := mqlib.New()
	if err != nil {
		log.Println("mqlib.New() err in sync asset db")
	}
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync trial asset")
	}
	trialAssetQuery := entity.NewTrialAssetQuery(db)
	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			log.Println("sync Trial Asset to db monitor stopped")
			wg.Done()
			if _amqp != nil {
				log.Printf("close mq client %+v", _amqp.Close())
			}
			return
		default:
			results, err := redislib.Redis().BRPop(domain.SyncTrialAssetSwap, domain.RedisBRPOPTimeout)
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					logrus.Error(fmt.Sprintf("brpop sync trial asset err:%v", err))
				}
				continue
			}
			if len(results) <= 1 {
				continue
			}
			trialAsset := new(entity.TrialAsset)
			// logrus.Info(0, "============= SyncTrialAssetDb source:", results[1])
			err = json.Unmarshal([]byte(results[1]), trialAsset)
			if err != nil {
				logrus.Error(fmt.Sprintf("sync trial asset json unmarshal err:%v  data:%+v", err, results[1]))
				continue
			}

			trialAssetQuery.TrialAsset = *trialAsset
			switch trialAsset.OpType {
			case domain.BillTrialAssetAdd:
				// logrus.Info(0, "============= SyncTrialAssetDb BillTrialAssetAdd:", fmt.Sprintf("%+v", trialAsset))
				err = trialAssetQuery.CreateData()
				if err != nil {
					logrus.Error(0, fmt.Sprintf("SyncTrialAssetDb sync trial asset db Create err:%+v  data:%+v", err, trialAssetQuery.TrialAsset))
				}

			default:
				counter, err := trialAssetQuery.CountDataByAwardOpId(trialAsset.AwardOpId)
				if counter > 0 {
					// logrus.Info(0, "============= SyncTrialAssetDb Update:", fmt.Sprintf("%+v", trialAsset))
					err = trialAssetQuery.Update()
					if err != nil {
						logrus.Error(0, fmt.Sprintf("SyncTrialAssetDb trial Asset update err:%+v,TrialAsset:%+v", err, trialAssetQuery.TrialAsset))
					}
				} else if (counter == 0 && err == nil) || gorm.IsRecordNotFoundError(err) {
					// logrus.Info(0, "============= SyncTrialAssetDb CreateData:", fmt.Sprintf("%+v", trialAsset))
					err = trialAssetQuery.CreateData()
					if err != nil {
						logrus.Error(0, fmt.Sprintf("SyncTrialAssetDb sync trial asset db update Create err:%+v  data:%+v", err, trialAssetQuery.TrialAsset))
					}
				}
			}

			// 增加资产体验金详情推送
			go message.New(trialAsset.UID, message.AssetQueueIndex, _amqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
				MsgType: message.AccountMsgTypeTrialDetail,
				MsgData: trialAsset,
			})

			userCache := swapcache.NewPosCache(swapcache.CacheParam{}, trialAsset.UID)
			userAsset, err := userCache.Load()
			if err != nil {
				logrus.Error(0, "SyncTrialAssetDb Load userAsset error:", err)
			} else {
				for currency, balance := range userAsset.TrialBalance {
					if currency != trialAsset.Currency {
						continue
					}
					assetMsg := message.AssetMsg{
						Currency:     currency,
						TrialBalance: balance,
					}
					go message.New(trialAsset.UID, message.AssetQueueIndex, _amqp).
						TopicAsset(message.SwapAccount).Push(message.AccountMsg{
						MsgType: message.AccountMsgTypeTrialAsset,
						MsgData: assetMsg,
					})
				}
			}
		}
	}
}
