package monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/eslib"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

func SyncOptionBill(ctx context.Context, wg *sync.WaitGroup) {
	var esClient *eslib.ESBase
	_amqp, err := mqlib.New()
	if err != nil {
		log.Println("mqlib.New() err in sync option asset bill")
	}
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync option asset bill")
	}
	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			if _amqp != nil {
				log.Println(_amqp.Close())
			}
			log.Println("sync option asset bill monitor stopped")
			wg.Done()
			return
		default:
			results, err := redislib.Redis().BRPop(domain.SyncListOptionBill, domain.RedisBRPOPTimeout)
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					logrus.Error(fmt.Sprintf("brpop option asset bill err:%v", err))
				}
				continue
			}
			bill := new(repository.BillOptionSync)
			if len(results) > 1 {
				err := json.Unmarshal([]byte(results[1]), bill)
				if err != nil {
					logrus.Error(fmt.Sprintf(" option asset bill json unmarshal err:%v  data:%+v", err, results[1]))
					continue
				}

				if bill.UID == "" {
					logrus.Error(fmt.Sprintf("option asset bill uid nil:%v  data:%+v", err, *bill))
					continue
				}

				if bill.OptionType == domain.OptionTypeFirm {
					esClient = eslib.New(domain.EsOptionBillIndex)
					updateProfitLoseWithBill(bill.BillOption) // 更新盈亏记录
					if bill.BillType != domain.BillTypeOptionFee &&
						bill.BillType != domain.BillTypeOptionFeeReturn {
						updateOptionTotalProfit(bill.BillOption) // 更新累计盈亏以及未行权总额
					}
				}
				if bill.OptionType == domain.OptionTypeDemo {
					esClient = eslib.New(domain.EsOptionBillDemoIndex)
				}
				// 期权盈利为负数时不记录资金流水
				if bill.BillType == domain.BillTypeOptionProfitReal && bill.Amount.Sign() <= 0 {
					continue
				}
				err = bill.Insert(db)
				if err != nil {
					logrus.Error(fmt.Sprintf("insert option bill db err:%v data:%+v", err, *bill))
					_ = redislib.Redis().LPush(domain.SyncListOptionBill, results[1])
					continue
				}
				_ = esClient.Create(ctx, bill)
			}
		}
	}
}

func updateProfitLoseWithBill(_bill entity.BillOption) {
	// 获取记账当天起始的时间戳
	var (
		dayStart    = util.OneDayBeginAndEndTimeStamp(time.Unix(_bill.OperateTime/1e9, 0))
		startFormat = strconv.FormatInt(dayStart, 10)
		pnlHashKey  = setting.GetOptionProfitDataKeyByTime(startFormat)
		subKey      = _bill.UID + util.ProfitLossBassKey(_bill.Currency)
		pnl         = &entity.OptionProfitLoss{
			UID:         _bill.UID,
			Currency:    _bill.Currency,
			OperateTime: dayStart,
		}
	)
	if redislib.Redis().HExist(pnlHashKey) {
		// 操作当前的数据还在redis 中
		redisData, err := redislib.Redis().HGet(pnlHashKey, subKey)
		if err != nil {
			logrus.Info(fmt.Sprintf("update option profit call redis het err:%+v, bill:%+v", err, _bill))
		}
		if len(redisData) > 0 {
			_ = json.Unmarshal([]byte(redisData), pnl)
		}
		if _bill.BillType == domain.BillTypeOptionPremium {
			pnl.NetIn = pnl.NetIn.Add(_bill.Amount)
		}
		if _bill.BillType == domain.BillTypeOptionPremiumReturn {
			pnl.NetIn = pnl.NetIn.Sub(_bill.Amount)
		}
		if _bill.BillType == domain.BillTypeOptionFee {
			pnl.TotalFee = pnl.TotalFee.Add(_bill.Amount)
		}
		if _bill.BillType == domain.BillTypeOptionFeeReturn {
			pnl.TotalFee = pnl.TotalFee.Sub(_bill.Amount)
		}
		if _bill.BillType == domain.BillTypeOptionProfitReal {
			pnl.ProfitLoss = pnl.ProfitLoss.Add(_bill.Amount)
			if _bill.Amount.Sign() >= 0 {
				pnl.Profit = pnl.Profit.Add(_bill.Amount.Abs())
				pnl.ProfitPremium = pnl.ProfitPremium.Add(_bill.Premium.Abs())
				pnl.ProfitCount = pnl.ProfitCount + 1
			}
			if _bill.Amount.Sign() < 0 {
				pnl.Loss = pnl.Loss.Add(_bill.Amount.Abs())
				pnl.LossCount = pnl.LossCount + 1
			}
		}

		if pnl.UpdateTime < _bill.OperateTime {
			pnl.UpdateTime = _bill.OperateTime
		}
		profitData, _ := json.Marshal(pnl)
		if err = redislib.Redis().HSet(pnlHashKey, subKey, string(profitData)); err != nil {
			logrus.Error(fmt.Sprintf("update profit call redis hset err:%+v, profitData:%+v,_bill:%+v", err, profitData, _bill))
		}
		return
	} else {
		todayStart := util.OneDayBeginAndEndTimeStamp(time.Now())
		if dayStart >= todayStart {
			// 假如是今天之后的数据
			pnl.CreateTime = time.Now().UnixNano()
			pnl.UpdateTime = _bill.OperateTime
			if _bill.BillType == domain.BillTypeOptionPremium {
				pnl.NetIn = pnl.NetIn.Add(_bill.Amount)
			}
			if _bill.BillType == domain.BillTypeOptionPremiumReturn {
				pnl.NetIn = pnl.NetIn.Sub(_bill.Amount)
			}
			if _bill.BillType == domain.BillTypeOptionProfitReal {
				pnl.ProfitLoss = pnl.ProfitLoss.Add(_bill.Amount)
				if _bill.Amount.Sign() >= 0 {
					pnl.Profit = pnl.Profit.Add(_bill.Amount.Abs())
					pnl.ProfitPremium = pnl.ProfitPremium.Add(_bill.Premium.Abs())
				} else {
					pnl.Loss = pnl.Loss.Add(_bill.Amount.Abs())
				}
			}

			profitData, _ := json.Marshal(pnl)
			err := redislib.Redis().HSet(pnlHashKey, subKey, string(profitData))
			if err != nil {
				logrus.Error(fmt.Sprintf("update profit call redis hset err:%+v, profitData:%+v,_bill:%+v", err, profitData, _bill))
			}
			_, err = redislib.Redis().SAdd(setting.GetOptionProfitDateKey(), startFormat)
			if err != nil {
				logrus.Error(fmt.Sprintf("update profit call redis SAdd dayTimeStr:%s err:%+v, _bill:%+v", startFormat, err, _bill))
			}
			return
		} else {
			// 是历史数据 直接更新数据库
			err := pnl.GetData()
			if err != nil {
				logrus.Error(fmt.Sprintf("get profit data err:%+v, pnl:%+v, _bill:%+v", err, pnl, _bill))
			}
			pnl.UpdateTime = _bill.OperateTime
			if _bill.BillType == domain.BillTypeOptionPremium {
				pnl.NetIn = pnl.NetIn.Add(_bill.Amount)
			}
			if _bill.BillType == domain.BillTypeOptionPremiumReturn {
				pnl.NetIn = pnl.NetIn.Sub(_bill.Amount)
			}
			if _bill.BillType == domain.BillTypeOptionProfitReal {
				pnl.ProfitLoss = pnl.ProfitLoss.Add(_bill.Amount)
				if _bill.Amount.Sign() >= 0 {
					pnl.Profit = pnl.Profit.Add(_bill.Amount.Abs())
					pnl.ProfitPremium = pnl.ProfitPremium.Add(_bill.Premium.Abs())
				} else {
					pnl.Loss = pnl.Loss.Add(_bill.Amount.Abs())
				}
			}
			_ = pnl.UpsertProfitLoss(nil)
		}
	}
}

func updateOptionTotalProfit(_bill entity.BillOption) {
	var (
		key    = util.GetTotalProfitRedisKey(_bill.UID, _bill.Currency)
		total  = entity.OptionTotalProfit{}
		client = redislib.Redis()
	)
	result, err := client.HGet(domain.OptionTotalProfitHash, key)
	if err != nil && err.Error() != "redis: nil" {
		logrus.Error(fmt.Sprintf("redis conn err:%+v,_bill:%+v", err, _bill))
		return
	}
	if result == "" {
		total.UID = _bill.UID
		total.Currency = _bill.Currency
		total.CreateTime = time.Now().UnixNano()
	} else {
		_ = json.Unmarshal([]byte(result), &total)
	}
	if _bill.BillType == domain.BillTypeOptionProfitReal {
		if _bill.Amount.Sign() >= 0 {
			total.TotalProfit = total.TotalProfit.Add(_bill.Amount)                      // 累计盈利 (累计期权订单已实现盈亏>=0总和)
			total.TotalProfitPremium = total.TotalProfitPremium.Add(_bill.Premium.Abs()) // 累计期权订单已实现盈亏>=0权力金总和
		} else {
			total.TotalLoss = total.TotalLoss.Add(_bill.Amount.Abs()) // 累计亏损 (累计期权订单已实现盈亏<0权利金总和)
		}
		total.Unexercised = total.Unexercised.Sub(_bill.Premium) // 减少未行权权利金
	}
	if _bill.BillType == domain.BillTypeOptionPremium {
		total.Unexercised = total.Unexercised.Add(_bill.Amount) // 增加未行权权利金
	}
	if _bill.BillType == domain.BillTypeOptionPremiumReturn {
		total.Unexercised = total.Unexercised.Sub(_bill.Amount) // 减少未行权权利金
	}
	total.UpdateTime = time.Now().UnixNano()
	profitByte, _ := json.Marshal(total)
	if err := client.HSet(domain.OptionTotalProfitHash, key, string(profitByte)); err != nil {
		logrus.Errorf("update option total profit err:%s key:%s value:%s", err.Error(), key, string(profitByte))
	}
}
