package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"futures-asset/internal/domain"
	repository "futures-asset/internal/domain/entity"
	"futures-asset/internal/message"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/rocketmq"
	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/sirupsen/logrus"
)

func SyncPosDb(ctx context.Context, wg *sync.WaitGroup, contractCode string) {
	_amqp, err := mqlib.New()
	if err != nil {
		log.Println("mqlib.New() err in sync asset db")
	}
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync pos db")
	}
	base, quote := util.BaseQuote(contractCode)

	logrus.Infof("%s pos persist consumer subscribe", contractCode)
	err = rocketmq.RMQ().AddConsumer(rocketmq.ConsumerGroup, base, quote)
	if err != nil {
		logrus.Errorf("add consumer about pos persist err:%s symbol:%s%s", err.Error(), base, quote)
		return
	}
	pairConsumer := rocketmq.RMQ().Consumer(rocketmq.ConsumerGroup, base, quote)
	err = pairConsumer.Subscribe(
		rocketmq.GetOrderTopic(base, quote),
		consumer.MessageSelector{},
		func(ctx context.Context, msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
			for i := range msgs {
				fmt.Printf("receive from pos persist topic %s receive msg %s\n", contractCode, string(msgs[i].Body))
				results := msgs[i].Body

				pos := new(repository.LogPosSync)
				if len(results) > 0 {
					err := json.Unmarshal(results, pos)
					if err != nil {
						logrus.Error(fmt.Sprintf("swap pos json unmarshal err:%v  data:%+v", err, results[1]))
						continue
					}

					go message.New(pos.PosSwap.UID, message.PosQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(pos.Pos)

					if pos.PosSwap.UID == "" {
						logrus.Error(fmt.Sprintf("swap pos uid nil:%v  data:%+v", err, *pos))
						continue
					}

					err = pos.PosSwap.UpsertPos(db)
					if err != nil {
						logrus.Error(fmt.Sprintf("swap pos update db err:%v  data:%+v", err, *pos))
					}

					if len(pos.LogPos.UID) > 0 && pos.PosSwap.UserType != domain.UserTypePlatformRobot {
						err = pos.LogPos.Insert(db)
						if err != nil {
							logrus.Error(fmt.Sprintf("swap pos log insert into db err:%v  data:%+v", err, pos.LogPos))
						}
					}
				}
			}
			return consumer.ConsumeSuccess, nil
		})
	if err != nil {
		logrus.Errorf(" %s pos persist consumer subscribe err: %s", contractCode, err.Error())
		return
	}
	err = pairConsumer.Start()
	if err != nil {
		logrus.Errorf(" %s pos persist consumer start err: %s", contractCode, err.Error())
		return
	}

	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			if _amqp != nil {
				log.Println(_amqp.Close())
			}
			log.Printf("sync %s pos monitor stopped", contractCode)
			wg.Done()
			return
		}
	}
}
