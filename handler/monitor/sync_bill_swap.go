package monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/message"
	"futures-asset/pkg/eslib"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

func SyncBillAsset(ctx context.Context, wg *sync.WaitGroup) {
	esBill := eslib.New(domain.EsBillIndex)

	_amqp, err := mqlib.New()
	if err != nil {
		log.Println("mqlib.New() err in sync asset db")
	}
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync swap bill db")
	}
	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			if _amqp != nil {
				log.Println(_amqp.Close())
			}
			log.Println("sync swap bill monitor stopped")
			wg.Done()
			return
		default:
			results, err := redislib.Redis().BRPop(domain.SyncListSwapBill, domain.RedisBRPOPTimeout)
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					logrus.Error(fmt.Sprintf("brpop swap bill err:%v", err))
				}
				continue
			}
			bill := new(repository.BillAssetSync)
			if len(results) > 1 {
				err := json.Unmarshal([]byte(results[1]), bill)
				if err != nil {
					logrus.Error(fmt.Sprintf("swap swap bill json unmarshal err:%v  data:%+v", err, results[1]))
					continue
				}

				if bill.UID == "" {
					logrus.Error(fmt.Sprintf("swap swap bill uid nil:%v  data:%+v", err, *bill))
					continue
				}
				switch bill.BillType {
				case domain.BillTypeReal, domain.BillTypeRealTrial, domain.BillTypeDeductProfitReal:
					UpdateProfitLoseWithBill(bill.BillAsset)
					UpdateTotalProfit(bill.BillAsset)

				case domain.BillTypeInnerIn, domain.BillTypeInnerOut:
					// 划转账单 钱包发  合约钱包不用发，只需要更新下盈亏记录
					UpdateProfitLoseWithBill(bill.BillAsset)

				case domain.BillTypeSubsidy, domain.BillTypeCloseSubsidy:
					UpdateSubsidy(bill.BillAsset)

				default:

				}

				go message.New(bill.UID, message.AssetQueueIndex, _amqp).TopicBill(message.SwapAccount).Push(bill)

				err = bill.Insert(db)
				if err != nil {
					logrus.Error(fmt.Sprintf("swap swap bill insert db err:%v  data:%+v", err, *bill))
					_ = redislib.Redis().LPush(domain.SyncListSwapBill, results[1])
					continue
				}
				err = esBill.Create(ctx, bill)
				if err != nil {
					logrus.Error(fmt.Sprintf("swap swap bill insert es err:%v  data:%+v", err, *bill))
					// _ = redislib.Redis().LPush(domain.SyncListSwapBill, results[1])
					continue
				}
			}
		}
	}
}

func SyncRobotBillAsset(ctx context.Context, wg *sync.WaitGroup) {
	esBill := eslib.New(domain.EsBillIndex)

	_amqp, err := mqlib.New()
	if err != nil {
		log.Println("mqlib.New() err in sync robot asset db")
	}
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync robot swap bill db")
	}
	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			if _amqp != nil {
				log.Println(_amqp.Close())
			}
			log.Println("sync robot swap bill monitor stopped")
			wg.Done()
			return
		default:
			results, err := redislib.Redis().BRPop(domain.SyncListSwapBillRobot, domain.RedisBRPOPTimeout)
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					logrus.Error(fmt.Sprintf("brpop swap bill err:%v", err))
				}
				continue
			}
			bill := new(repository.BillAssetSync)
			if len(results) > 1 {
				err := json.Unmarshal([]byte(results[1]), bill)
				if err != nil {
					logrus.Error(fmt.Sprintf("swap robot swap bill json unmarshal err:%v  data:%+v", err, results[1]))
					continue
				}

				if bill.UID == "" {
					logrus.Error(fmt.Sprintf("swap robot swap bill uid nil:%v  data:%+v", err, *bill))
					continue
				}

				err = bill.InsertRobot(db)
				if err != nil {
					logrus.Error(fmt.Sprintf("swap robot swap bill insert db err:%v  data:%+v", err, *bill))
					_ = redislib.Redis().LPush(domain.SyncListSwapBillRobot, results[1])
					continue
				}
				_ = esBill.Create(ctx, bill)
			}
		}
	}
}

func UpdateProfitLoseWithBill(_bill entity.BillAsset) {
	// 获取记账当天起始的时间戳
	dayStartTime := util.OneDayBeginAndEndTimeStamp(time.Unix(_bill.OperateTime/1e9, 0))
	dayTimeStr := strconv.FormatInt(dayStartTime, 10)
	profitHashKey := setting.GetProfitDataKeyByTime(dayTimeStr)
	key := _bill.UID + util.ProfitLossBassKey(_bill.Currency)
	if redislib.Redis().HExist(profitHashKey) {
		// 操作当前的数据还在redis 中
		redisData, err := redislib.Redis().HGet(profitHashKey, key)
		if err != nil {
			logrus.Info(fmt.Sprintf("bill update profit get redis data err:%+v, bill:%+v", err, _bill))
		}
		profitLossData := &entity.ProfitLoss{}
		if redisData == "" {
			profitLossData.UID = _bill.UID
			profitLossData.Currency = _bill.Currency
			profitLossData.OperateTime = dayStartTime
		} else {
			_ = json.Unmarshal([]byte(redisData), profitLossData)
		}

		if _bill.BillType == domain.BillTypeInnerIn || _bill.BillType == domain.BillTypeInnerOut {
			profitLossData.NetIn = profitLossData.NetIn.Add(_bill.Amount)
		} else {
			profitLossData.ProfitLoss = profitLossData.ProfitLoss.Add(_bill.Amount)
		}

		if profitLossData.UpdateTime < _bill.OperateTime {
			profitLossData.UpdateTime = _bill.OperateTime
		}
		profitData, _ := json.Marshal(profitLossData)
		err = redislib.Redis().HSet(profitHashKey, key, string(profitData))
		if err != nil {
			logrus.Error(fmt.Sprintf("update profit set err:%+v, profitData:%+v,_bill:%+v", err, profitData, _bill))
		}
		return
	} else {
		todayStart := util.OneDayBeginAndEndTimeStamp(time.Now())
		if dayStartTime >= todayStart {
			// 假如是今天之后的数据
			profitLossData := entity.ProfitLoss{
				UID:         _bill.UID,
				Currency:    _bill.Currency,
				NetIn:       decimal.Zero,
				ProfitLoss:  decimal.Zero,
				OperateTime: dayStartTime,
			}

			if _bill.BillType == domain.BillTypeInnerIn || _bill.BillType == domain.BillTypeInnerOut {
				profitLossData.NetIn = profitLossData.NetIn.Add(_bill.Amount)
			} else {
				profitLossData.ProfitLoss = profitLossData.ProfitLoss.Add(_bill.Amount)
			}
			profitLossData.CreateTime = time.Now().UnixNano()
			profitLossData.UpdateTime = _bill.OperateTime
			profitData, _ := json.Marshal(profitLossData)
			err := redislib.Redis().HSet(profitHashKey, key, string(profitData))
			if err != nil {
				logrus.Error(fmt.Sprintf("update profit set err:%+v, profitData:%+v,_bill:%+v", err, profitData, _bill))
			}

			_, err = redislib.Redis().SAdd(setting.GetProfitDateKey(), dayTimeStr)
			if err != nil {
				logrus.Error(fmt.Sprintf("update profit SAdd dayTimeStr:%s err:%+v, _bill:%+v", dayTimeStr, err, _bill))
			}
			return
		} else {
			// 是历史数据 直接更新数据库
			profitLossData := entity.ProfitLoss{
				UID:         _bill.UID,
				Currency:    _bill.Currency,
				OperateTime: dayStartTime,
			}
			err := profitLossData.GetData()
			if err != nil {
				logrus.Error(fmt.Sprintf("get profit data err:%+v, profitLossData:%+v, _bill:%+v", err, profitLossData, _bill))
			}
			if _bill.BillType == domain.BillTypeInnerIn || _bill.BillType == domain.BillTypeInnerOut {
				profitLossData.NetIn = profitLossData.NetIn.Add(_bill.Amount)
			} else {
				profitLossData.ProfitLoss = profitLossData.ProfitLoss.Add(_bill.Amount)
			}
			profitLossData.UpdateTime = _bill.OperateTime
			_ = profitLossData.UpsertProfitLoss(nil)
		}
	}
}

func UpdateTotalProfit(_bill entity.BillAsset) {
	key := util.GetTotalProfitRedisKey(_bill.UID, _bill.Currency)
	profitData := entity.TotalProfit{}
	result, err := redislib.Redis().HGet(domain.TotalProfitHash, key)
	if err != nil && err.Error() != "redis: nil" {
		logrus.Error(fmt.Sprintf("redis conn err:%+v,_bill:%+v", err, _bill))
		return
	}
	if result == "" {
		profitData.UID = _bill.UID
		profitData.Currency = _bill.Currency
		profitData.CreateTime = time.Now().UnixNano()
	} else {
		_ = json.Unmarshal([]byte(result), &profitData)
	}
	profitData.TotalProfit = profitData.TotalProfit.Add(_bill.Amount)
	profitData.UpdateTime = time.Now().UnixNano()
	profitByte, _ := json.Marshal(profitData)
	_ = redislib.Redis().HSet(domain.TotalProfitHash, key, string(profitByte))
}

func UpdateSubsidy(_bill entity.BillAsset) {
	key := util.GetTotalProfitRedisKey(_bill.UID, _bill.Currency)
	profitData := entity.TotalProfit{}
	result, err := redislib.Redis().HGet(domain.TotalProfitHash, key)
	if err != nil && err.Error() != "redis: nil" {
		logrus.Error(fmt.Sprintf("redis conn err:%+v,_bill:%+v", err, _bill))
		return
	}
	if result == "" {
		profitData.UID = _bill.UID
		profitData.Currency = _bill.Currency
		profitData.CreateTime = time.Now().UnixNano()
	} else {
		_ = json.Unmarshal([]byte(result), &profitData)
	}
	profitData.Subsidy = profitData.Subsidy.Add(_bill.Amount)
	profitData.UpdateTime = time.Now().UnixNano()
	profitByte, _ := json.Marshal(profitData)
	_ = redislib.Redis().HSet(domain.TotalProfitHash, key, string(profitByte))
}
