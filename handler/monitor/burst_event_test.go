package monitor

import (
	"log"
	"testing"

	"futures-asset/conf"
	"futures-asset/internal/domain/es"
	"futures-asset/pkg/eslib"
)

func TestBurstEvent_UpdateBurstOverflow(t *testing.T) {
	// init config
	c, err := conf.Init()
	if err != nil {
		log.Fatalf("init config err: %s", err.Error())
	}
	defer c.Close()
	// init es
	if err := eslib.Init(c.Mid.ES.Contract); err != nil {
		log.Fatalf("init es err: " + err.Error())
	}
	es.InitEsIndex()

	event := new(BurstEvent)
	event.BurstId = "01569172559904374784"
	event.UID = "37506182"
	event.UpdateBurstOverflow() // 更新爆仓单穿仓状态
}
