package monitor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"futures-asset/cache/memorycache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/db/swap"
	"futures-asset/pkg/match"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/sqllib"
	"log"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

func SyncUserStatistics(ctx context.Context, wg *sync.WaitGroup) {
	db, err := sqllib.Db()
	if err != nil {
		log.Println("get db err in sync user statistics")
	}
	for {
		select {
		case <-ctx.Done():
			time.Sleep(5 * time.Second)
			log.Println("sync user statistics to db monitor stopped")
			wg.Done()
			return
		default:
			results, err := redislib.Redis().BRPop(domain.SyncListUserStatistics, domain.RedisBRPOPTimeout)
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					logrus.Error(fmt.Sprintf("brpop sync user statistics err:%v", err))
				}
				continue
			}
			statistics := new(swap.UserStatistics)
			if len(results) <= 1 {
				continue
			}
			err = json.Unmarshal([]byte(results[1]), statistics)
			if err != nil {
				logrus.Error(fmt.Sprintf("sync user statistics json unmarshal err:%v  data:%+v", err, results[1]))
				continue
			}

			if len(statistics.UID) == 0 {
				continue
			}
			if len(statistics.AccountType) == 0 {
				statistics.AccountType = match.AccountTypeSwap
			}
			cachedId := statistics.GetId()
			cachedData := memorycache.GetUserStatistics(cachedId)
			cachedData.Id = cachedId
			cachedData.UID = statistics.UID
			cachedData.AccountType = statistics.AccountType
			if cachedData.FirstOpenTime == 0 {
				cachedData.FirstOpenTime = statistics.FirstOpenTime
			}
			if cachedData.LatestPosId != statistics.LatestPosId {
				cachedData.LatestPosId = statistics.LatestPosId
				cachedData.HoldPosTimes = cachedData.HoldPosTimes + 1
			}
			cachedData.HoldPosValue = cachedData.HoldPosValue.Add(statistics.HoldPosValue)
			memorycache.CacheUserStatistics(cachedData.Id, cachedData)

			err = cachedData.Upsert(db)
			if err != nil {
				logrus.Error(fmt.Sprintf("user statistics update err:%+v,data:%+v", err, cachedData))
			}
		}
	}
}
