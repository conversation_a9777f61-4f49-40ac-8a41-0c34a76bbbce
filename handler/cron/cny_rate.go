package cron

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"futures-asset/conf"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

func StartCnyRate() {
	defer func() {
		if err := recover(); err != nil {
			logrus.Error(fmt.Sprintf("cron get cny rate failed,err:%v", err))
			go StartCnyRate()
		}
	}()
	GetCnyUsdRate()
	ticker := time.NewTicker(time.Minute)
	for range ticker.C {
		GetCnyUsdRate()
	}
}

func GetCnyUsdRate() {
	cny := GetUsdToCnyRate()
	if cny.IsZero() {
		return
	}
	domain.CnyRate = cny
}

func GetUsdToCnyRate() decimal.Decimal {
	paramBytes, _ := json.Marshal("")
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["otc"], "/rate/cnyUsd")
	res, err := http.Post(url, "application/json", bytes.NewBuffer(paramBytes))
	if err != nil {
		logrus.Error(fmt.Sprintf("update rate err:%+v", err))
		return decimal.Zero
	}
	resBytes, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logrus.Error(fmt.Sprintf("update rate err:%+v", err))
		return decimal.Zero
	}
	rate := &payload.TranCny{}
	err = json.Unmarshal(resBytes, &rate)
	if err != nil {
		logrus.Error(fmt.Sprintf("update rate err:%+v,resBytes:%+v", err, string(resBytes)))
		return decimal.Zero
	}
	return rate.UsdToCny
}
