package cron

import (
	"fmt"
	"runtime"
	"sync"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/cachekey"
	"futures-asset/cache/cachelock"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/match"
	"futures-asset/pkg/redislib"
	"futures-asset/service/coupling"
	"futures-asset/service/isolation"
	"futures-asset/util"
	"futures-asset/util/modelutil"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// StartTrialAssetData 体验金定时生效, 失效及回收处理定时任务
func StartTrialAssetData() {
	defer func() {
		if err := recover(); err != nil {
			logrus.Error(fmt.Sprintf("cron StartTrialAssetData failed,err:%v", err))
			go StartTrialAssetData()
		}
	}()
	ticker := time.NewTicker(time.Second * 5)
	for range ticker.C {
		trialAssetAll()
		runtime.Gosched()
	}
}

func trialAssetAll() {
	trialMutex := redislib.NewMutex(domain.MutexSwapCronTrial, 30*time.Second)
	if trialMutex.LockOnce() != nil {
		logrus.Error(fmt.Sprintf("trialAssetAll NewMutex err."))
		return
	}
	defer trialMutex.Unlock()

	// 获取所有用户
	usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	users, err := redislib.Redis().HGetAll(usersKey)
	if err != nil {
		logrus.Error("trialAsset All HGetAll:", usersKey, "error:", err)
		return
	}
	var wg sync.WaitGroup
	for uid := range users {
		checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: uid}}
		if swapcache.UserBursting(checkBurst) {
			continue // 爆仓中不处理体验金
		}
		// 获取资产只读这里不改，也不需要太精确，只是看下体验金资产
		userCache := swapcache.NewPosCache(swapcache.CacheParam{
			TradeCommon: repository.TradeCommon{
				Quote: domain.CurrencyUSDT,
			},
		}, uid)
		userAsset, err := userCache.Load()
		if err != nil {
			logrus.Info(fmt.Sprintf("trial AssetAll get asset err.userCache:%+v", userCache))
			return
		}
		// 看体验金资产是不是有，如果有去 定时生效 失效等操作
		if len(userAsset.TrialDetail) > 0 {
			wg.Add(1)
			go trialAssetTimingPro(&wg, userCache, userAsset)
		}
	}
	wg.Wait()
}

func trialAssetTimingPro(_wg *sync.WaitGroup, userCache *swapcache.PosCache, assetInfo *repository.AssetSwap) {
	// todo 体验金逻辑需要修改 等产品确认需求
	defer _wg.Done()

	// 爆仓中直接return 啥也不用做
	checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: assetInfo.UID}}
	if swapcache.UserBursting(checkBurst) {
		return
	}
	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+assetInfo.UID, 30*time.Second)
	if userMutex.Lock() != nil {
		logrus.Error(fmt.Sprintf("trialAssetTimingPro user lock err.:%s", assetInfo.UID))
		return
	}
	defer userMutex.Unlock()

	var changeTrial []*entity.TrialAsset      // 体验金修改 同步数据库
	var mqTrialAsset []*repository.MqCmsAsset // 通知财务记账list
	var billSync []repository.BillAssetSync   // 记账
	var receiveTimes []string                 // 回收通知
	logAssetSync := modelutil.NewLogAssetSync(assetInfo, userCache.Quote, time.Now().UnixNano())
	nowTime := time.Now().Unix()
	for index := 0; index < len(assetInfo.TrialDetail); index++ {
		trialStatus := assetInfo.TrialDetail[index].TrialAssetStatus
		trialEffectiveTime := assetInfo.TrialDetail[index].EffectiveTime
		trialWarningTime := assetInfo.TrialDetail[index].WarningTime
		trialInvalidTime := assetInfo.TrialDetail[index].InvalidTime

		trial := assetInfo.TrialDetail[index]

		// 体验金剩余数量为0, 更新状态为完全使用
		if trial.OpenAmount.IsZero() && trial.LockAmount.IsZero() && trial.RecycleAmount.LessThanOrEqual(decimal.Zero) {
			assetInfo.TrialDetail[index].TrialAssetStatus = domain.TrialAssetStatusFinish
			assetInfo.TrialDetail[index].StatusTime = nowTime
			assetInfo.TrialDetail[index].OpType = domain.BillTrialAssetRecycle
			assetInfo.TrialDetail[index].Amount = decimal.Zero

			changeTrial = append(changeTrial, assetInfo.TrialDetail[index])

			// 回收数量为0，这个地方财务不用收到会不处理，体验金回收的时候需要做个标记
			mqTrial := modelutil.NewTrialMqCmsAsset(assetInfo.TrialDetail[index])
			mqTrialAsset = append(mqTrialAsset, mqTrial)

			assetInfo.TrialDetail = append(assetInfo.TrialDetail[0:index], assetInfo.TrialDetail[index+1:]...)
			continue
		}

		// >= 失效时间, 并且没有开仓, 回收体验金
		if trialStatus < domain.TrialAssetStatusInvalid && ((nowTime >= trialInvalidTime && trial.OpenAmount.IsZero()) || (nowTime >= trial.StatusTime && trial.StatusTime > 0 && trial.RecycleAmount.IsPositive())) {
			recycleAmount := assetInfo.TrialDetail[index].RecycleAmount
			if recycleAmount.IsZero() && trial.RecoveryAmount.IsPositive() {
				recycleAmount = trial.RecoveryAmount
			} else {
				assetInfo.TrialDetail[index].RecycleAmount = recycleAmount
				assetInfo.TrialDetail[index].StatusTime = nowTime
			}

			// 如果存在委托单 -> 撤销并等待下次定时任务回收
			if assetInfo.HasFrozen("") {
				go match.Service.ConditionCancel(assetInfo.UID, "", "", 0, 0, 0, int32(domain.CancelTypeTrialRecycle), 1)
				continue
			}

			// totalIsolatedMargin := userCache.HoldCostTotalIsolated("")
			// 如果账户余额(包含体验金余额) - 所有逐仓仓位保证金 < 要回收金额 -> 进行平部分逐仓操作
			// if assetInfo.Balance.Add(userAsset.TrialAsset.TrialBalance).Sub(totalIsolatedMargin).LessThan(recycleAmount) {
			//	if totalIsolatedMargin.IsPositive() {
			//		// 如果逐仓仓位保证金大于0 取仓位保证金最大的逐仓仓位
			//		maxMarginPos, err := userCache.GetMaxMarginIsolatedPos()
			//		if err != nil {
			//			logrus.Error(fmt.Sprintf("get maxMaginPos err.%+v,userCache:%+v,recycleAmount:%+v", err, userCache, recycleAmount))
			//			break
			//		}
			//		if !maxMarginPos.IsolatedMargin.IsPositive() {
			//			logrus.Error(fmt.Sprintf("pos is zero maxMarginPos.%+v,userCache:%+v,recycleAmount:%+v", maxMarginPos, userCache, recycleAmount))
			//			break
			//		}
			//		reAmount := recycleAmount.Sub(userAsset.Balance.Add(userAsset.TrialAsset.TrialBalance).Sub(totalIsolatedMargin))
			//		recyclePos := reAmount.Div(maxMarginPos.IsolatedMargin).Mul(maxMarginPos.Pos) // 获取需要回收的仓位
			//
			//		// 获取合约币对最小下单数量及精度
			//		base, quote := util.BaseQuote(maxMarginPos.ContractCode)
			//		pairInfo, err := setting.Service.GetPairInfo(base, quote)
			//		if err != nil {
			//			logrus.Errorf("trialAssetTimingPro get contract pair base:%s quote:%s setting err:%s", base, quote, err)
			//		} else {
			//			// 根据交易币数量精度进位截取需要平的仓位数
			//			recyclePos, _ = util.RoundCeil(recyclePos, pairInfo.AmountPrecision)
			//			// 如果小于最小下单仓位, 则使用最小下单仓位
			//			if recyclePos.LessThan(pairInfo.MinAmount) {
			//				recyclePos = pairInfo.MinAmount
			//			}
			//		}
			//		// 需要回收的仓位 大于持有的仓位，全部回收
			//		if recyclePos.GreaterThan(maxMarginPos.Pos) {
			//			recyclePos = maxMarginPos.Pos
			//		}
			//		param := match.ClosePositionParams{
			//			PositionParams: match.PositionParams{
			//				Side:         domain.Sell,
			//				Offset:       domain.Close,
			//				OrderType:    match.OrderTypeLimit,
			//				Amount:       recyclePos,
			//				TriggerPrice: decimal.Zero,
			//			},
			//			UID:          uid,
			//			AccountType:     match.AccountTypeSwap,
			//			Base:            base,
			//			Quote:           quote,
			//			Leverage:        maxMarginPos.Leverage,
			//			Platform:        match.PlatformSystem,
			//			LiquidationType: int(domain.LiquidationTypeTrialReduce),
			//			MarginMode:      int(domain.MarginModeIsolated),
			//			Depth:           5,
			//		}
			//		if maxMarginPos.PosSide == domain.LongPos {
			//			param.Side = domain.Sell
			//		} else {
			//			param.Side = domain.Buy
			//		}
			//		go func() {
			//			resp, _ := match.Service.ClosePositions(&param)
			//			logrus.Infof("call contract-engine close place returned %+v _params:%+v", resp, param)
			//		}()
			//		break
			//	}
			//
			// 所有账户余额全部是体验金  且没有逐仓仓位，把所有余额回收
			//	recycleAmount = userAsset.Balance.Add(userAsset.TrialAsset.TrialBalance)
			// }

			// 更新体验金总余额
			// userAsset.TrialAsset.TrialBalance = userAsset.TrialAsset.TrialBalance.Sub(recycleAmount)

			// 构建账单记录及通知财务的数据
			tryAssetRecycle := repository.BillAssetSync{
				BillAsset: entity.BillAsset{
					UID:         assetInfo.UID,
					OperateId:   assetInfo.TrialDetail[index].AwardOpId,
					BillId:      util.GenerateId(),
					Currency:    assetInfo.TrialDetail[index].Currency,
					BillType:    domain.BillTrialAssetRecycle,
					Amount:      recycleAmount.Neg(),
					OperateTime: time.Now().UnixNano(),
					RecycleOpId: assetInfo.TrialDetail[index].RecycleOpId,
				},
			}
			billSync = append(billSync, tryAssetRecycle)
			assetInfo.TrialDetail[index].Amount = recycleAmount.Neg()
			assetInfo.TrialDetail[index].TrialAssetStatus = domain.TrialAssetStatusInvalid
			assetInfo.TrialDetail[index].OpType = domain.BillTrialAssetRecycle
			mqTrial := modelutil.NewTrialMqCmsAsset(assetInfo.TrialDetail[index])
			mqTrialAsset = append(mqTrialAsset, mqTrial)
			// 构建回收通知数据
			assetInfo.TrialDetail[index].RecycleAmount = decimal.Zero
			changeTrial = append(changeTrial, assetInfo.TrialDetail[index])
			receiveTime := util.SecondLayout(assetInfo.TrialDetail[index].AwardTime, util.EnumSecond)
			assetInfo.TrialDetail = append(assetInfo.TrialDetail[0:index], assetInfo.TrialDetail[index+1:]...)
			receiveTimes = append(receiveTimes, receiveTime)
			continue

		}

		// >= 预警时间
		if trialStatus < domain.TrialAssetStatusWarn {
			if nowTime >= trialWarningTime {
				assetInfo.TrialDetail[index].TrialAssetStatus = domain.TrialAssetStatusWarn
				assetInfo.TrialDetail[index].OpType = 0 // 不是新增 opType 要重置
				go func() {
					// 预警及构建体验金变动需要同步数据库的记录
					if len(assetInfo.TrialDetail) > index && assetInfo.TrialDetail[index] != nil {
						receiveTime := util.SecondLayout(assetInfo.TrialDetail[index].AwardTime, util.EnumSecond)
						invalidTime := util.SecondLayout(assetInfo.TrialDetail[index].InvalidTime, util.EnumSecond)
						util.MailSmsTrialWarnRecycle(assetInfo.UID, receiveTime, invalidTime, domain.MSTrialWarning)
					}
				}()

				changeTrial = append(changeTrial, assetInfo.TrialDetail[index])
				continue
			}
		}

		// >= 生效时间
		if trialStatus < domain.TrialAssetStatusEffect && nowTime >= trialEffectiveTime {
			assetInfo.TrialDetail[index].TrialAssetStatus = domain.TrialAssetStatusEffect
			assetInfo.TrialDetail[index].OpType = 0 // 不是新增 opType 要重置
			changeTrial = append(changeTrial, assetInfo.TrialDetail[index])
			continue
		}
	}

	// 同步体验金修改的记录到数据库
	if len(changeTrial) > 0 {
		for _, change := range changeTrial {
			logrus.Info(0, "=========", change.UID, "trial change", fmt.Sprintf("%+v", change))
		}

		// logAssetSync.AssetSwap.Balance = assetInfo.Balance.Add(userAsset.TrialAsset.TrialBalance)
		err := userCache.UpdateTrialAsset(*assetInfo)
		if err != nil {
			logrus.Info(fmt.Sprintf("trial Asset Timing Pro err.userCache:%+v,userAsset:%+v", userCache, assetInfo))
			return
		}
		go func() {
			for _, recTime := range receiveTimes {
				go util.MailSmsTrialWarnRecycle(assetInfo.UID, recTime, "", domain.MSTrialRecycle)
			}
			redis := redislib.Redis()
			isolation.AddAssetLogs(redis, logAssetSync)
			coupling.AddTrialChange(redis, changeTrial)
			coupling.AddAssetLogs(redis, mqTrialAsset...)
			coupling.AddBills(redis, billSync...)
		}()
	}
}
