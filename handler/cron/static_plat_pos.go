package cron

import (
	"runtime/debug"
	"sync"
	"time"

	"futures-asset/conf"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/setting"

	"github.com/sirupsen/logrus"
)

// StartStaticPlatPos 统计平台持仓
func StartStaticPlatPos(contractS map[string]setting.ContractPair) {
	var err error
	defer func() {
		if err := recover(); err != nil {
			logrus.Errorf("StartStaticPlatPos err:%+v", err)
			if conf.Conf.Biz.Debug {
				debug.PrintStack()
			}
			go StartStaticPlatPos(contractS)
		}
	}()
	pushCount := 0 // 统计推送次数
	ticker := time.NewTicker(10 * time.Second)
	wg := new(sync.WaitGroup)
	for range ticker.C {
		pushCount += 1
		if pushCount >= 60 { // 推送60次重新获取配置， 币对状态修改后最多 10 分钟生效
			contractS, err = setting.Service.GetAllPairSettingInfo()
			if err != nil {
				logrus.Errorln("StartStaticPlatPos GetAllPairSettingInfo error:", err)
				continue
			}
			pushCount = 0
		}
		for contractCode, contractConfig := range contractS {
			// 如果币对被禁用不做持仓推送
			if err != nil || contractConfig.State == domain.ContractDisable {
				continue
			}
			// 进行持仓推送
			wg.Add(1)
			go repository.StaticPlatPos(contractCode, wg)
		}
		wg.Wait()
	}
}
