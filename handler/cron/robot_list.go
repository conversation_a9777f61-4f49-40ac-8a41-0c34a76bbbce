package cron

import (
	"fmt"
	"futures-asset/internal/domain"
	"futures-asset/pkg/rdmap"
	"futures-asset/pkg/user"
	"time"

	"github.com/sirupsen/logrus"
)

func init() {
	domain.RobotUsers = rdmap.NewRdMap()
}

func StartRobotList() {
	defer func() {
		if err := recover(); err != nil {
			logrus.Error(fmt.Sprintf("cron get robot list failed,err:%v", err))
			go StartRobotList()
		}
	}()

	UpdateRobotList()
	ticker := time.NewTicker(time.Minute * 5)
	for range ticker.C {
		UpdateRobotList()
	}
}

func UpdateRobotList() {
	userData := user.Service.RobotList()
	for _, v := range userData {
		if !domain.RobotUsers.HasKey(v.UID) {
			domain.RobotUsers.Set(v.UID, nil)
		}
	}
}
