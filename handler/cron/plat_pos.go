package cron

import (
	"encoding/json"
	"fmt"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/message"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"

	"github.com/robfig/cron"
	"github.com/sirupsen/logrus"
)

// StartPlatPosPush 后台推送最新仓位
func StartPlatPosPush() {
	defer func() {
		if err := recover(); err != nil {
			logrus.Error(fmt.Sprintf("cron plat pos push failed,err:%v", err))
			go StartPlatPosPush()
		}
	}()
	c := cron.New()
	// 每分钟起一次定时任务
	_ = c.AddFunc("*/2 * * * * ?", pushTotalPos) // 前台推送 每2秒1次
	c.Start()
}

func pushTotalPos() {
	platData, err := redislib.Redis().HGetAll(domain.PlatHoldPos)
	if err != nil {
		logrus.Error("PlatPosList get Plat Hold Pos err.")
		return
	}
	_amqp, err := mqlib.New()
	if err != nil {
		return
	}

	for contractCode, platPosStr := range platData {
		platPosDetail := repository.PlatPosDetail{}
		err = json.Unmarshal([]byte(platPosStr), &platPosDetail)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal platPosStr err:%+v, platPosStr:%s", err, platPosStr))
			continue
		}
		message.New("", message.AssetQueueIndex, _amqp).TopicTotalPos(message.SwapAccount, contractCode).Push(map[string]interface{}{
			"contractCode": contractCode,
			"totalPos":     platPosDetail.TotalPos,
		})
	}

	_ = _amqp.Close()
}
