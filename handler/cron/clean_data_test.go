package cron

import (
	"log"
	"testing"

	"futures-asset/conf"
	"futures-asset/pkg/sqllib"
)

func TestMain(m *testing.M) {
	// init config
	conf.Init("futures-asset", "../../conf")

	// init logger
	loglib.InitLog(conf.Conf.Log.LogDir, conf.Conf.Log.SaveTime, conf.Conf.Log.LogReduce)

	// init db
	dbconf := &sqllib.Config{
		Host:    conf.Conf.DB.Host,
		Port:    conf.Conf.DB.Port,
		User:    conf.Conf.DB.User,
		Pwd:     conf.Conf.DB.Pwd,
		DBName:  conf.Conf.DB.Name,
		Prefix:  conf.Conf.DB.Prefix,
		LogMode: conf.Conf.Biz.Debug,
	}
	if err := sqllib.Init(dbconf); err != nil {
		log.Fatalf("init mysql err: " + err.<PERSON><PERSON>r())
	}
	m.<PERSON>()
}
