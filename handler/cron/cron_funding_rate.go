package cron

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/cachekey"
	"futures-asset/cache/cachelock"
	"futures-asset/cache/price"
	"futures-asset/cache/sharedcache"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/es"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/message"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"futures-asset/pkg/sqllib"
	"futures-asset/service/coupling"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/robfig/cron"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// StartFundRateData 更新资金费率
func StartFundRateData() {
	defer func() {
		if err := recover(); err != nil {
			logrus.Error(fmt.Sprintf("cron StartFundRateData failed,err:%v", err))
			go StartFundRateData()
		}
	}()

	c := cron.New()
	// 每分钟起一次定时任务
	_ = c.AddFunc("0 * * * * ?", makeFundRates)
	// _ = c.AddFunc("0/15 * * * * ?", makeFundRates) // 每15秒起一次定时任务
	c.Start()
}

func makeFundRates() {
	fundRateMutex := redislib.NewMutex(domain.MutexSwapCronFund, 30*time.Second)
	if fundRateMutex.LockOnce() != nil {
		logrus.Error(fmt.Sprintf("profitStatic NewMutex err."))
		return
	}
	defer fundRateMutex.Unlock()

	contractSettings, err := setting.Service.GetAllPairSettingInfo()
	if err != nil {
		logrus.Error("makeFundRates get all contract config err.")
		return
	}
	for contractCode, contractConfig := range contractSettings {
		go makeFundRate(contractCode, contractConfig)
	}
}

func makeFundRate(contractCode string, contractConfig setting.ContractPair) {
	indexPrice := sharedcache.IndexPrice(contractCode)
	fundRateListKey := cachekey.GetFundRateRedisKey(contractCode)
	swapcache.SetOneFundRate(fundRateListKey, contractCode, indexPrice, contractConfig.Interest)
	_ = swapcache.PremiumIndex(contractCode, contractConfig)
	fundRate := swapcache.FundRate(contractCode, contractConfig)
	nowTime := time.Now()

	go message.New("", message.AssetQueueIndex, mqlib.CommonAmqp).
		TopicFundRate(message.SwapAccount, contractCode).
		Push(map[string]interface{}{
			"contractCode": contractCode,
			"fundRate":     fundRate,
			"currentTime":  time.Now().Unix(),
		})

	// 每天 0，8，16 点结算
	if nowTime.Hour()%8 == 0 {
		if nowTime.Minute() == 0 {
			logFundRate := entity.LogFundingRate{
				Base:        contractConfig.Base,
				Quote:       contractConfig.Quote,
				FundRate:    fundRate,
				MarkPrice:   pCache.GetMarkPrice(contractCode),
				IndexPrice:  indexPrice,
				OperateTime: nowTime.Unix(),
			}

			logrus.Info(fmt.Sprintf("Settlement All.fundRate:%+v,indexPrice:%+v,indexPrice:%+v.time:%+v", fundRate, indexPrice, contractCode, nowTime.Unix()))
			if !fundRate.IsZero() {
				SettlementAll(fundRate, contractCode, contractConfig)
			}

			err := logFundRate.Insert(nil)
			fmt.Println("=========================", logFundRate.Base, logFundRate.Quote, "logFundRate.Insert", err)
			if err != nil {
				logrus.Error(fmt.Sprintf("insert fund rate log err:%+v, logFundRate:%+v", err, logFundRate))
			}
		}
	}
}

// SettlementAll 资金费率为正 净多出资金费用  资金费用为负 净空 出资金费用
func SettlementAll(fundRate decimal.Decimal, contractCode string, contractConfig setting.ContractPair, pCache *price.PCache) {
	usersKey := domain.UserPosBase.Key(contractCode)
	users, err := redislib.Redis().HGetAll(usersKey)
	if err != nil {
		logrus.Error("usersKey HGetAll", usersKey, "error:", err)
		return
	}
	logrus.Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundRate))
	// 出资金费率的用户
	fundCostOutUsers := make([]repository.SettlementPos, 0)
	// 收资金费率的用户
	fundCostInUsers := make([]repository.SettlementPos, 0)

	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
	totalOutPos := decimal.Zero
	totalInPos := decimal.Zero
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero
	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
			continue
		}
		checkBurst := cachelock.BurstLockParams{
			ContractCode: userPosSwap.ContractCode,
			Liquidation: cache.LiquidationInfo{
				UID:        userPosSwap.UID,
				MarginMode: domain.MarginMode(userPosSwap.MarginMode),
			},
		}
		if swapcache.UserBursting(checkBurst) {
			logrus.Info(fmt.Sprintf("user in burst.userPos:%+v", userPosSwap))
			continue
		}

		base, quote := util.BaseQuote(userPosSwap.ContractCode)
		settlementPos := repository.SettlementPos{
			UserHoldPos: userPosSwap,
			Base:        base,
			Quote:       quote,
		}

		// 单向持仓需要特殊处理
		if !userPosSwap.BothPos.IsZero() && userPosSwap.BothTrialMargin.LessThanOrEqual(decimal.Zero) {
			settlementPos.PosSide = domain.BothPos
			settlementPos.Pos = userPosSwap.BothPos.Abs()
			if userPosSwap.BothPos.IsPositive() {
				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
				processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			} else if userPosSwap.BothPos.IsNegative() {
				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
				processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			}
		} else {
			// Isolated true 是逐仓 逐仓和全仓处理方式不一样
			if domain.MarginMode(userPosSwap.MarginMode) == domain.MarginModeIsolated {
				if userPosSwap.LongPos.IsPositive() && userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
					settlementPos.PosSide = domain.LongPos
					settlementPos.Pos = userPosSwap.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
				if userPosSwap.ShortPos.IsPositive() && userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
					settlementPos.PosSide = domain.ShortPos
					settlementPos.Pos = userPosSwap.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
			} else {
				realPos := decimal.Zero
				longPos := decimal.Zero
				shortPos := decimal.Zero
				if userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
					longPos = userPosSwap.LongPos
				}
				if userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
					shortPos = userPosSwap.ShortPos
				}
				realPos = longPos.Sub(shortPos)
				settlementPos.Pos = realPos.Abs()
				if realPos.IsPositive() {
					settlementPos.PosSide = domain.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				} else if realPos.IsNegative() {
					settlementPos.PosSide = domain.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
			}
		}
	}

	// 体验金仓位计算
	trialFundCostOutUsers, trialFundCostInUsers, trialTotalOutPos, trialTotalInPos, trialTotalLongPos, trialTotalShortPos := settlementTrialPos(fundRate, contractCode, contractConfig)
	if len(trialFundCostOutUsers) > 0 {
		fundCostOutUsers = append(fundCostOutUsers, trialFundCostOutUsers...)
	}
	if len(trialFundCostInUsers) > 0 {
		fundCostInUsers = append(fundCostInUsers, trialFundCostInUsers...)
	}
	totalOutPos = totalOutPos.Add(trialTotalOutPos)
	totalInPos = totalInPos.Add(trialTotalInPos)
	totalLongPos = totalLongPos.Add(trialTotalLongPos)
	totalShortPos = totalShortPos.Add(trialTotalShortPos)

	if !totalInPos.Equal(totalOutPos) {
		logrus.Info(fmt.Sprintf("%s pos static err.totalInPos != totalOutPos.totalInPos:%+v, totalOutPos:%+v", contractCode, totalInPos, totalOutPos))
		logrus.Info(fmt.Sprintf("******* %s totalLongPos：%+v totalShortPos:%+v", contractCode, totalLongPos, totalShortPos))
		logrus.Info(fmt.Sprintf("******* %s fundCostOutUsers:%+v", contractCode, fundCostOutUsers))
		logrus.Info(fmt.Sprintf("******* %s fundCostInUsers:%+v", contractCode, fundCostInUsers))
	}

	db, err := sqllib.Db()
	if err != nil {
		logrus.Error(fmt.Sprintf("get db err:%+v", err))
	}
	// 总共实收资金费用
	totalRealGet := decimal.Zero
	for _, userPos := range fundCostOutUsers {
		if userPos.IsTrial {
			// 体验金仓位收取资金费率
			totalRealGet = totalRealGet.Add(PtGetTrialFundCost(&userPos, fundRate, pCache, db))
		} else {
			// 真实仓位收取资金费率
			totalRealGet = totalRealGet.Add(PtGetFundCost(&userPos, fundRate, pCache, db))
		}
	}

	logrus.Info(fmt.Sprintf("Pt put fund cost.totalRealGet: %+v,totalInPos: %+v", totalRealGet, totalInPos))
	for _, userPos := range fundCostInUsers {
		if userPos.IsTrial {
			// 体验金仓位发放资金费率
			PtPutTrialFundCost(&userPos, fundRate, totalRealGet, totalInPos, pCache, db)
		} else {
			// 真实仓位收发放金费率
			PtPutFundCost(&userPos, fundRate, totalRealGet, totalInPos, pCache, db)
		}
	}
}

func settlementTrialPos(fundRate decimal.Decimal, contractCode string, contractConfig setting.ContractPair) ([]repository.SettlementPos, []repository.SettlementPos, decimal.Decimal, decimal.Decimal, decimal.Decimal, decimal.Decimal) {
	// 出资金费率的用户
	fundCostOutUsers := make([]repository.SettlementPos, 0)
	// 收资金费率的用户
	fundCostInUsers := make([]repository.SettlementPos, 0)

	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
	totalOutPos := decimal.Zero
	totalInPos := decimal.Zero
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero

	usersKey := domain.UserTrialPosBase.Key(contractCode)
	users, err := redislib.Redis().HGetAll(usersKey)
	if err != nil {
		logrus.Error("userTrialKey HGetAll", usersKey, "error:", err)
		return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
	}
	logrus.Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundRate))

	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
			continue
		}
		checkBurst := cachelock.BurstLockParams{
			ContractCode: userPosSwap.ContractCode,
			Liquidation: cache.LiquidationInfo{
				UID:        userPosSwap.UID,
				MarginMode: domain.MarginMode(userPosSwap.MarginMode),
				IsTrialPos: true,
			},
		}
		if swapcache.UserBursting(checkBurst) {
			logrus.Info(fmt.Sprintf("user in burst.userPos:%+v", userPosSwap))
			continue
		}

		base, quote := util.BaseQuote(userPosSwap.ContractCode)
		settlementPos := repository.SettlementPos{
			UserHoldPos: userPosSwap,
			Base:        base,
			Quote:       quote,
			IsTrial:     true,
		}

		// 因为全仓不能开保证金仓位,所直接略过
		// 单向持仓需要特殊处理
		if !userPosSwap.BothPos.IsZero() {
			settlementPos.PosSide = domain.BothPos
			settlementPos.Pos = userPosSwap.BothPos.Abs()
			if userPosSwap.BothPos.IsPositive() {
				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
				processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			} else if userPosSwap.BothPos.IsNegative() {
				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
				processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			}
		} else {
			if domain.MarginMode(userPosSwap.MarginMode) == domain.MarginModeIsolated {
				if userPosSwap.LongPos.IsPositive() {
					settlementPos.PosSide = domain.LongPos
					settlementPos.Pos = userPosSwap.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
				if userPosSwap.ShortPos.IsPositive() {
					settlementPos.PosSide = domain.ShortPos
					settlementPos.Pos = userPosSwap.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
			}
		}
	}

	return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
}

func processLongPos(fundCostOutUsers, fundCostInUsers *[]repository.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *repository.SettlementPos) {
	if fundRate.IsPositive() {
		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
	} else {
		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
		*totalInPos = totalInPos.Add(settlementPos.Pos)
	}
}

func processShortPos(fundCostOutUsers, fundCostInUsers *[]repository.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *repository.SettlementPos) {
	if fundRate.IsPositive() {
		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
		*totalInPos = totalInPos.Add(settlementPos.Pos)
	} else {
		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
	}
}

// PtGetFundCost 用户支付资金费用给平台, 资金费用取绝对值进位
// 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
func PtGetFundCost(tmpUserPos *repository.SettlementPos, fundRate decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
	// settleId := util.GenerateId() // TODO snowflake ID
	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs()
	fundCost, _ = util.RoundCeil(fundCost, domain.CurrencyPrecision)
	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, 30*time.Second)
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtGetFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  tmpUserPos.Base,
			Quote: tmpUserPos.Quote,
		},
	}, tmpUserPos.UID)
	userAsset, err := userCache.Load()
	if err != nil {
		logrus.Info(fmt.Sprintf("PtGetFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}

	bParamList := make([]payload.BalanceUpdate, 0)
	bIsolatedParamList := make([]payload.BalanceUpdate, 0)
	isTrialPos := false

	if tmpUserPos.MarginMode == 0 {
		if tmpUserPos.IsTrial {
			switch tmpUserPos.PosSide {
			case domain.LongPos:
				tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
			case domain.ShortPos:
				tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
			case domain.BothPos:
				tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
			default:
				return decimal.Zero
			}
		} else {
			switch tmpUserPos.PosSide {
			case domain.LongPos:
				tmpUserPos.MarginMode = userAsset.LongPos.MarginMode
				isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)
			case domain.ShortPos:
				tmpUserPos.MarginMode = userAsset.ShortPos.MarginMode
				isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)
			case domain.BothPos:
				tmpUserPos.MarginMode = userAsset.BothPos.MarginMode
				isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)
			default:
				return decimal.Zero
			}
		}
	}

	if isTrialPos {
		return decimal.Zero
	}

	switch domain.MarginMode(tmpUserPos.MarginMode) {
	case domain.MarginModeCross:
		// 获取quote 总资产折合 假如qBalance < fundCost, fundCost修改
		rate := decimal.NewFromInt(1)
		qBalance := userAsset.CBalance(tmpUserPos.Quote)
		holdMargin, _, _, _, err := userCache.TotalCrossMaintainMargin(pCache, tmpUserPos.ContractCode)
		if err != nil {
			logrus.Info(fmt.Sprintf("PtGetFundCost TotalCrossMaintainMargin error: %v", err))
			return decimal.Zero
		}
		if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
			rate = pCache.SpotRate(tmpUserPos.Quote, domain.CurrencyUSDT)
			if rate.LessThanOrEqual(decimal.Zero) {
				logrus.Info(fmt.Sprintln("PtGetFundCost rate error:", rate))
				return decimal.Zero
			}
			uBalance, err := userAsset.TotalJoinBalance(pCache)
			if err != nil {
				logrus.Info(fmt.Sprintf("PtGetFundCost TotalBalance err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v, err:%v", userCache, tmpUserPos, fundRate, pCache, err))
				return decimal.Zero
			}
			uCost := fundCost.Mul(rate)
			if uBalance.GreaterThan(uCost.Add(holdMargin)) {
				totalCollect := uCost
				bParam := userCache.FundingBalanceParam(settleId, domain.CurrencyUSDT, totalCollect.Neg())
				bParamList = append(bParamList, bParam)

				// for _, currency := range domain.CurrencyList {
				//	if totalCollect.LessThanOrEqual(decimal.Zero) {
				//		continue
				//	}
				//	cBalance := userAsset.CBalance(currency)
				//	if cBalance.GreaterThan(decimal.Zero) {
				//		cRate := pCache.SpotURate(currency)
				//		if cRate.GreaterThan(decimal.Zero) {
				//			if cBalance.Mul(cRate).GreaterThanOrEqual(totalCollect) {
				//				bParam := userCache.FundingBalanceParam(settleId, currency, cBalance.Mul(cRate).Neg())
				//				bParamList = append(bParamList, bParam)
				//			} else {
				//				bParam := userCache.FundingBalanceParam(settleId, currency, cBalance.Neg())
				//				bParamList = append(bParamList, bParam)
				//				totalCollect = totalCollect.Sub(cBalance.Mul(cRate))
				//			}
				//		} else {
				//			log.Println("PtGetFundCost", currency, "rate is zero")
				//			continue
				//		}
				//	}
				// }
			}

		} else {
			if qBalance.GreaterThan(fundCost.Add(holdMargin)) {
				bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
				bParamList = append(bParamList, bParam)
			}
		}

	case domain.MarginModeIsolated:
		bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			posValue := userAsset.LongPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.LongPos.ContractCode, userAsset.LongPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.LongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Sub(fundCost)
				if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}

		case domain.ShortPos:
			posValue := userAsset.ShortPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.ShortPos.ContractCode, userAsset.ShortPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.ShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Sub(fundCost)
				if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		
		case domain.BothPos:
			posValue := userAsset.BothPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.BothPos.ContractCode, userAsset.BothPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.BothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Sub(fundCost)
				if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		default:
			logrus.Info(fmt.Sprintf("PtGetFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
			return decimal.Zero

		}

	default:
		logrus.Info(fmt.Sprintf("PtGetFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero

	}

	if len(bParamList) > 0 {
		for _, param := range bParamList {
			if !param.Amount.IsZero() {
				balanceRes := userCache.BalanceAdd(param, userAsset, pCache, true)

				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
					balanceRes.BillAssetLogs[i].FundRate = fundRate
				}

				err = userCache.UpdatePosAndAsset(userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
					return decimal.Zero
				}
				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
				log.Println("PtGetFundCost InsertLogFunding", userAsset.UID, fundRate)
				go func() {
					// 推送 资金费用 账单
					redis := redislib.Redis()
					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				}()
				// 账本统计
				go func(p payload.BalanceUpdate) {
					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
					if err != nil {
						logrus.Errorf("PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
						return
					}
					if p.Amount.IsPositive() {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
					} else {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
					}
				}(param)
			}
		}
	}

	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if !param.Amount.IsZero() {
				balanceRes := payload.BalanceRes{
					AssetLogs:     make([]*repository.MqCmsAsset, 0),
					BillAssetLogs: make([]repository.BillAssetSync, 0),
				}
				userCache.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
					balanceRes.BillAssetLogs[i].FundRate = fundRate
				}

				err = userCache.UpdatePosAndAsset(userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
					return decimal.Zero
				}
				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)

				log.Println("PtGetFundCost InsertLogFunding", userAsset.UID, fundRate)

				go func() {
					// 推送 资金费用 账单
					redis := redislib.Redis()
					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				}()
				// 账本统计
				go func(p payload.BalanceUpdate) {
					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
					if err != nil {
						logrus.Errorf("2 PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
						return
					}
					if p.Amount.IsPositive() {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
					} else {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
					}
				}(param)
			}
		}
	}

	return fundCost
}

// PtPutFundCost 平台支付资金费用给用户 资金费用取绝对值截取
func PtPutFundCost(tmpUserPos *repository.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
	settleId := util.GenerateId()
	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs().Truncate(domain.CurrencyPrecision)
	if !totalInPos.IsZero() {
		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(domain.CurrencyPrecision)
		// 有可能实际收取的不够，所以需要按比例重新计算下
		if receivableCost.Cmp(fundCost) < 0 {
			logrus.Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
			fundCost = receivableCost
		}
	}
	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, 30*time.Second)
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtPutFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  tmpUserPos.Base,
			Quote: tmpUserPos.Quote,
		},
	}, tmpUserPos.UID)
	userAsset, err := userCache.Load()
	if err != nil {
		logrus.Info(fmt.Sprintf("PtPutFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}

	isTrialPos := false

	switch tmpUserPos.PosSide {
	case domain.LongPos:
		isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)

	case domain.ShortPos:
		isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)

	case domain.BothPos:
		isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)

	default:
		return decimal.Zero

	}

	if isTrialPos {
		return decimal.Zero
	}

	log.Println("PtPutFundCost", "UID", userAsset.UID, "settleId", settleId, "Quote", userCache.Quote, "fundCost", fundCost)

	bParamList := make([]payload.BalanceUpdate, 0)
	bIsolatedParamList := make([]payload.BalanceUpdate, 0)
	bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost)
	if domain.MarginMode(tmpUserPos.MarginMode) == domain.MarginModeIsolated {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(fundCost)
			if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.ShortPos:
			userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(fundCost)
			if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.BothPos:
			userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(fundCost)
			if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		default:
			logrus.Info(fmt.Sprintf("PtPutFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
			return decimal.Zero

		}
		// userAsset.Used = userAsset.Used.Add(fundCost)
	} else {
		bParamList = append(bParamList, bParam)
	}

	logrus.Info(fmt.Sprintf("PtPutFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
	if len(bParamList) > 0 {
		for _, param := range bParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := userCache.BalanceAdd(param, userAsset, pCache, true)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
				balanceRes.BillAssetLogs[i].FundRate = fundRate
			}
			err = userCache.UpdatePosAndAsset(userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
					log.Println("PtPutFundCost InsertLogFunding", userAsset.UID, fundRate)
				}
			}
			go func() {
				// 推送 资金费用 账单
				redis := redislib.Redis()
				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			}()
			// 账本统计
			go func(p payload.BalanceUpdate) {
				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
				if err != nil {
					logrus.Errorf("PtPutFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
					return
				}
				if p.Amount.IsPositive() {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				} else {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				}
			}(param)
		}
	}
	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := payload.BalanceRes{
				AssetLogs:     make([]*repository.MqCmsAsset, 0),
				BillAssetLogs: make([]repository.BillAssetSync, 0),
			}
			userCache.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
				balanceRes.BillAssetLogs[i].FundRate = fundRate
			}
			err = userCache.UpdatePosAndAsset(userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
					log.Println("PtPutFundCost InsertLogFunding", userAsset.UID, fundRate)
				}
			}
			go func() {
				// 推送 资金费用 账单
				redis := redislib.Redis()
				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			}()
			go func(p payload.BalanceUpdate) {
				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
				if err != nil {
					logrus.Errorf("2 PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
					return
				}
				if p.Amount.IsPositive() {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				} else {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				}
			}(param)
		}
	}

	return fundCost
}

// PtGetTrialFundCost 体验金用户支付资金费用给平台, 资金费用取绝对值进位
// 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
func PtGetTrialFundCost(tmpUserPos *repository.SettlementPos, fundRate decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
	settleId := util.GenerateId()
	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs()
	fundCost, _ = util.RoundCeil(fundCost, domain.CurrencyPrecision)
	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, 30*time.Second)
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtGetTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  tmpUserPos.Base,
			Quote: tmpUserPos.Quote,
		},
	}, tmpUserPos.UID)
	userAsset, err := userCache.Load()
	if err != nil {
		logrus.Info(fmt.Sprintf("PtGetTrialFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}

	bIsolatedParamList := make([]payload.BalanceUpdate, 0)

	if tmpUserPos.MarginMode == 0 {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
		case domain.ShortPos:
			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
		case domain.BothPos:
			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
		default:
			return decimal.Zero
		}
	}

	switch domain.MarginMode(tmpUserPos.MarginMode) {
	case domain.MarginModeIsolated:
		bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			posValue := userAsset.TrialLongPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialLongPos.ContractCode, userAsset.TrialLongPos.UID, "PtGetTrialFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialLongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		case domain.ShortPos:
			posValue := userAsset.TrialShortPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialShortPos.ContractCode, userAsset.TrialShortPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}

		case domain.BothPos:
			posValue := userAsset.TrialBothPos.CalcPosValue(pCache)
			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialBothPos.ContractCode, userAsset.TrialBothPos.UID, "PtGetTrialFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialBothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				log.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}

		default:
			logrus.Info(fmt.Sprintf("PtGetTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
			return decimal.Zero

		}

	default:
		logrus.Info(fmt.Sprintf("PtGetTrialFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero

	}

	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if !param.Amount.IsZero() {
				balanceRes := payload.BalanceRes{
					AssetLogs:     make([]*repository.MqCmsAsset, 0),
					BillAssetLogs: make([]repository.BillAssetSync, 0),
				}
				userCache.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
					balanceRes.BillAssetLogs[i].FundRate = fundRate
				}

				err = userCache.UpdateTrialPos(userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
					return decimal.Zero
				}
				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)

				log.Println("PtGetTrialFundCost InsertLogFunding", userAsset.UID, fundRate)

				go func() {
					// 推送 资金费用 账单
					redis := redislib.Redis()
					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				}()
				// 账本记录
				go func(p payload.BalanceUpdate) {
					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
					if err != nil {
						logrus.Errorf("PtGetTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
						return
					}
					if param.Amount.IsPositive() {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
					} else {
						es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
					}
				}(param)
			}
		}
	}

	return fundCost
}

// PtPutTrialFundCost 平台支付资金费用给体验金用户 资金费用取绝对值截取
func PtPutTrialFundCost(tmpUserPos *repository.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
	settleId := util.GenerateId()
	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs().Truncate(domain.CurrencyPrecision)
	if !totalInPos.IsZero() {
		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(domain.CurrencyPrecision)
		// 有可能实际收取的不够，所以需要按比例重新计算下
		if receivableCost.Cmp(fundCost) < 0 {
			logrus.Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
			fundCost = receivableCost
		}
	}
	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, 30*time.Second)
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtPutTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  tmpUserPos.Base,
			Quote: tmpUserPos.Quote,
		},
	}, tmpUserPos.UID)
	userAsset, err := userCache.Load()
	if err != nil {
		logrus.Info(fmt.Sprintf("PtPutTrialFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
		return decimal.Zero
	}

	if tmpUserPos.MarginMode == 0 {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
		case domain.ShortPos:
			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
		case domain.BothPos:
			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
		default:
			return decimal.Zero
		}
	}

	log.Println("PtPutTrialFundCost", "UID", userAsset.UID, "settleId", settleId, "Quote", userCache.Quote, "fundCost", fundCost)

	bIsolatedParamList := make([]payload.BalanceUpdate, 0)
	bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost)
	if domain.MarginMode(tmpUserPos.MarginMode) == domain.MarginModeIsolated {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.ShortPos:
			userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.BothPos:
			userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		default:
			logrus.Info(fmt.Sprintf("PtPutTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
			return decimal.Zero
		}
	}

	logrus.Info(fmt.Sprintf("PtPutTrialFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := payload.BalanceRes{
				AssetLogs:     make([]*repository.MqCmsAsset, 0),
				BillAssetLogs: make([]repository.BillAssetSync, 0),
			}
			userCache.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
				balanceRes.BillAssetLogs[i].FundRate = fundRate
			}
			err = userCache.UpdateTrialPos(userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
					log.Println("PtPutTrialFundCost InsertLogFunding", userAsset.UID, fundRate)
				}
			}
			go func() {
				// 推送 资金费用 账单
				redis := redislib.Redis()
				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			}()
			// 账本统计
			go func(p payload.BalanceUpdate) {
				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UID, p.Currency)
				if err != nil {
					logrus.Errorf("PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
					return
				}
				if p.Amount.IsPositive() {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				} else {
					es.SaveLedgerDetail(userAsset.UID, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				}
			}(param)
		}
	}
	return fundCost
}

func InsertLogFunding(tmpUserPos *repository.SettlementPos, mqAsset repository.MqCmsAsset, userAsset *repository.AssetSwap,
	fundRate, markPrice decimal.Decimal, db *gorm.DB,
) {
	logFunding := entity.LogFundingFee{
		FundingId:    mqAsset.OrderId,
		UID:          tmpUserPos.UID,
		Base:         tmpUserPos.Base,
		Quote:        tmpUserPos.Quote,
		ContractCode: tmpUserPos.ContractCode,
		FundRate:     fundRate,
		MarkPrice:    markPrice,
		Amount:       mqAsset.Amount,
		FundPosNum:   tmpUserPos.Pos,
		MarginMode:   int(tmpUserPos.MarginMode),
		OperateTime:  mqAsset.OperateTime,
		PosSide:      int(tmpUserPos.PosSide),
	}
	if logFunding.Amount.IsPositive() {
		logFunding.Direction = domain.Income
	} else {
		logFunding.Direction = domain.Outlay
	}
	lFundPos := entity.FundingFeePos{}
	sFundPos := entity.FundingFeePos{}
	bFundPos := entity.FundingFeePos{}
	if tmpUserPos.LongPos.IsPositive() {
		lFundPos.PosSide = int(domain.LongPos)
		lFundPos.Pos = tmpUserPos.LongPos
		lFundPos.OpenPriceAvg = tmpUserPos.LongPriceAvg.Truncate(domain.CurrencyPrecision)
		lFundPos.Liquidation = userAsset.LongPos.Liquidation
		lFundPos.Margin = tmpUserPos.LongIsolatedMargin
		lFundPos.OpenTime = tmpUserPos.LongOpenTime / 1e9
		lFundPos.PosId = tmpUserPos.LongPosId
	}
	if tmpUserPos.ShortPos.IsPositive() {
		sFundPos.PosSide = int(domain.ShortPos)
		sFundPos.Pos = tmpUserPos.ShortPos
		sFundPos.OpenPriceAvg = tmpUserPos.ShortPriceAvg.Truncate(domain.CurrencyPrecision)
		sFundPos.Liquidation = userAsset.ShortPos.Liquidation
		sFundPos.Margin = tmpUserPos.ShortIsolatedMargin
		sFundPos.OpenTime = tmpUserPos.ShortOpenTime / 1e9
		sFundPos.PosId = tmpUserPos.ShortPosId
	}
	if !tmpUserPos.BothPos.IsZero() {
		bFundPos.PosSide = int(domain.BothPos)
		bFundPos.Pos = tmpUserPos.BothPos.Abs()
		bFundPos.OpenPriceAvg = tmpUserPos.BothPriceAvg.Truncate(domain.CurrencyPrecision)
		bFundPos.Liquidation = userAsset.BothPos.Liquidation
		bFundPos.Margin = tmpUserPos.BothIsolatedMargin
		bFundPos.OpenTime = tmpUserPos.BothOpenTime / 1e9
		bFundPos.PosId = tmpUserPos.BothPosId
	}

	switch tmpUserPos.PosSide {
	case domain.LongPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = int(userAsset.TrialLongPos.UserType)
			logFunding.AccountType = userAsset.TrialLongPos.AccountType
			logFunding.Leverage = userAsset.TrialLongPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = int(userAsset.LongPos.UserType)
			logFunding.AccountType = userAsset.LongPos.AccountType
			logFunding.Leverage = userAsset.LongPos.Leverage
		}

	case domain.ShortPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = int(userAsset.TrialShortPos.UserType)
			logFunding.AccountType = userAsset.TrialShortPos.AccountType
			logFunding.Leverage = userAsset.ShortPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = int(userAsset.ShortPos.UserType)
			logFunding.AccountType = userAsset.ShortPos.AccountType
			logFunding.Leverage = userAsset.ShortPos.Leverage
		}

	case domain.BothPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = int(userAsset.TrialBothPos.UserType)
			logFunding.AccountType = userAsset.TrialBothPos.AccountType
			logFunding.Leverage = userAsset.TrialBothPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = int(userAsset.BothPos.UserType)
			logFunding.AccountType = userAsset.BothPos.AccountType
			logFunding.Leverage = userAsset.BothPos.Leverage
		}

	default:

	}

	if tmpUserPos.MarginMode == int32(domain.MarginModeIsolated) {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			logFunding.PosData = append(logFunding.PosData, lFundPos)

		case domain.ShortPos:
			logFunding.PosData = append(logFunding.PosData, sFundPos)

		case domain.BothPos:
			logFunding.PosData = append(logFunding.PosData, bFundPos)

		default:

		}
	} else {
		lever := decimal.NewFromInt(int64(logFunding.Leverage))
		if lFundPos.Pos.IsPositive() {
			if lFundPos.Margin.IsZero() && lever.IsPositive() {
				lFundPos.Margin = lFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, lFundPos)
		}
		if sFundPos.Pos.IsPositive() {
			if sFundPos.Margin.IsZero() && lever.IsPositive() {
				sFundPos.Margin = sFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, sFundPos)
		}
		if !bFundPos.Pos.IsZero() {
			if bFundPos.Margin.IsZero() && lever.IsPositive() {
				bFundPos.Margin = bFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, bFundPos)
		}
	}

	posB, _ := json.Marshal(logFunding.PosData)
	logFunding.StrPos = string(posB)

	err := logFunding.Insert(db)
	if err != nil {
		logrus.Error(fmt.Sprintf("log funding insert err:%+v", err))
	}
}
