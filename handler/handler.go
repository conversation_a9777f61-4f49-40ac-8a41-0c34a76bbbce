package handler

import (
	"context"
	"encoding/json"
	"strconv"
	"sync"

	"futures-asset/conf"
	"futures-asset/handler/cron"
	"futures-asset/handler/monitor"
	"futures-asset/pkg/safely"
	"futures-asset/pkg/setting"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"

	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

func StartMonitors(ctx context.Context, wg *sync.WaitGroup) {
	go cron.StartCnyRate()
	go cron.StartRobotList()

	if conf.Conf.Biz.Monitor != 1 {
		return
	}

	contractSettings, err := setting.Service.GetAllPairSettingInfo()
	if err != nil {
		logrus.Panic("StartMonitors get all contract config err.")
		return
	}
	for contractCode := range contractSettings {
		if contractCode != "" {
			wg.Add(1)
			go monitor.SyncPosDb(ctx, wg, contractCode)
		}
	}

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go monitor.SyncAssetDb(ctx, wg, strconv.Itoa(i))
	}

	wg.Add(8)
	go monitor.SyncBillAsset(ctx, wg)
	go monitor.SyncProfitLoss(ctx, wg)
	go monitor.SyncTrialAssetDb(ctx, wg)
	go monitor.BurstMonitor(ctx, wg)
	go monitor.SyncRobotBillAsset(ctx, wg)
	go monitor.SyncUserWinRate(ctx, wg)
	go monitor.SyncUserStatistics(ctx, wg)
	go monitor.SyncOptionBill(ctx, wg)

	go monitor.ContractMqPublishAsset()
	go monitor.PublishTrade()
	go cron.StartFundRateData()
	go cron.StartProfitStaticData()
	go cron.StartSyncOptionProfitData()
	go cron.StartTrialAssetData()
	go cron.StartPlatPosPush()
	safely.Run(cron.StartCleanData)

	go cron.StartStaticPlatPos(contractSettings)
	go monitor.SubscribePair(ctx, wg)

	go cron.StartUpdateAccountPrincipal()
}

// 根据topic名称解析不同类型的消息数据
func MessageQueueHandler(
	session sarama.ConsumerGroupSession,
	claim sarama.ConsumerGroupClaim,
	msg *sarama.ConsumerMessage,
) {
	defer func() {
		if err := recover(); err != nil {
			logrus.WithFields(logrus.Fields{
				"topic":     msg.Topic,
				"partition": msg.Partition,
				"offset":    msg.Offset,
				"error":     err,
			}).Error("MessageQueueHandler panic recovered")
		}
	}()

	logrus.WithFields(logrus.Fields{
		"topic":     msg.Topic,
		"partition": msg.Partition,
		"offset":    msg.Offset,
		"key":       string(msg.Key),
	}).Info("MessageQueueHandler received message")

	// 根据topic名称确定消息类型并解析
	var messageData interface{}
	var err error

	switch msg.Topic {
	case "futures-settle-trade-account":
		// 账户结算消息
		var data futuresEnginePB.AccountSettleEngine
		err = json.Unmarshal(msg.Value, &data)
		messageData = &data

	case "futures-settle-order-cancel":
		// 订单取消消息
		var data futuresEnginePB.Order
		err = json.Unmarshal(msg.Value, &data)
		messageData = &data

	default:
		// 未知的topic类型
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"message":   string(msg.Value),
		}).Error("MessageQueueHandler unknown topic type")
		return
	}

	if err != nil {
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"error":     err,
			"message":   string(msg.Value),
		}).Error("MessageQueueHandler failed to unmarshal message")
		return
	}

	// 处理解析后的消息数据
	processMessageData(msg.Topic, messageData)

	logrus.WithFields(logrus.Fields{
		"topic":     msg.Topic,
		"partition": msg.Partition,
		"offset":    msg.Offset,
	}).Info("MessageQueueHandler processed successfully")
}

// processMessageData 处理不同类型的消息数据
func processMessageData(topic string, data interface{}) {
	switch topic {
	case "futures-settle-trade-account":
		if accountData, ok := data.(*futuresEnginePB.AccountSettleEngine); ok {
			logrus.WithFields(logrus.Fields{
				"topic": topic,
				"data":  accountData,
			}).Info("Processing account settle message")
			// TODO: 添加账户结算的具体处理逻辑
		}

	case "futures-settle-order-cancel":
		if orderData, ok := data.(*futuresEnginePB.Order); ok {
			logrus.WithFields(logrus.Fields{
				"topic": topic,
				"data":  orderData,
			}).Info("Processing order cancel message")
			// TODO: 添加订单取消的具体业务处理逻辑
		}

	default:
		logrus.WithFields(logrus.Fields{
			"topic": topic,
		}).Warn("No specific handler for this topic")
	}
}
