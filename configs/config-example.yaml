env:
  serviceName: "gateway"
  log:
    pretty: true
    level: info
    path: ./logs/log
    maxAge: 72h
    rotationTime: 1h
  debug: true

http:
  port: 8080
  timeouts:
    readTimeout: 2s
    readHeaderTimeout: 2s
    writeTimeout: 10s
    idleTimeout: 5s

observability:
  pyroscope:
    enable: true
    url: "http://pyroscope"
  otel:
    enable: true
    host: 127.0.0.1
    port: 4317
    isSecure: false
    exporter: otlp

db:
  logLevel: warn
  master:
    host: 127.0.0.1
    port: 3306
    username: username
    password: password
    name:
    maxIdleConns: 10
    maxOpenConns: 200
    connMaxLifetime: 10s

redis:
  host: 127.0.0.1
  port: 6379
  username:
  password:
  dialTimeout: 5s
  readTimeout: 3s
  writeTimeout: 3s
  poolSize: 10
  minIdleConns: 2
  maxIdleConns: 10
  connMaxIdleTime: 30s

elasticsearch:
  username:
  password:
  maxIdleConnsPerHost: 50
  dialerTimeout: 30s
  idleConnTimeout: 1h
  TLSHandshakeTimeout: 10s
  responseHeaderTimeout: 30s
  expectContinueTimeout: 1s
  routineSize: 3
  routineChanSize: 10
  url:
    - http://127.0.0.1:9200

kafka:
  clientID: gateway
  brokers:
    - 127.0.0.1:19093
    - 127.0.0.1:19094
    - 127.0.0.1:19095
  sasl:
    enable: false
    user: user
    password: 1234
  channelBufferSize: 256
  producer:
    topic:
      device: notification-device
      login: notification-login
      eventTrack: risk-event-transaction
      eventAlarm: risk-event-alarm
      eventMetric: risk-event

customConfig:
  csrf:
    enable: false
    secret: 123456
    appVersion: 1.0.0

  auth:
    id: 1
    secret: 123456

  token:
    expires: 1000s
    tickExpires: 900s

  setCookie:
    sameSite: 4 # 1=default 2=lax 3=strict 4=none

  cors:
    accessControlAllowCredentials: true

  withdrawAmountLimitToday: 10000

  memberService:
    url: member.domain
    token:
  permissionService:
    url: permission.domain
    token:
  notificationService:
    url: notification.domain
    token:
  c2cService:
    url: c2c.domain
    token:
  orderService:
    url: order.domain
    token:
  settingService:
    url: setting.domain
    token:
  walletService:
    url: wallet.domain
    token:
  cryptoQuoteService:
    url: cryptoQuote.domain
    token:
  kycService:
    url: kyc.domain
    token:
  articleService:
    url: article.domain
    token:
  auditLogService:
    url: audit-log.domain
    token:
  approvalService:
    url: approval.domain
    token:
  commissionService:
    url: commission.domain
    token:
  orchidService:
    url: orchid.domain
    token:
  missionService:
    url: mission.domain
    token:
  lotusFingerService:
    url: lotusFinger.domain
    token:
  campaignService:
    url: campaign.domain
    token:

  httpClientTransportConfig:
    idleConnTimeout: 90 # 這裡會乘上 time.Second
    tLSHandshakeTimeout: 30 # 這裡會乘上 time.Second
    expectContinueTimeout: 1 # 這裡會乘上 time.Second
    responseHeaderTimeout: 30 # 這裡會乘上 time.Second
    maxIdleConns: 5000
    maxIdleConnsPerHost: 10000
    maxConnsPerHost: 0
    netDialerTimeOut: 60 # 這裡會乘上 time.Second
    netDialerKeepAlive: 60 # 這裡會乘上 time.Second

  workerPool:
    size: 5

  aliyunOSS:
    regionID: cn-hongkong
    accessKey: wuZPMwm96eg9Bd9H85OAckUZwM9ffhTDNb90Ozm4JvU=
    secretKey: qSNe7ugiqaM1dYAYymc/igVpbK4pGmOz3TvTtp5PWeA=
    roleArn: acs:ram::5162957890343066:role/cnx-oss
    roleSessionName: cnx-oss

  klineVersion: v1 #k線版本 v1:postgre v3:MySQL
