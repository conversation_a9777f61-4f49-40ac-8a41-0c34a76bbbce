{"--是否本机开启定时监控任务": "", "IsMonitor": 1, "debug": true, "host": "0.0.0.0", "port": "5101", "rpcport": "5100", "db": {"host": "**************", "port": "3307", "user": "root", "pwd": "kaRBJPuz6oVQ", "name": "contract_wallet", "prefix": ""}, "redis": {"addrs": ["*************:6379", "*************:6380", "**************:6379", "**************:6380", "*************:6379", "*************:6380"], "prefix": ""}, "spotredis": {"addrs": ["*************:6379", "*************:6380", "**************:6379", "**************:6380", "*************:6379", "*************:6380"], "prefix": ""}, "es": {"addrs": ["http://**************:9200", "http://*************:9200", "http://***********:9200"]}, "mq": {"host": "***********", "port": "5672", "user": "admin", "pwd": "gT4ofpPpo7dA3rF6881A"}, "log": {"log_dir": "./futures-asset/logs/wallet.log", "save_time": 30, "log_reduce": false}, "--burst server": "", "burstCoinPairs": ["btc-usdt", "eth-usdt"], "--inner server": "", "server": {"setting": "***********:1103", "matchdata": "***********:3303", "spotmatchdata": "***********:3303", "wallet": "***********:3103", "user": "***********:8103", "otc": "***********:8303", "cms_oprate": "***********:6101", "cms_asset": "***********:6301", "contractwallet": "***********:5101", "contractengine": "***********:5201", "contractdata": "***********:5301", "inquiry": "***********:6701"}}