package isolation

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"futures-asset/cache/price"
	"futures-asset/cache/swapcache"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/match"
	"futures-asset/pkg/redislib"
	"futures-asset/service/persist"
	"futures-asset/util"
	"futures-asset/util/modelutil"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

type Batch struct {
	req *payload.LockParam
}

func NewBatch(_req *payload.LockParam) *Batch {
	return &Batch{req: _req}
}

func (slf *Batch) UnLockAsset(orders []payload.LockParam) (domain.Code, []payload.BatchUnlockItem, error) {
	// req user
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 15*time.Second)
	if mutex.Lock() != nil {
		return domain.Code251101, nil, domain.ErrLockPos
	}
	defer mutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return domain.Code252404, nil, err
	}

	pCache := price.New()
	/*

		{
		UID:********
		Base:eth
		Quote:usdt
		AccountType:swap
		OperateTime:1686215518423961995
		Orders:[
		{
		UID: UserType:0 OrderId:10886728767014174720
		Base: Quote:
		OrderType:10
		Price:1833.3 Amount:0.2 AccountType:
		Side:1
		Offset:1
		OperateTime:0
		Leverage:20
		MarginMode:1
		UnfrozenMargin:18.333
		IsInnerCall:0 IsErr:false
		PositionMode:0
		IsLimitOrder:1 LiquidationType:0
		}]
		}
	*/
	posSides := make(map[int32]bool)

	leverage := asset.GetLeverage(slf.req.ContractCode())
	userIntLeverage := 0
	switch asset.PositionMode {
	case domain.HoldModeBoth:
		userIntLeverage = leverage.BLeverage

	default:
		switch leverage.MarginMode {
		case domain.MarginModeCross:
			userIntLeverage = leverage.Leverage

		case domain.MarginModeIsolated:
			if slf.req.PosSide == domain.Long {
				userIntLeverage = leverage.LLeverage
			} else if slf.req.PosSide == domain.Short {
				userIntLeverage = leverage.SLeverage
			}
			// if (slf.req.Side == domain.Buy && slf.req.Offset == domain.Open) ||
			// 	(slf.req.Side == domain.Sell && slf.req.Offset == domain.Close) {
			// 	userIntLeverage = leverage.LLeverage
			// } else if (slf.req.Side == domain.Sell && slf.req.Offset == domain.Open) ||
			// 	(slf.req.Side == domain.Buy && slf.req.Offset == domain.Close) {
			// 	userIntLeverage = leverage.SLeverage
			// }

		default:
			userIntLeverage = leverage.Leverage
		}

	}

	totalUnfrozen := decimal.Zero
	items := make([]payload.BatchUnlockItem, 0)
	assetSyncList := make([]*repository.LogAssetSync, 0)
	for _, order := range orders {
		replyItem := payload.BatchUnlockItem{
			OrderId: order.OrderId,
		}

		if leverage.MarginMode != domain.MarginModeNone && int32(leverage.MarginMode) != order.MarginMode {
			// 记录错误订单
			replyItem.Code = domain.Code251117
			items = append(items, replyItem)
			continue
		}
		if userIntLeverage <= 0 {
			if leverage.MarginMode == domain.MarginModeIsolated {
				if slf.req.PosSide == domain.Long {
					userIntLeverage = leverage.LLeverage
				} else if slf.req.PosSide == domain.Short {
					userIntLeverage = leverage.SLeverage
				}
				// if (order.Side == domain.Buy && order.Offset == domain.Open) ||
				// 	(order.Side == domain.Sell && order.Offset == domain.Close) {
				// 	userIntLeverage = leverage.LLeverage
				// } else if (order.Side == domain.Sell && order.Offset == domain.Open) ||
				// 	(order.Side == domain.Buy && order.Offset == domain.Close) {
				// 	userIntLeverage = leverage.SLeverage
				// }
			}
			if userIntLeverage <= 0 {
				// 记录错误订单
				logrus.Info(0, "UnLockAsset userIntLeverage", userIntLeverage, fmt.Sprintf("%+v", leverage))
				replyItem.Code = domain.Code251114
				items = append(items, replyItem)
				continue
			}
		}
		if userIntLeverage != order.Leverage {
			// 记录错误订单
			replyItem.Code = domain.Code251115
			items = append(items, replyItem)
			continue
		}

		// 双向持仓平仓需要自行计算解冻保证金
		if asset.PositionMode == domain.HoldModeBoth && order.IsClose() && order.UnfrozenMargin.LessThanOrEqual(decimal.Zero) {
			order.UnfrozenMargin, _ = util.RoundCeil(order.Amount.Mul(order.Price).Div(decimal.NewFromInt32(int32(userIntLeverage))), domain.CurrencyPrecision)
		}
		// 按照统一币对批量修改(注: 此处slf.req.ContractCode()不可使用order.ContractCode()替代, 因为撮合给的orders中所有ContractCode都为空)
		logrus.Info(0, "================ Batch.UnLockAsset [DecrFrozen]", order.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "order", fmt.Sprintf("%+v", order), "order.UnfrozenMargin", order.UnfrozenMargin)
		// asset.DecrFrozen(util.PosSide(order.Side, order.Offset, int32(asset.PositionMode)), slf.req.ContractCode(), order.UnfrozenMargin, len(order.AwardOpIds) > 0)
		asset.DecrFrozen(slf.req.PosSide, slf.req.ContractCode(), order.UnfrozenMargin, len(order.AwardOpIds) > 0)
		// posSide := util.RealPosSide(order.Side, order.Offset, int32(asset.PositionMode))
		posSide := slf.req.PosSide
		if len(order.AwardOpIds) > 0 { // 解冻体验金, 处理体验金
			trialList := asset.TrialDetail.Gets(order.AwardOpIds...)
			if trialList.GetLockAmount().LessThanOrEqual(order.UnfrozenMargin) {
				trialList.UnlockAmount(order.UnfrozenMargin)
			}

			if order.TrialIsEnd {
				trialList.AddOpenAmount(order.UnfrozenMargin)

				pos := asset.GetPos(posSide, true)
				pos.UpdateTrialMargin(order.UnfrozenMargin)
				pos.Liquidation = pos.CalcLiquidationPrice(pCache, asset.TrialLongPos, asset.TrialShortPos, asset.TrialBothPos, decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero)
				asset.SetPos(posSide, pos, true)
				posSides[posSide] = true
			}

			// 每次体验金都要更新
			if posSide == domain.LongPos {
				err = userCache.UpdateTrialLongPos(asset)
			} else if posSide == domain.ShortPos {
				err = userCache.UpdateTrialShortPos(asset)
			} else {
				err = userCache.UpdateTrialBothPos(asset)
			}

			if err != nil {
				logrus.Error(0, "Batch.UnLockAsset UpdateTrialPos err", err)
				return domain.Code252405, nil, err
			}
			go func() {
				redis := redislib.Redis()
				if err := redis.LPush(domain.SyncTrialAssetSwap, lo.ToAnySlice(trialList)...); err != nil {
					logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,swapTrialAsset:%+v", err, trialList))
				}
			}()
		}
		logrus.Info(0, "================ Batch.UnLockAsset [DecrFrozen]", order.UID, "order.UnfrozenMargin", order.UnfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

		replyItem.Code = http.StatusOK
		items = append(items, replyItem)

		totalUnfrozen = totalUnfrozen.Add(order.UnfrozenMargin)
		assetSync := modelutil.NewLogAssetSync(asset, slf.req.Quote, slf.req.OperateTime)
		assetSyncList = append(assetSyncList, assetSync)
	}

	err = userCache.LockOrUnlock(asset)
	if err != nil {
		return domain.Code252405, nil, err
	}

	redis := redislib.Redis()
	go AddAssetLogs(redis, assetSyncList...) // wallet资产异步存库

	// 如果存在体验金更新仓位保证金
	if len(posSides) > 0 {
		go func() {
			_redis := redislib.Redis()
			if posSides[domain.LongPos] {
				pos := userCache.NewLogPosSync(asset.TrialLongPos, slf.req.OperateTime, "", slf.req.OrderId,
					slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
				if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
					logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
				}
			}
			if posSides[domain.ShortPos] {
				pos := userCache.NewLogPosSync(asset.TrialShortPos, slf.req.OperateTime, "", slf.req.OrderId,
					slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
				if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
					logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
				}
			}
			if posSides[domain.BothPos] {
				pos := userCache.NewLogPosSync(asset.TrialBothPos, slf.req.OperateTime, "", slf.req.OrderId,
					slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
				if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
					logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
				}
			}
		}()
	}

	return http.StatusOK, items, nil
}

func (slf *Batch) LockAsset(req *payload.BatchLock) (domain.Code, []payload.BatchLockItem, error) {
	// req user
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+req.UID, 30*time.Second)
	if mutex.Lock() != nil {
		return domain.Code251101, nil, errors.New("lock user err")
	}
	defer mutex.Unlock()
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  req.Base,
			Quote: req.Quote,
		},
	}, req.UID)

	asset, err := userCache.Load()
	if err != nil {
		return domain.Code252404, nil, err
	}

	// 当用户体验金余额大于0时, 禁止其使用API下单
	trialBalance := asset.TrialCBalance(req.Quote)
	if asset.AssetMode == domain.AssetMode {
		pCache := price.New()
		trialBalance, err = asset.TotalJoinTrialBalance(pCache)
		if err != nil {
			return domain.Code252404, nil, err
		}
	}
	if strings.ToUpper(req.Platform) == match.PlatformApi &&
		trialBalance.Sign() > 0 {
		return domain.Code250008, nil, errors.New("limit user api order by trial")
	}

	contractCode := util.ContractCode(req.Base, req.Quote)
	leverage := asset.GetLeverage(contractCode)

	userIntLeverage := 0
	switch asset.PositionMode {
	case domain.HoldModeBoth:
		userIntLeverage = leverage.BLeverage

	default:
		switch leverage.MarginMode {
		case domain.MarginModeCross:
			userIntLeverage = leverage.Leverage

		case domain.MarginModeIsolated:
			if slf.req.PosSide == domain.Long {
				userIntLeverage = leverage.LLeverage
			} else if slf.req.PosSide == domain.Short {
				userIntLeverage = leverage.SLeverage
			}
			// if (slf.req.Side == domain.Buy && slf.req.Offset == domain.Open) ||
			// 	(slf.req.Side == domain.Sell && slf.req.Offset == domain.Close) {
			// 	userIntLeverage = leverage.LLeverage
			// } else if (slf.req.Side == domain.Sell && slf.req.Offset == domain.Open) ||
			// 	(slf.req.Side == domain.Buy && slf.req.Offset == domain.Close) {
			// 	userIntLeverage = leverage.SLeverage
			// }

		default:
			userIntLeverage = leverage.Leverage
		}

	}

	if userIntLeverage <= 0 {
		logrus.Info(0, "LockAsset userIntLeverage", userIntLeverage, fmt.Sprintf("%+v", leverage))
		return domain.Code251114, nil, errors.New("get user leverage err")
	}

	if leverage.MarginMode != domain.MarginModeNone && leverage.MarginMode != req.MarginMode {
		return domain.Code251117, nil, errors.New("marginMode not equal")
	}

	maxPosValue, err := swapcache.GetMaxPosValueWithLeverage(req.Base, req.Quote, userIntLeverage)
	if err != nil {
		return domain.ErrGetAllCfg, nil, errors.New(domain.ErrMsg[domain.ErrGetAllCfg])
	}
	if maxPosValue.IsZero() {
		return domain.ErrMaxPos, nil, errors.New(domain.ErrMsg[domain.ErrMaxPos])
	}
	redis := redislib.Redis()
	items := make([]payload.BatchLockItem, 0)
	syncAssetList := make([]*repository.LogAssetSync, 0)
	pCache := price.New()
	for _, order := range req.Orders {
		replyItem := payload.BatchLockItem{
			OrderId:      order.OrderId,
			Amount:       decimal.Zero,
			FrozenMargin: decimal.Zero,
		}

		if order.Leverage != userIntLeverage {
			// 记录错误订单
			replyItem.Code = domain.Code251115
			items = append(items, replyItem)
			continue
		}

		if maxPosValue.IsPositive() {
			if maxPosValue.LessThan(asset.ShortPos.CalcPosValue(pCache).Add(asset.LongPos.CalcPosValue(pCache)).Add(order.Amount.Mul(order.Price))) {
				replyItem.Code = domain.ErrMaxPos
				items = append(items, replyItem)
				continue
			}
		}

		// 可用 = 全仓保证金余额 - 全仓仓位保证金 - 全仓冻结保证金 - 逐仓冻结保证金
		crossMarginBalance, err := userCache.CrossMarginBalance(asset, pCache)
		if err != nil {
			log.Println("LockAsset CrossMarginBalance error:", err)
			return domain.Code252407, items, err
		}
		avail := crossMarginBalance.Sub(userCache.HoldCostTotalCross(pCache, asset.AssetMode, req.Quote)).
			Sub(userCache.FrozenTotal(asset, req.Quote))
		if avail.Sign() <= 0 {
			replyItem.Code = domain.Code252002
			items = append(items, replyItem)
			continue
		}

		if asset.PositionMode == domain.HoldModeBoth && req.IsClose() {
			switch order.Side {
			case domain.Buy:
				if asset.BothPos.Pos.GreaterThanOrEqual(decimal.Zero) {
					replyItem.Code = domain.Code251110
					items = append(items, replyItem)
					continue
				}

			case domain.Sell:
				if asset.BothPos.Pos.LessThanOrEqual(decimal.Zero) {
					replyItem.Code = domain.Code251111
					items = append(items, replyItem)
					continue
				}

			default:

			}
		}

		userLeverage := decimal.NewFromInt(int64(userIntLeverage))
		originFrozen, _ := util.RoundCeil(order.Amount.Mul(order.Price).Div(userLeverage), domain.CurrencyPrecision)
		diff := avail.Sub(originFrozen)
		if asset.AssetMode == domain.AssetMode {
			// 联合保证金 需要转成 usdt
			rate := pCache.SpotURate(slf.req.Quote)
			if rate.IsZero() {
				log.Printf("avail:%s  slf.req.Leverage:%d rate:%s slf.req.Amount:%s", avail, leverage.BLeverage, rate, slf.req.Amount)
				replyItem.Code = domain.Code250009
				items = append(items, replyItem)
				continue
			}
			usdtFrozen, _ := util.RoundCeil(originFrozen.Mul(rate), domain.CurrencyPrecision)
			diff = avail.Sub(usdtFrozen)
		}
		if diff.Sign() < 0 {
			replyItem.Code = domain.Code252002
			items = append(items, replyItem)
			continue
		}
		if avail.Mul(userLeverage).Div(order.Price).LessThan(order.Amount) {
			log.Printf("avail:%s  req.Leverage:%d req.Amount:%s", avail, userLeverage, order.Amount)
			replyItem.Code = domain.Code250005
			items = append(items, replyItem)
			continue
		}
		logrus.Info(0, "================ Batch.LockAsset [IncrFrozen]", slf.req.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "slf.req", fmt.Sprintf("%+v", slf.req))
		asset.IncrFrozen(req.PosSide, contractCode, originFrozen)
		logrus.Info(0, "================ Batch.LockAsset [IncrFrozen]", slf.req.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))
		replyItem.Code = http.StatusOK
		replyItem.Amount = order.Amount
		replyItem.FrozenMargin = originFrozen
		haveTrial := 0
		if trialBalance.GreaterThan(decimal.Zero) {
			haveTrial = 1
		}
		replyItem.HaveTrial = haveTrial
		items = append(items, replyItem)

		// log.Printf("req:%s, req value:%s req.Amount:%s req.Price:%s", toFrozen, toFrozen.Mul(userLeverage), order.Amount, order.Price)
		assetLog := modelutil.NewLogAssetSync(asset, req.Quote, req.OperateTime)
		syncAssetList = append(syncAssetList, assetLog)
	}

	err = userCache.LockOrUnlock(asset)
	if err != nil {
		return domain.Code252405, nil, err
	}

	go AddAssetLogs(redis, syncAssetList...) // wallet资产异步存库

	return http.StatusOK, items, nil
}

func (slf *Batch) LockPos(req *payload.BatchLock) (domain.Code, []payload.BatchLockItem, error) {
	// req user
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+req.UID, 30*time.Second)
	if mutex.Lock() != nil {
		return domain.Code251101, nil, errors.New("lock user err")
	}
	defer mutex.Unlock()
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  req.Base,
			Quote: req.Quote,
		},
	}, req.UID)

	asset, err := userCache.Load()
	if err != nil {
		return domain.Code252404, nil, err
	}
	// log.Printf("req user %s cache asset: %+v", req.UID, asset)

	items := make([]payload.BatchLockItem, 0)
	syncPosList := make([]*repository.LogPosSync, 0)
	for _, order := range req.Orders {
		replyItem := payload.BatchLockItem{
			OrderId:      order.OrderId,
			Amount:       decimal.Zero,
			FrozenMargin: decimal.Zero,
		}

		if !req.IsClose() {
			// 记录错误订单
			replyItem.Code = domain.CodeParamInvalid
			items = append(items, replyItem)
			continue
		}

		if req.PositionMode != int32(asset.PositionMode) {
			// 记录错误订单
			replyItem.Code = domain.Code251124
			items = append(items, replyItem)
			continue
		}

		pos := asset.LongPos // 默认多仓操作
		replyItem.Code = domain.Code251111
		// 判断是否为空仓
		if req.PosSide == domain.ShortPos {
			pos = asset.ShortPos
			replyItem.Code = domain.Code251110
		}

		diffPos := pos.PosAvailable.Sub(order.Amount)
		if pos.PosAvailable.Sign() <= 0 || diffPos.Sign() < 0 {
			// 记录错误订单
			items = append(items, replyItem)
			continue
		}

		if pos.PosSide == domain.LongPos {
			asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Sub(order.Amount)
			syncPos := userCache.NewLogPosSync(asset.LongPos, req.OperateTime, "", order.OrderId,
				order.Side, req.PosSide, order.Amount, decimal.Zero)
			syncPosList = append(syncPosList, syncPos)
		}
		if pos.PosSide == domain.ShortPos {
			asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Sub(order.Amount)
			syncPos := userCache.NewLogPosSync(asset.ShortPos, req.OperateTime, "", order.OrderId,
				order.Side, req.PosSide, order.Amount, decimal.Zero)
			syncPosList = append(syncPosList, syncPos)
		}

		replyItem.Code = http.StatusOK
		haveTrial := 0
		if pos.TrialMargin.GreaterThan(decimal.Zero) {
			haveTrial = 1
		}
		replyItem.HaveTrial = haveTrial
		items = append(items, replyItem)
	}

	err = userCache.UpdatePos(asset)
	if err != nil {
		return domain.Code251120, items, err
	}

	// wallet仓位异步存库
	redis := redislib.Redis()
	go AddPosLogs(redis, syncPosList...)

	return http.StatusOK, items, nil
}

func (slf *Batch) UnlockPos(orders []payload.LockParam) (domain.Code, []payload.BatchUnlockItem, error) {
	// todo both pos unlock
	// req user
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 30*time.Second)
	if mutex.Lock() != nil {
		return domain.Code251101, nil, domain.ErrLockPos
	}
	defer mutex.Unlock()

	// 获取仓位
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return domain.Code251103, nil, err
	}

	// log.Printf("close sell req user pos: userid:%s asset:%+v", asset.UID, asset)
	items := make([]payload.BatchUnlockItem, 0)
	for _, order := range orders {
		replyItem := payload.BatchUnlockItem{
			OrderId: order.OrderId,
		}
		switch order.Side {
		case domain.Buy:
			if len(order.AwardOpIds) == 0 {
				if asset.PositionMode == domain.HoldModeBoth {
					if asset.BothPos.PosAvailable.Sub(order.Amount).LessThan(asset.BothPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.BothPos.PosAvailable = asset.BothPos.PosAvailable.Sub(order.Amount)
				} else {
					if asset.ShortPos.PosAvailable.Add(order.Amount).GreaterThan(asset.ShortPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Add(order.Amount)
				}
			} else {
				// 体验金模式下
				if asset.PositionMode == domain.HoldModeBoth {
					if asset.TrialBothPos.PosAvailable.Sub(order.Amount).LessThan(asset.TrialBothPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.TrialBothPos.PosAvailable = asset.TrialBothPos.PosAvailable.Sub(order.Amount)
				} else {
					if asset.TrialShortPos.PosAvailable.Add(order.Amount).GreaterThan(asset.TrialShortPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.TrialShortPos.PosAvailable = asset.TrialShortPos.PosAvailable.Add(order.Amount)
				}
			}

		case domain.Sell:
			if len(order.AwardOpIds) == 0 {
				if asset.PositionMode == domain.HoldModeBoth {
					if asset.BothPos.PosAvailable.Add(order.Amount).GreaterThan(asset.BothPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.BothPos.PosAvailable = asset.BothPos.PosAvailable.Add(order.Amount)
				} else {
					if asset.LongPos.PosAvailable.Add(order.Amount).GreaterThan(asset.LongPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Add(order.Amount)
				}
			} else {
				if asset.PositionMode == domain.HoldModeBoth {
					if asset.TrialBothPos.PosAvailable.Add(order.Amount).GreaterThan(asset.TrialBothPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.TrialBothPos.PosAvailable = asset.TrialBothPos.PosAvailable.Add(order.Amount)
				} else {
					if asset.TrialLongPos.PosAvailable.Add(order.Amount).GreaterThan(asset.TrialLongPos.Pos) {
						replyItem.Code = domain.Code251118
						items = append(items, replyItem)
						continue
					}
					asset.TrialLongPos.PosAvailable = asset.TrialLongPos.PosAvailable.Add(order.Amount)
				}
			}
		}

		replyItem.Code = http.StatusOK
		items = append(items, replyItem)
	}

	err = userCache.UpdateTrialPos(asset)
	if err != nil {
		return domain.Code251105, nil, err
	}
	err = userCache.UpdatePos(asset)
	if err != nil {
		return domain.Code251105, nil, err
	}

	go func() {
		// wallet资产异步存库
		pos := userCache.NewLogPosSync(asset.LongPos, slf.req.OperateTime, "", slf.req.OrderId,
			slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
		_redis := redislib.Redis()
		if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
		pos = userCache.NewLogPosSync(asset.ShortPos, slf.req.OperateTime, "", slf.req.OrderId,
			slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
		if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
		pos = userCache.NewLogPosSync(asset.TrialLongPos, slf.req.OperateTime, "", slf.req.OrderId,
			slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
		if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
		pos = userCache.NewLogPosSync(asset.TrialShortPos, slf.req.OperateTime, "", slf.req.OrderId,
			slf.req.Side, slf.req.PosSide, slf.req.Amount.Neg(), decimal.Zero)
		if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
	}()

	return http.StatusOK, items, nil
}
