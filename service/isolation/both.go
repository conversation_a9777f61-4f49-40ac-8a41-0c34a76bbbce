package isolation

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"futures-asset/cache/price"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"futures-asset/service/persist"
	"futures-asset/util"
	"futures-asset/util/modelutil"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// Both 买入平空仓操作
type Both struct {
	req *payload.LockParam
}

func (slf *Both) Lock() (payload.LockRes, error) {
	if slf.req.IsOpen() && slf.req.Price.Sign() <= 0 {
		return payload.LockRes{Code: domain.CodeParamInvalid}, errors.New("price less or equal 0")
	}
	if slf.req.PositionMode != domain.HoldModeBoth {
		return payload.LockRes{Code: domain.Code251124}, errors.New("hold mode err")
	}
	pCache := price.New()
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 30*time.Second)
	if mutex.Lock() != nil {
		return payload.LockRes{Code: domain.Code251101}, domain.ErrLockPos
	}
	defer mutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{Base: slf.req.Base, Quote: slf.req.Quote},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return payload.LockRes{Code: domain.Code252404}, err
	}

	leverage := asset.GetLeverage(slf.req.ContractCode())
	if leverage.MarginMode != domain.MarginModeNone && int32(leverage.MarginMode) != slf.req.MarginMode {
		return payload.LockRes{Code: domain.Code251117}, errors.New("marginMode not equal")
	}
	if leverage.BLeverage <= 0 {
		logrus.Info(0, "Both Lock", fmt.Sprintf("%+v", leverage))
		return payload.LockRes{Code: domain.Code251114}, errors.New("get user leverage err")
	}
	if leverage.BLeverage != slf.req.Leverage {
		return payload.LockRes{Code: domain.Code251115}, errors.New("leverage not equal")
	}
	userLeverage := decimal.NewFromInt(int64(leverage.BLeverage))
	maxPosValue, err := swapcache.GetMaxPosValueWithLeverage(slf.req.Base, slf.req.Quote, leverage.BLeverage)
	if err != nil {
		return payload.LockRes{Code: domain.ErrGetAllCfg}, errors.New(domain.ErrMsg[domain.ErrGetAllCfg])
	}
	if maxPosValue.IsZero() {
		return payload.LockRes{Code: domain.ErrMaxPos}, errors.New(domain.ErrMsg[domain.ErrMaxPos])
	}
	if maxPosValue.IsPositive() {
		frozenValue := asset.GetFrozenByCode(slf.req.ContractCode()).Mul(userLeverage)
		if maxPosValue.LessThan(asset.ShortPos.CalcPosValue(pCache).Add(asset.LongPos.CalcPosValue(pCache)).
			Add(asset.BothPos.CalcPosValue(pCache)).
			Add(slf.req.Amount.Mul(slf.req.Price)).Add(frozenValue)) {
			return payload.LockRes{Code: domain.ErrMaxPos}, errors.New(domain.ErrMsg[domain.ErrMaxPos])
		}
	}

	originFrozen := decimal.Zero
	rate := decimal.NewFromInt(1)

	if slf.req.IsClose() {
		// 验证可平仓位
		reduceCode, err := slf.ReduceLock(asset)
		if err != nil {
			return payload.LockRes{Code: reduceCode}, err
		}
	}

	available, err := slf.GetAvailable(pCache, userCache, asset)
	if err != nil {
		log.Println("both Lock error:", err)
		return payload.LockRes{Code: domain.Code252407}, err
	}

	trialBalance := asset.TrialCBalance(slf.req.Quote)
	if asset.AssetMode == domain.AssetMode {
		trialBalance, err = asset.TotalJoinTrialBalance(pCache)
		if err != nil {
			log.Println("both Lock error:", err)
			return payload.LockRes{Code: domain.Code252408}, err
		}
	}

	// 强平订单跳过资金判断
	if slf.req.LiquidationType == int(domain.LiquidationTypeNone) && available.Sign() <= 0 {
		return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenSell
	}
	originFrozen, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage), domain.CurrencyPrecision)

	if asset.AssetMode == domain.AssetMode {
		// 联合保证金 需要转成 usdt
		rate = pCache.SpotURate(slf.req.Quote)
		if rate.IsZero() {
			log.Printf("available: %s  slf.req.Leverage :%d rate :%s slf.req.Amount: %s", available, leverage.BLeverage, rate, slf.req.Amount)
			return payload.LockRes{Code: domain.Code250009}, domain.ErrInsufficientPos
		}
		usdtFrozen, _ := util.RoundCeil(originFrozen.Mul(rate), domain.CurrencyPrecision)
		diff := available.Sub(usdtFrozen)
		if slf.req.IsLimitOrder == domain.Market {
			codeSetting, err := setting.Service.GetCachePair(slf.req.Base, slf.req.Quote)
			if err != nil {
				logrus.Errorf("open sell get contract pair base: %s quote: %s setting err: %s", slf.req.Base, slf.req.Quote, err)
				return payload.LockRes{Code: domain.Code250002}, err
			}
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				minAmount := codeSetting.MinAmount
				log.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
				if available.Mul(userLeverage).LessThan(minAmount) {
					log.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s price: %s settingMinAmount: %s)",
						available, minAmount, userLeverage, slf.req.Price, codeSetting.MinAmount)
					return payload.LockRes{Code: domain.Code252002}, errors.New("place min amount limit")
				}
				slf.req.Amount = available.Mul(userLeverage).Div(rate).Div(slf.req.Price).Truncate(codeSetting.AmountPrecision)
				originFrozen, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage).Mul(rate), domain.CurrencyPrecision)
				diff = available.Sub(usdtFrozen)
			}
		} else {
			// 强平订单跳过资金判断
			if slf.req.LiquidationType == int(domain.LiquidationTypeNone) {
				if diff.Sign() < 0 {
					return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenBuy
				}
				if available.Mul(userLeverage).Div(rate).Div(slf.req.Price).LessThan(slf.req.Amount) {
					log.Printf("available: %s  slf.req.Leverage: %d slf.req.Amount: %s", available, leverage.BLeverage, slf.req.Amount)
					return payload.LockRes{Code: domain.Code250005}, domain.ErrInsufficientPos
				}
			}
		}
	} else {
		diff := available.Sub(originFrozen)
		if slf.req.IsLimitOrder == domain.Market {
			codeSetting, err := setting.Service.GetCachePair(slf.req.Base, slf.req.Quote)
			if err != nil {
				logrus.Errorf("open sell get contract pair base:%s quote:%s setting err:%s", slf.req.Base, slf.req.Quote, err)
				return payload.LockRes{Code: domain.Code250002}, err
			}
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				minAmount := codeSetting.MinAmount
				log.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
				if available.Mul(userLeverage).LessThan(minAmount) {
					log.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s price: %s settingMinAmount: %s)",
						available, minAmount, userLeverage, slf.req.Price, codeSetting.MinAmount)
					return payload.LockRes{Code: domain.Code252002}, errors.New("place min amount limit")
				}
				slf.req.Amount = available.Mul(userLeverage).Div(slf.req.Price).Truncate(codeSetting.AmountPrecision)
				originFrozen, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage), domain.CurrencyPrecision)
				diff = available.Sub(originFrozen)
			}
		} else {
			// 强平订单跳过资金判断
			if slf.req.LiquidationType == int(domain.LiquidationTypeNone) {
				if diff.Sign() < 0 {
					return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenBuy
				}
				// 强平订单跳过资金判断
				if available.Mul(userLeverage).Div(slf.req.Price).LessThan(slf.req.Amount) {
					log.Printf("available: %s  slf.req.Leverage: %d slf.req.Amount: %s", available, leverage.BLeverage, slf.req.Amount)
					return payload.LockRes{Code: domain.Code250005}, domain.ErrInsufficientPos
				}
			}
		}
	}
	asset.IncrFrozen(slf.req.PosSide, slf.req.ContractCode(), originFrozen)
	err = userCache.LockOrUnlock(asset)
	if err != nil {
		return payload.LockRes{Code: domain.Code251123}, err
	}

	pos := userCache.NewLogPosSync(asset.BothPos, slf.req.OperateTime, "", slf.req.OrderId,
		slf.req.Side, slf.req.PosSide, slf.req.Amount, decimal.Zero)
	go func() {
		// wallet资产异步存库
		if err := persist.SyncPos(redislib.Redis(), slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
	}()

	haveTrial := 0
	if trialBalance.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}
	return payload.LockRes{Code: http.StatusOK, PositionMode: asset.PositionMode, Amount: slf.req.Amount, FrozenMargin: originFrozen, JoinMarginRate: rate, HaveTrial: haveTrial}, nil
}

func (slf *Both) ReduceLock(asset *repository.AssetSwap) (domain.Code, error) {
	// 仓位为0 不能减
	if asset.BothPos.Pos.IsZero() {
		return domain.Code251125, fmt.Errorf("insufficient both pos avaiable")
	}
	// 买入 仓位 > 0 不能减;
	if slf.req.Side == domain.Buy && asset.BothPos.Pos.IsPositive() {
		return domain.Code251125, fmt.Errorf("insufficient both pos avaiable")
	}
	// 卖出 仓位 < 0 不能减
	if slf.req.Side == domain.Sell && asset.BothPos.Pos.IsNegative() {
		return domain.Code251125, fmt.Errorf("insufficient both pos avaiable")
	}
	return http.StatusOK, nil
}

func (slf *Both) GetAvailable(pCache *price.PCache, userCache *swapcache.PosCache, asset *repository.AssetSwap) (decimal.Decimal, error) {
	available := decimal.Zero
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == domain.AssetMode {
		rate = pCache.SpotURate(slf.req.Quote)
	}
	var err error = nil
	available, err = userCache.GetAvailableBase(pCache, asset, domain.MarginMode(slf.req.MarginMode), slf.req.Quote)
	if err != nil {
		log.Println("Both GetAvail GetAvailableBase error:", err)
		return decimal.Zero, err
	}

	if (asset.BothPos.Pos.IsPositive() && slf.req.Side == domain.Sell) || (asset.BothPos.Pos.IsNegative() && slf.req.Side == domain.Buy) {
		// 单向持仓反方向 需要加一个仓位实际占用的成本 [当前标的全仓持仓仓位 * 持仓均价 / 杠杆倍数]
		realCost := asset.BothPos.OpenHoldCost().Mul(rate).Abs()
		available = available.Add(realCost)
	}

	return available, nil
}

func (slf *Both) UnLock() (domain.Code, error) {
	// req user
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 15*time.Second)
	if mutex.Lock() != nil {
		return domain.Code251101, nil
	}
	defer mutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return domain.Code252404, nil
	}

	leverage := asset.GetLeverage(slf.req.ContractCode())
	if leverage.MarginMode != domain.MarginModeNone && int32(leverage.MarginMode) != slf.req.MarginMode {
		return domain.Code251117, errors.New("marginMode not equal")
	}
	if leverage.BLeverage <= 0 {
		logrus.Info(0, "Both UnLock", fmt.Sprintf("%+v", leverage))
		return domain.Code251114, errors.New("get user leverage err")
	}
	if leverage.BLeverage != slf.req.Leverage {
		return domain.Code251115, errors.New("leverage not equal")
	}

	// 双向持仓平仓需要自行计算解冻保证金
	if slf.req.IsClose() && slf.req.UnfrozenMargin.LessThanOrEqual(decimal.Zero) {
		slf.req.UnfrozenMargin, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(decimal.NewFromInt32(int32(leverage.BLeverage))), domain.CurrencyPrecision)
	}
	// 按照统一币对批量修改(注: 此处slf.req.ContractCode()不可使用order.ContractCode()替代, 因为撮合给的orders中所有ContractCode都为空)
	logrus.Info(0, "================ Both.UnLock [DecrFrozen]", slf.req.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "slf.req", fmt.Sprintf("%+v", slf.req), "slf.req.UnfrozenMargin", slf.req.UnfrozenMargin)
	// asset.DecrFrozen(util.PosSide(slf.req.Side, slf.req.Offset, int32(asset.PositionMode)), slf.req.ContractCode(), slf.req.UnfrozenMargin)
	asset.DecrFrozen(slf.req.Side, slf.req.ContractCode(), slf.req.UnfrozenMargin)
	logrus.Info(0, "================ Both.UnLock [DecrFrozen]", slf.req.UID, "slf.req.UnfrozenMargin", slf.req.UnfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	err = userCache.LockOrUnlock(asset)
	if err != nil {
		return domain.Code252405, nil
	}

	assetSync := modelutil.NewLogAssetSync(asset, slf.req.Quote, slf.req.OperateTime)
	redis := redislib.Redis()
	go AddAssetLogs(redis, assetSync) // wallet资产异步存库

	return http.StatusOK, nil
}
