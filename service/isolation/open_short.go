package isolation

import (
	"errors"
	"fmt"
	"log"
	"net/http"

	"futures-asset/cache/price"
	"futures-asset/cache/swapcache"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"futures-asset/util"
	"futures-asset/util/modelutil"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// OpenPos 开仓操作
type OpenShort struct {
	req *payload.LockParam
}

func (slf *OpenShort) Lock() (payload.LockRes, error) {
	// req user
	// mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 30*time.Second)
	// if mutex.Lock() != nil {
	// 	return payload.LockRes{Code: domain.Code251101}, domain.ErrLockPos
	// }
	// defer mutex.Unlock()
	redislib.Redis().LockRedis(domain.MutexSwapPosLock+slf.req.UID, 10)
	defer redislib.Redis().UnLockRedis(domain.MutexSwapPosLock + slf.req.UID)

	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return payload.LockRes{Code: domain.Code252404}, err
	}
	// log.Printf("req %+v cache asset: %+v", slf.req, asset)

	// 平仓不用校验price, 所以放在开仓函数内部校验
	if slf.req.Price.Sign() <= 0 {
		return payload.LockRes{Code: domain.CodeParamInvalid}, errors.New("price less or equal 0")
	}
	leverage := asset.GetLeverage(slf.req.ContractCode())
	if leverage.MarginMode != domain.MarginModeNone && int32(leverage.MarginMode) != slf.req.MarginMode {
		return payload.LockRes{Code: domain.Code251117}, errors.New("marginMode not equal")
	}

	if asset.PositionMode == domain.HoldModeBoth {
		return payload.LockRes{Code: domain.Code251124}, errors.New("hold mode error")
	}

	userLeverage := decimal.Zero
	switch domain.MarginMode(slf.req.MarginMode) {
	case domain.MarginModeIsolated:
		if leverage.SLeverage != slf.req.Leverage {
			return payload.LockRes{Code: domain.Code251115}, errors.New("leverage not equal")
		}
		userLeverage = decimal.NewFromInt(int64(leverage.SLeverage))

	case domain.MarginModeCross:
		if leverage.Leverage != slf.req.Leverage {
			return payload.LockRes{Code: domain.Code251115}, errors.New("leverage not equal")
		}
		userLeverage = decimal.NewFromInt(int64(leverage.Leverage))

	default:
		return payload.LockRes{Code: domain.Code251115}, errors.New("leverage not equal")

	}

	if userLeverage.IntPart() <= 0 {
		logrus.Info(0, "OpenShort Lock userLeverage", userLeverage, fmt.Sprintf("%+v", leverage))
		return payload.LockRes{Code: domain.Code251114}, errors.New("get user leverage err")
	}

	pCache := price.New()

	maxPosValue, err := swapcache.GetMaxPosValueWithLeverage(slf.req.Base, slf.req.Quote, leverage.Leverage)
	if err != nil {
		return payload.LockRes{Code: domain.ErrGetAllCfg}, errors.New(domain.ErrMsg[domain.ErrGetAllCfg])
	}
	if maxPosValue.IsZero() {
		return payload.LockRes{Code: domain.ErrMaxPos}, errors.New(domain.ErrMsg[domain.ErrMaxPos])
	}
	if maxPosValue.IsPositive() {
		frozenValue := asset.GetFrozenByCode(slf.req.ContractCode()).Mul(userLeverage)
		if maxPosValue.LessThan(asset.ShortPos.CalcPosValue(pCache).Add(asset.LongPos.CalcPosValue(pCache)).Add(slf.req.Amount.Mul(slf.req.Price)).Add(frozenValue)) {
			return payload.LockRes{Code: domain.ErrMaxPos}, errors.New(domain.ErrMsg[domain.ErrMaxPos])
		}
	}

	available, err := userCache.GetAvailableBase(pCache, asset, domain.MarginMode(slf.req.MarginMode), slf.req.Quote)
	if err != nil {
		log.Println("open sell Lock GetAvailableBase error:", err)
		return payload.LockRes{Code: domain.Code252407}, err
	}

	trialBalance := asset.TrialCBalance(slf.req.Quote)
	if asset.AssetMode == domain.AssetMode {
		trialBalance, err = asset.TotalJoinTrialBalance(pCache)
		if err != nil {
			log.Println("both Lock error:", err)
			return payload.LockRes{Code: domain.Code252408}, err
		}
	}

	// if slf.req.MarginMode == int32(domain.MarginModeCross) {
	//	// 全仓需要加全仓未实现
	//	avail = avail.Add(userCache.CrossUnrealTotal(pCache, asset.AssetMode, slf.req.Quote))
	// }
	if available.Sign() <= 0 {
		return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenSell
	}

	originFrozen, _ := util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage), domain.CurrencyPrecision)
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == domain.AssetMode {
		// 联合保证金 需要转成 usdt
		rate = pCache.SpotURate(slf.req.Quote)
		if rate.IsZero() {
			log.Printf("available: %s  slf.req.Leverage: %d rate: %s slf.req.Amount: %s", available, leverage.Leverage, rate, slf.req.Amount)
			return payload.LockRes{Code: domain.Code250009}, domain.ErrInsufficientOpenSell
		}
		usdtFrozen, _ := util.RoundCeil(originFrozen.Mul(rate), domain.CurrencyPrecision)
		diff := available.Sub(usdtFrozen)
		if slf.req.IsLimitOrder == domain.Market {
			codeSetting, err := setting.Service.GetCachePair(slf.req.Base, slf.req.Quote)
			if err != nil {
				logrus.Errorf("open sell get contract pair base:%s quote:%s setting err:%s", slf.req.Base, slf.req.Quote, err)
				return payload.LockRes{Code: domain.Code250002}, err
			}
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				minAmount := codeSetting.MinAmount
				log.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
				if available.Mul(userLeverage).LessThan(minAmount) {
					log.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s price: %s settingMinAmount: %s)",
						available, minAmount, userLeverage, slf.req.Price, codeSetting.MinAmount)
					return payload.LockRes{Code: domain.Code252002}, errors.New("place min amount limit")
				}
				slf.req.Amount = available.Mul(userLeverage).Div(rate).Div(slf.req.Price).Truncate(codeSetting.AmountPrecision)
				originFrozen, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage).Mul(rate), domain.CurrencyPrecision)
				diff = available.Sub(usdtFrozen)
			}
		} else {
			if diff.Sign() < 0 {
				return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenBuy
			}
			if available.Mul(userLeverage).Div(rate).Div(slf.req.Price).LessThan(slf.req.Amount) {
				log.Printf("available: %s  slf.req.Leverage: %d slf.req.Amount: %s", available, leverage.Leverage, slf.req.Amount)
				return payload.LockRes{Code: domain.Code250005}, domain.ErrInsufficientPos
			}
		}
	} else {
		diff := available.Sub(originFrozen)
		if slf.req.IsLimitOrder == domain.Market {
			codeSetting, err := setting.Service.GetCachePair(slf.req.Base, slf.req.Quote)
			if err != nil {
				logrus.Errorf("open sell get contract pair base:%s quote:%s setting err:%s", slf.req.Base, slf.req.Quote, err)
				return payload.LockRes{Code: domain.Code250002}, err
			}
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				minAmount := codeSetting.MinAmount
				log.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
				if available.Mul(userLeverage).LessThan(minAmount) {
					log.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s price: %s settingMinAmount: %s)",
						available, minAmount, userLeverage, slf.req.Price, codeSetting.MinAmount)
					return payload.LockRes{Code: domain.Code252002}, errors.New("place min amount limit")
				}
				slf.req.Amount = available.Mul(userLeverage).Div(slf.req.Price).Truncate(codeSetting.AmountPrecision)
				originFrozen, _ = util.RoundCeil(slf.req.Amount.Mul(slf.req.Price).Div(userLeverage), domain.CurrencyPrecision)
				diff = available.Sub(originFrozen)
			}
		} else {
			if diff.Sign() < 0 {
				return payload.LockRes{Code: domain.Code252002}, domain.ErrInsufficientOpenBuy
			}
			if available.Mul(userLeverage).Div(slf.req.Price).LessThan(slf.req.Amount) {
				log.Printf("available: %s  slf.req.Leverage: %d slf.req.Amount: %s", available, leverage.Leverage, slf.req.Amount)
				return payload.LockRes{Code: domain.Code250005}, domain.ErrInsufficientPos
			}
		}
	}
	logrus.Info(0, "================ OpenShort.Lock [IncrFrozen]", slf.req.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "slf.req", fmt.Sprintf("%+v", slf.req))
	asset.IncrFrozen(slf.req.PosSide, slf.req.ContractCode(), originFrozen)
	logrus.Info(0, "================ OpenShort.Lock [IncrFrozen]", slf.req.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	err = userCache.LockOrUnlock(asset)
	if err != nil {
		return payload.LockRes{Code: domain.Code252405}, err
	}

	redis := redislib.Redis()
	assetSwap := modelutil.NewLogAssetSync(asset, slf.req.Quote, slf.req.OperateTime)
	go AddAssetLogs(redis, assetSwap) // wallet资产异步存库

	haveTrial := 0
	if trialBalance.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}
	return payload.LockRes{Code: http.StatusOK, PositionMode: asset.PositionMode, Amount: slf.req.Amount, FrozenMargin: originFrozen, JoinMarginRate: rate, HaveTrial: haveTrial}, nil
}

func (slf *OpenShort) UnLock() (domain.Code, error) {
	orders := []payload.LockParam{
		*slf.req,
	}
	code, _, err := NewBatch(slf.req).UnLockAsset(orders)
	return code, err
}
