package isolation

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/service/persist"
	"futures-asset/util"

	"github.com/sirupsen/logrus"
)

func AddAssetLogs(_redis *redislib.Config, bills ...*repository.LogAssetSync) {
	if _redis == nil {
		_redis = redislib.Redis()
	}
	for _, bill := range bills {
		if bill.AssetSwap.UID == "" {
			logrus.Error(fmt.Sprintf("asset log not have user id, asset:%+v", bill))
			continue
		}
		// wallet资产异步存库
		if err := _redis.LPush(domain.SyncListSwapAsset+bill.AssetSwap.UID[len(bill.AssetSwap.UID)-1:], bill); err != nil {
			logrus.Error(fmt.Sprintf("lpush asset err:%v", err))
		}
	}
}

func AddPosLogs(_redis *redislib.Config, bills ...*repository.LogPosSync) {
	if _redis == nil {
		_redis = redislib.Redis()
	}
	for _, bill := range bills {
		// 仓位异步存库
		if err := persist.SyncPos(redislib.Redis(), strings.ToUpper(bill.LogPos.ContractCode), bill); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, bill))
		}
	}
}

func SyncPos(pos repository.PosSwap) error {
	mutex := redislib.NewMutex(domain.MutexSwapPosLock+pos.UID, 30*time.Second)
	if mutex.Lock() != nil {
		return errors.New("sync pos error")
	}
	defer mutex.Unlock()

	base, quote := util.BaseQuote(pos.ContractCode)
	// 获取仓位
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  base,
			Quote: quote,
		},
	}, pos.UID)
	return userCache.UpdateAnyPos(pos)
}
