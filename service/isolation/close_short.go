package isolation

import (
	"fmt"
	"net/http"

	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/service/persist"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// CloseShort 买入平空仓操作
type CloseShort struct {
	req *payload.LockParam
}

func (slf *CloseShort) Lock() (payload.LockRes, error) {
	// req taker & maker
	// mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 30*time.Second)
	// if mutex.Lock() != nil {
	// 	return payload.LockRes{Code: domain.Code251101}, domain.ErrLockPos
	// }
	// defer mutex.Unlock()
	redislib.Redis().LockRedis(domain.MutexSwapPosLock+slf.req.UID, 10)
	defer redislib.Redis().UnLockRedis(domain.MutexSwapPosLock + slf.req.UID)

	// 获取仓位
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return payload.LockRes{Code: domain.Code251102}, err
	}

	if asset.ShortPos.PosAvailable.Sign() <= 0 {
		logrus.Infof("close buy req user pos: userid:%s PosAvailable:%v,Amount:%v", asset.UID, asset.ShortPos.PosAvailable.String(), slf.req.Amount.String())
		return payload.LockRes{Code: domain.Code251110}, fmt.Errorf("insufficient short pos avaiable zero")
	}

	diffPos := asset.ShortPos.PosAvailable.Sub(slf.req.Amount)
	if diffPos.IsNegative() {
		switch slf.req.OrderType {
		// 37-部分止损 36-全部止损 32-部分止盈 31-全部止盈
		case domain.OrderTypeStopLossPart, domain.OrderTypeStopLossAll, domain.OrderTypeTakeProfitPart, domain.OrderTypeTakeProfitAll:
			slf.req.Amount = asset.ShortPos.PosAvailable

		default:
			return payload.LockRes{Code: domain.Code251110}, fmt.Errorf("insufficient short pos avaiable")

		}
	}
	asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Sub(slf.req.Amount)

	err = userCache.UpdateShortPos(asset)
	if err != nil {
		return payload.LockRes{Code: domain.Code251104}, err
	}

	pos := userCache.NewLogPosSync(asset.ShortPos, slf.req.OperateTime, "", slf.req.OrderId,
		slf.req.Side, slf.req.PosSide, slf.req.Amount, decimal.Zero)
	go func() {
		// wallet资产异步存库
		if err := persist.SyncPos(redislib.Redis(), slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
	}()

	haveTrial := 0
	if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}
	return payload.LockRes{Code: http.StatusOK, Amount: slf.req.Amount, HaveTrial: haveTrial}, nil
}

func (slf *CloseShort) UnLock() (domain.Code, error) {
	orders := []payload.LockParam{
		*slf.req,
	}
	code, _, err := NewBatch(slf.req).UnlockPos(orders)
	return code, err
}
