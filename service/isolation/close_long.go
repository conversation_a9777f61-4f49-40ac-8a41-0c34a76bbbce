package isolation

import (
	"fmt"
	"net/http"

	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/service/persist"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// CloseLong 卖出平多仓操作
type CloseLong struct {
	req *payload.LockParam
}

func (slf *CloseLong) Lock() (payload.LockRes, error) {
	// req user
	// mutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.req.UID, 30*time.Second)
	// if mutex.Lock() != nil {
	// 	return payload.LockRes{Code: domain.Code251101}, domain.ErrLockPos
	// }
	// defer mutex.Unlock()
	redislib.Redis().LockRedis(domain.MutexSwapPosLock+slf.req.UID, 10)
	defer redislib.Redis().UnLockRedis(domain.MutexSwapPosLock + slf.req.UID)

	// 获取仓位
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  slf.req.Base,
			Quote: slf.req.Quote,
		},
	}, slf.req.UID)
	asset, err := userCache.Load()
	if err != nil {
		return payload.LockRes{Code: domain.Code251103}, err
	}

	if asset.LongPos.PosAvailable.Sign() <= 0 {
		logrus.Infof("close sell req user pos: userid:%s PosAvailable:%v,Amount:%v", asset.UID, asset.LongPos.PosAvailable.String(), slf.req.Amount.String())
		return payload.LockRes{Code: domain.Code251111}, fmt.Errorf("insufficient long pos avaiable zero")
	}

	diffPos := asset.LongPos.PosAvailable.Sub(slf.req.Amount)
	if diffPos.IsNegative() {
		switch slf.req.OrderType {
		// 37-部分止损 36-全部止损 32-部分止盈 31-全部止盈
		case domain.OrderTypeStopLossPart, domain.OrderTypeStopLossAll, domain.OrderTypeTakeProfitPart, domain.OrderTypeTakeProfitAll:
			slf.req.Amount = asset.LongPos.PosAvailable

		default:
			return payload.LockRes{Code: domain.Code251111}, fmt.Errorf("insufficient long pos avaiable")
		}
	}
	asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Sub(slf.req.Amount)

	err = userCache.UpdateLongPos(asset)
	if err != nil {
		return payload.LockRes{Code: domain.Code251105}, err
	}

	pos := userCache.NewLogPosSync(asset.LongPos, slf.req.OperateTime, "", slf.req.OrderId,
		slf.req.Side, slf.req.PosSide, slf.req.Amount, decimal.Zero)
	go func() {
		// wallet资产异步存库
		if err := persist.SyncPos(redislib.Redis(), slf.req.ContractCode(), pos); err != nil {
			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
		}
	}()

	haveTrial := 0
	if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}
	return payload.LockRes{Code: http.StatusOK, Amount: slf.req.Amount, HaveTrial: haveTrial}, nil
}

func (slf *CloseLong) UnLock() (domain.Code, error) {
	orders := []payload.LockParam{
		*slf.req,
	}
	code, _, err := NewBatch(slf.req).UnlockPos(orders)
	return code, err
}
