package isolation

import (
	"fmt"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/pkg/redislib"
	"log"
	"testing"
)

var param = &payload.LockParam{
	UID:         "00000000",
	Base:        "BTCUSDT",
	Quote:       "USDT",
	OrderId:     "111111",
	OrderType:   2,
	OperateTime: 1575594608686069227,
}

var paramUnlock = &payload.LockParam{
	UID:         "00000000",
	Base:        "BTCUSDT",
	Quote:       "USDT",
	OrderId:     "111111",
	OrderType:   2,
	OperateTime: 1575594608686069227,
}

func TestGetMaxPosWithLeverage(t *testing.T) {
	if err := redislib.Init([]string{
		"10.32.1.100:6379",
		"10.32.1.101:6379",
		"10.32.1.10:6379",
		"10.32.1.100:6380",
		"10.32.1.101:6380",
		"10.32.1.10:6380",
	}, ""); err != nil {
		log.Fatalf("init redis err: " + err.Error())
	}
	val, _ := swapcache.GetMaxPosValueWithLeverage("etc", "usdt", 10)
	fmt.Println(val)
}
