package burst

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"runtime"
// 	"runtime/debug"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/cache/cachelock"
// 	"futures-asset/cache/price"
// 	"futures-asset/cache/sharedcache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/handler/monitor"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/match"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// func (bs *workingBurstService) NewBurstProcessorStart(processor int, ctx context.Context, wg *sync.WaitGroup) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "BurstProcessorStart recover error:", err)
// 			debug.PrintStack()
// 			time.Sleep(time.Second)
// 		}
// 	}()
// 	wg.Add(1)

// 	_ctx := context.WithValue(ctx, "name", "BurstProcessor")
// 	_wg := new(sync.WaitGroup)
// 	for i := 0; i < processor; i++ {
// 		go bs._processor(i, domain.MarginModeCross, 0, ctx, _wg, false)
// 		go bs._processor(i, domain.MarginModeIsolated, domain.LongPos, ctx, _wg, false)
// 		go bs._processor(i, domain.MarginModeIsolated, domain.ShortPos, ctx, _wg, false)
// 		go bs._processor(i, domain.MarginModeIsolated, domain.BothPos, ctx, _wg, false)
// 	}

// 	go bs._healthChecker(domain.MarginModeCross, ctx, _wg, false, false)
// 	go bs._healthChecker(domain.MarginModeIsolated, ctx, _wg, false, false)

// 	// 逐仓体验金仓位
// 	go bs._healthChecker(domain.MarginModeIsolated, ctx, _wg, false, true)

// 	time.Sleep(time.Second * 3)

// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop
// 		}
// 	}

// 	bs.burstStop = true
// 	_wg.Wait()
// 	wg.Done()
// 	fmt.Println(bs.contractCode, _ctx.Value("name"), "all stopped")
// }

// func (bs *workingBurstService) _healthChecker(marginMode domain.MarginMode, ctx context.Context, wg *sync.WaitGroup, isRecover bool, isTrialPos bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(1, fmt.Sprintf("%s marginMod(%d) rechecker revocer error:", bs.contractCode, marginMode))
// 			logrus.Error(1, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			bs._healthChecker(marginMode, ctx, wg, true, isTrialPos)
// 		}
// 	}()
// 	if !isRecover {
// 		wg.Add(1)
// 	}

// 	healthCheckerCtx := context.WithValue(ctx, "name", fmt.Sprintf("healthChecker %d", marginMode))

// 	time.Sleep(time.Second * 2)
// 	logrus.Info(0, bs.contractCode, "burst", healthCheckerCtx.Value("name"), "started")
// 	defer logrus.Info(0, bs.contractCode, "burst", healthCheckerCtx.Value("name"), "stopped")

// 	healthCheckerRedisKey := cachekey.GetBurstWorkingUsersRedisKey(marginMode)
// 	if isTrialPos {
// 		healthCheckerRedisKey = cachekey.GetTrialBurstWorkingUsersRedisKey(marginMode)
// 	}
// Loop:
// 	for {
// 		select {
// 		case <-healthCheckerCtx.Done():
// 			break Loop

// 		default:
// 			users, err := redisCli.Client().SMembers(context.Background(), healthCheckerRedisKey).Result()
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, "reChecker SMembers", healthCheckerRedisKey, "error:", err)
// 				}
// 			} else {
// 				for _, uid := range users {
// 					userBurstLockKey := cachekey.GetBurstLockRedisKey(uid, domain.MarginModeCross)
// 					locks, err := redisCli.Client().HGetAll(context.Background(), userBurstLockKey).Result()
// 					if err != nil {
// 						logrus.Error(0, "healthChecker HGetAll error:", err)
// 						continue
// 					}
// 					if len(locks) > 0 {
// 						switch marginMode {
// 						case domain.MarginModeCross:
// 							for lock := range locks {
// 								if lock == uid {
// 									// todo 预留健康检查
// 								}
// 							}

// 						case domain.MarginModeIsolated:
// 							// todo 预留健康检查

// 						default:

// 						}
// 					}
// 				}
// 			}

// 		}
// 		runtime.Gosched()
// 		time.Sleep(time.Second * 5)
// 	}

// 	wg.Done()
// }

// func (bs *workingBurstService) _processor(index int, marginMode domain.MarginMode, posSide int32, ctx context.Context, wg *sync.WaitGroup, isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(1, fmt.Sprintf("%s marginMod(%d) processor %d revocer error:", bs.contractCode, marginMode, index))
// 			logrus.Error(1, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			bs._processor(index, marginMode, posSide, ctx, wg, true)
// 		}
// 	}()
// 	if !isRecover {
// 		wg.Add(1)
// 	}

// 	workingIndex := fmt.Sprintf("%d", index)
// 	processorCtx := context.WithValue(ctx, "workingIndex", workingIndex)

// 	time.Sleep(time.Second * 2)
// 	logrus.Info(0, bs.contractCode, "burst processor", workingIndex, "started")
// 	defer logrus.Info(0, bs.contractCode, "burst processor", workingIndex, "stopped")

// Loop:
// 	for {
// 		select {
// 		case <-processorCtx.Done():
// 			break Loop

// 		default:
// 			liquidationData, err := rPopBurstTask(marginMode, bs.contractCode, posSide)
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, bs.contractCode, index, "processor rPopBurstTask error:", err)
// 				}
// 				goto Next
// 			}

// 			logrus.Info(0, bs.contractCode, "process", liquidationData)

// 			liquidation := cache.LiquidationInfo{}
// 			err = json.Unmarshal([]byte(liquidationData), &liquidation)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, "burst info error:", liquidationData)
// 				goto Next
// 			}

// 			if !liquidation.IsTrialPos {
// 				bs._burst(liquidationData, marginMode)
// 			} else {
// 				bs._burstTrial(liquidationData, marginMode)
// 			}

// 		}
// 	Next:
// 		runtime.Gosched()
// 		time.Sleep(time.Millisecond * 200)

// 	}

// 	wg.Done()
// }

// func (bs *workingBurstService) _burst(liquidationData string, marginMode domain.MarginMode) {
// 	liquidation := cache.LiquidationInfo{}
// 	err := json.Unmarshal([]byte(liquidationData), &liquidation)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "burst info error:", liquidationData)
// 		return
// 	}

// 	processStatus, err := getBurstWorkStatus(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidation.IsTrialPos)
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burst HGet", "error:", err)
// 		}
// 	}

// 	// 更新爆仓处理状态
// 	switch processStatus {
// 	case domain.BurstTrigged, domain.BurstReady:
// 		_, err = updateBurstWorkStatus(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, domain.BurstProcessing, liquidation.IsTrialPos)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "buildProcessor HSet error:", err)
// 		}

// 	case domain.BurstProcessing:

// 	case domain.BurstProcessed:
// 		logrus.Info(0, bs.contractCode, liquidation.UID, "burst processed")
// 		return

// 	default:
// 		logrus.Info(0, "====================== end")
// 		return

// 	}

// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burst recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			lPushBurstTask(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidationData)
// 			return
// 		}
// 		lPushBurstTask(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidationData)
// 	}()

// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, liquidation.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "burstService burst GetUserCacheData error:", err)
// 		return
// 	}

// 	logrus.Info(0, bs.contractCode, liquidation.UID, "liquidation work get user asset info", fmt.Sprintf("%+v", liquidation))

// 	pCache := price.New()

// 	switch marginMode {
// 	case domain.MarginModeIsolated:
// 		// 撤销平仓单
// 		success, _ := match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, domain.Close, 0, int32(marginMode), int32(liquidation.CancelType), 0)
// 		if !success {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burstTradeWork ConditionCancel crossed Close failed")
// 		}

// 	default:
// 		// 撤销开平仓单
// 		success, _ := match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, 0, 0, int32(marginMode), int32(liquidation.CancelType), 0)
// 		if !success {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burstTradeWork ConditionCancel crossed Open failed")
// 		}

// 	}
// 	time.Sleep(300 * time.Millisecond)

// 	getCountTimes := 0
// reGetOrderCount:
// 	countOk, orderCount := match.Service.OrderListCount(liquidation.UID, bs.base, bs.quote, 0)
// 	if !countOk || orderCount >= 1 {
// 		time.Sleep(time.Second)
// 		if getCountTimes < 3 {
// 			getCountTimes += 1
// 			goto reGetOrderCount
// 		}
// 	}
// 	if orderCount >= 1 {
// 		logrus.Info(0, bs.contractCode, liquidation.UID, "liquidation cancel user failed retry:", getCountTimes, "orderCount", orderCount, fmt.Sprintf("%+v", liquidation))
// 		return
// 	}

// 	longPos := decimal.Zero
// 	longPosValue := decimal.Zero
// 	shortPos := decimal.Zero
// 	shortPosValue := decimal.Zero

// 	{
// 		for _, posData := range []repository.PosSwap{
// 			assetInfo.LongPos, assetInfo.ShortPos, assetInfo.BothPos,
// 		} {
// 			switch posData.PosSide {
// 			case domain.LongPos:
// 				longPos = longPos.Add(posData.Pos)
// 				longPosValue = longPosValue.Add(posData.CalcPosValue(pCache))

// 			case domain.ShortPos:
// 				shortPos = shortPos.Add(posData.Pos)
// 				shortPosValue = shortPosValue.Add(posData.CalcPosValue(pCache))
// 			case domain.BothPos:
// 				if posData.Pos.GreaterThan(decimal.Zero) {
// 					longPos = longPos.Add(posData.Pos.Abs())
// 					longPosValue = longPosValue.Add(posData.CalcPosValue(pCache))
// 				} else if posData.Pos.LessThan(decimal.Zero) {
// 					shortPos = shortPos.Add(posData.Pos.Abs())
// 					shortPosValue = shortPosValue.Add(posData.CalcPosValue(pCache))
// 				}

// 			default:

// 			}
// 		}
// 	}

// 	logrus.Info(0, bs.contractCode, liquidation.UID,
// 		"longPos", longPos,
// 		"longPosValue", longPosValue,
// 		"shortPos", shortPos,
// 		"shortPosValue", shortPosValue,
// 	)

// 	assetInfo, err = userCache.Load()
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation Load assetInfo error:", err)
// 		return
// 	}
// 	// 刷新 posInfo 信息
// 	posInfo := repository.PosSwap{}
// 	switch liquidation.PosSide {
// 	case domain.LongPos:
// 		posInfo = assetInfo.LongPos

// 	case domain.ShortPos:
// 		posInfo = assetInfo.ShortPos

// 	case domain.BothPos:
// 		posInfo = assetInfo.BothPos

// 	default:
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation PosSide error:", fmt.Sprintf("%+v", liquidation))
// 		return

// 	}

// 	switch liquidation.LiquidationType {

// 	case domain.LiquidationTypeReduce:
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation force down level", liquidation.CurrentLevel, "->", liquidation.TargetLevel)

// 		// 强制减仓（降级）
// 		forceDownOk, isSelfTrade, reduceAmount, err := bs.forceDownPosLevel(liquidation, posInfo, domain.LiquidationTypeBurstSelfReduce)
// 		if err != nil {
// 			time.Sleep(time.Second)
// 			logrus.Error(0, bs.contractCode, assetInfo.UID, "force done level failed", err)
// 			return
// 		}

// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "not last level, forceDownOk", forceDownOk, "isSelfTrade", isSelfTrade, "reduceAmount", reduceAmount)
// 		if forceDownOk {
// 			_, _ = updateBurstWorkStatus(liquidation.MarginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, domain.BurstProcessed, liquidation.IsTrialPos)
// 			return
// 		}

// 	case domain.LiquidationTypeBurst:
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst start")

// 		if posInfo.Pos.IsZero() && posInfo.MarginMode == int32(domain.MarginModeCross) && assetInfo.PositionMode == domain.HoldModeHedge {
// 			switch posInfo.PosSide {
// 			case domain.ShortPos:
// 				posInfo = assetInfo.LongPos
// 			case domain.LongPos:
// 				posInfo = assetInfo.ShortPos
// 			default:

// 			}
// 		}

// 		if assetInfo.PositionMode == domain.HoldModeHedge {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst long and short reduce")
// 			// 可以多空抵消
// 			if longPos.GreaterThan(decimal.Zero) && shortPos.GreaterThan(decimal.Zero) {
// 				logrus.Info(0, bs.contractCode, liquidation.UID, "longPos", longPos, "shortPos", shortPos)
// 				err = bs.selfForceBurst(longPos, shortPos, liquidation, assetInfo, decimal.Zero, domain.LiquidationTypeBurstSelfReduce)
// 				logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst selfForceBurst error:", err)
// 				if err != nil {
// 					logrus.Error(0, bs.contractCode, liquidation.UID, "burstTradeWork SelfForceBurst error:", err)
// 				}
// 				time.Sleep(time.Second)
// 			}
// 		}

// 		checkDeal := true
// 		err = bs.closeUserPos(liquidation, posInfo, decimal.Zero, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst closeUserPos error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation closeUserPos error:", err)
// 			// checkDeal = false
// 		}

// 		err = bs.depth5Close(liquidation, posInfo, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst depth5Close error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "DepthTradeBurst depth5Close error:", err)
// 			// checkDeal = false
// 		}

// 		err = bs.forceRivalBurst(liquidation, posInfo, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst forceRivalBurst error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "forceRivalBurst error:", err)
// 			// checkDeal = false
// 		}

// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst checkDeal:", checkDeal)
// 		// 检查成交
// 		if checkDeal {
// 			time.Sleep(time.Second * 2)
// 			err = bs.checkDeal(liquidation, posInfo, false, domain.CancelTypeBurst, domain.LiquidationTypeBurst, decimal.Zero)
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst checkDeal error:", err)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, assetInfo.UID, "liquidation checkDeal error:", err)
// 				return
// 			}
// 			_, _ = updateBurstWorkStatus(liquidation.MarginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, domain.BurstProcessed, liquidation.IsTrialPos)
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) _burstTrial(liquidationData string, marginMode domain.MarginMode) {
// 	liquidation := cache.LiquidationInfo{}
// 	err := json.Unmarshal([]byte(liquidationData), &liquidation)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "burst info error:", liquidationData)
// 		return
// 	}

// 	processStatus, err := getBurstWorkStatus(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidation.IsTrialPos)
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burst HGet", "error:", err)
// 		}
// 	}

// 	// 更新爆仓处理状态
// 	switch processStatus {
// 	case domain.BurstTrigged, domain.BurstReady:
// 		_, err = updateBurstWorkStatus(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, domain.BurstProcessing, liquidation.IsTrialPos)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "buildProcessor HSet error:", err)
// 		}

// 	case domain.BurstProcessing:

// 	case domain.BurstProcessed:
// 		logrus.Info(0, bs.contractCode, liquidation.UID, "burst processed")
// 		return

// 	default:
// 		logrus.Info(0, "====================== end")
// 		return

// 	}

// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "burst recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			lPushBurstTask(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidationData)
// 			return
// 		}
// 		lPushBurstTask(marginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, liquidationData)
// 	}()

// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, liquidation.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "burstService burst GetUserCacheData error:", err)
// 		return
// 	}

// 	logrus.Info(0, bs.contractCode, liquidation.UID, "liquidation work get user asset info", fmt.Sprintf("%+v", liquidation))

// 	pCache := price.New()

// 	longPos := decimal.Zero
// 	longPosValue := decimal.Zero
// 	shortPos := decimal.Zero
// 	shortPosValue := decimal.Zero

// 	{
// 		for _, posData := range []repository.PosSwap{
// 			assetInfo.TrialLongPos, assetInfo.TrialShortPos, assetInfo.TrialBothPos,
// 		} {
// 			switch posData.PosSide {
// 			case domain.LongPos:
// 				longPos = longPos.Add(posData.Pos)
// 				longPosValue = longPosValue.Add(posData.CalcPosValue(pCache))

// 			case domain.ShortPos:
// 				shortPos = shortPos.Add(posData.Pos)
// 				shortPosValue = shortPosValue.Add(posData.CalcPosValue(pCache))

// 			case domain.BothPos:
// 				if posData.Pos.GreaterThan(decimal.Zero) {
// 					longPos = longPos.Add(posData.Pos.Abs())
// 					longPosValue = longPosValue.Add(posData.CalcPosValue(pCache))
// 				} else if posData.Pos.LessThan(decimal.Zero) {
// 					shortPos = shortPos.Add(posData.Pos.Abs())
// 					shortPosValue = shortPosValue.Add(posData.CalcPosValue(pCache))
// 				}
// 			default:

// 			}
// 		}
// 	}

// 	logrus.Info(0, bs.contractCode, liquidation.UID,
// 		"longPos", longPos,
// 		"longPosValue", longPosValue,
// 		"shortPos", shortPos,
// 		"shortPosValue", shortPosValue,
// 	)

// 	assetInfo, err = userCache.Load()
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation Load assetInfo error:", err)
// 		return
// 	}
// 	// 刷新 posInfo 信息
// 	posInfo := repository.PosSwap{}
// 	// 体验金仓位
// 	switch liquidation.PosSide {
// 	case domain.LongPos:
// 		posInfo = assetInfo.TrialLongPos

// 	case domain.ShortPos:
// 		posInfo = assetInfo.TrialShortPos

// 	case domain.BothPos:
// 		posInfo = assetInfo.TrialBothPos

// 	default:
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation PosSide error:", fmt.Sprintf("%+v", liquidation))
// 		return

// 	}

// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeBurst:
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst start")

// 		checkDeal := true
// 		err = bs.closeUserPos(liquidation, posInfo, decimal.Zero, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst closeUserPos error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "liquidation closeUserPos error:", err)
// 			// checkDeal = false
// 		}

// 		err = bs.depth5Close(liquidation, posInfo, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst depth5Close error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "DepthTradeBurst depth5Close error:", err)
// 			// checkDeal = false
// 		}

// 		err = bs.forceRivalBurst(liquidation, posInfo, decimal.Zero)
// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst forceRivalBurst error:", err)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "forceRivalBurst error:", err)
// 			// checkDeal = false
// 		}

// 		logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst checkDeal:", checkDeal)
// 		// 检查成交
// 		if checkDeal {
// 			time.Sleep(time.Second * 2)
// 			err = bs.checkDeal(liquidation, posInfo, false, domain.CancelTypeBurst, domain.LiquidationTypeBurst, decimal.Zero)
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation burst checkDeal error:", err)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, assetInfo.UID, "liquidation checkDeal error:", err)
// 				return
// 			}
// 			_, _ = updateBurstWorkStatus(liquidation.MarginMode, bs.contractCode, liquidation.PosSide, liquidation.UID, domain.BurstProcessed, liquidation.IsTrialPos)
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) closeUserPos(liquidation cache.LiquidationInfo, posInfo repository.PosSwap, closePrice, closeAmount decimal.Decimal) error {
// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, "closeUserPos updatePosInfo error:", err)
// 		return err
// 	}
// 	logrus.Info(0, "closeUserPos updatePosInfo done")
// 	// 检查是否有仓位
// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		reduceAmount, err := bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Info(0, "closeUserPos fetchCloseAmount error", err)
// 			return errors.New("closeUserPos fetchCloseAmount error")
// 		}
// 		closeAmount = reduceAmount
// 		if closeAmount.IsZero() {
// 			return nil
// 		}
// 		if posInfo.Pos.IsZero() && posInfo.MarginMode == int32(domain.MarginModeCross) {
// 			switch posInfo.PosSide {
// 			case domain.LongPos:
// 				posInfo = assetInfo.ShortPos

// 			case domain.ShortPos:
// 				posInfo = assetInfo.LongPos

// 			default:

// 			}
// 		}
// 		logrus.Info(0, fmt.Sprintf("LiquidationTypeReduce user: %s close amount: %s reduce amount: %s", liquidation.UID, closeAmount.String(), reduceAmount.String()))
// 		fallthrough

// 	default:
// 		if posInfo.Pos.IsZero() {
// 			logrus.Info(0, "closeUserPos position is zero", fmt.Sprintf("%+v", posInfo))
// 			return nil
// 		}

// 	}
// 	logrus.Info(0, "closeUserPos check position done")
// 	if closeAmount.LessThanOrEqual(decimal.Zero) {
// 		closeAmount = posInfo.Pos.Abs()
// 	}
// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "closeUserPos ContractSettings not exists")
// 		return nil
// 	}
// 	closeAmount = closeAmount.RoundCeil(settingInfo.AmountPrecision)
// 	if closeAmount.Abs().GreaterThan(posInfo.Pos.Abs()) {
// 		closeAmount = posInfo.Pos.Abs()
// 	}
// 	if closePrice.LessThanOrEqual(decimal.Zero) {
// 		closePrice = liquidation.CollapsePrice
// 	}
// 	lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		errMsg := fmt.Sprintf("closeUserPos GetContractLastPrice error: %s", err)
// 		logrus.Error(0, errMsg)
// 		return errors.New(errMsg)
// 	}
// 	if decimal.Max(liquidation.CollapsePrice, lastPrice).LessThanOrEqual(decimal.Zero) {
// 		errMsg := fmt.Sprintf("closeUserPos MAX(collapsePrice<%s>, lastPrice<%s>) is zero", liquidation.CollapsePrice, lastPrice)
// 		logrus.Error(0, errMsg)
// 		return err
// 	} else {
// 		if closePrice.Sub(lastPrice).Div(decimal.Max(liquidation.CollapsePrice, lastPrice)).GreaterThan(decimal.NewFromFloat(0.05)) {
// 			errMsg := fmt.Sprintf("closeUserPos collapsePrice<%s> and lastPrice<%s> overflow 5%%", liquidation.CollapsePrice, lastPrice)
// 			logrus.Error(0, errMsg)
// 			return err
// 		}
// 	}
// 	logrus.Info(0, fmt.Sprintf("closeUserPos price check done user: %s, close amount: %s precision: %d", liquidation.UID, closeAmount.String(), settingInfo.AmountPrecision))

// 	closeSide := domain.Sell
// 	closePosSide := domain.Long
// 	posFlag := "LongPos"
// 	switch posInfo.PosSide {
// 	case domain.ShortPos:
// 		closeSide = domain.Buy
// 		closePosSide = domain.Short
// 		posFlag = "ShortPos"

// 	case domain.BothPos:
// 		if posInfo.Pos.LessThan(decimal.Zero) {
// 			closeSide = domain.Buy
// 			closePosSide = domain.Short
// 			posFlag = "ShortPos"
// 		}

// 	default:

// 	}
// 	if closePrice.LessThanOrEqual(decimal.Zero) {
// 		errMsg := fmt.Sprintln("closeUserPos collapsePrice Less than or equal zero")
// 		logrus.Error(0, errMsg)
// 		return err
// 	}
// 	logrus.Info(0, "closeUserPos close price check done")

// 	{
// 		closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 			PositionParams: match.PositionParams{
// 				Side:         closeSide,
// 				PosSide:      closePosSide,
// 				IsLimitOrder: match.IsLimitOrderYes,
// 				OrderType:    match.OrderTypeNormal,
// 				Price:        closePrice.Truncate(domain.PricePrecision),
// 				Amount:       closeAmount.Truncate(domain.CurrencyPrecision),
// 				TriggerPrice: decimal.Zero,
// 			},
// 			UID:             liquidation.UID,
// 			AccountType:     match.AccountTypeSwap,
// 			Base:            bs.base,
// 			Quote:           bs.quote,
// 			Leverage:        posInfo.Leverage,
// 			Platform:        match.PlatformSystem,
// 			LiquidationType: int(liquidation.LiquidationType),
// 			MarginMode:      int(posInfo.MarginMode),
// 			TimeInForce:     match.TimeInForceTypeGTC,
// 			PositionMode:    assetInfo.PositionMode,
// 			IsMixMargin:     assetInfo.AssetMode,
// 			BurstId:         liquidation.BurstId,
// 			BurstTime:       liquidation.BurstTime,
// 			AwardOpIds:      posInfo.AwardOpIds,
// 		})
// 		logrus.Info(0, bs.contractCode, liquidation.UID, "ClosePositions close side", closeSide, "Response:", fmt.Sprintf("%+v", closeResponse))
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "closeUserPos ClosePositions", posFlag, "close error:", err)
// 			return err
// 		} else {
// 			if closeResponse.Code != 200 {
// 				switch closeResponse.Code {
// 				case 300032:
// 					return errors.New("order too big")

// 				default:
// 					logrus.Error(0, bs.contractCode, liquidation.UID, "closeUserPos", posFlag, "close Response", fmt.Sprintf("%+v", closeResponse), "amount:", closeAmount.Truncate(domain.CurrencyPrecision))
// 					return errors.New("closeUserPos closeResponse failed")

// 				}
// 			}
// 		}
// 	}

// 	time.Sleep(time.Second * 2)

// 	_, _ = match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, int32(closePosSide), int32(closeSide), posInfo.MarginMode, int32(liquidation.CancelType), 0)

// 	return nil
// }

// func (bs *workingBurstService) selfForceBurst(longPos, shortPos decimal.Decimal, liquidation cache.LiquidationInfo, assetInfo *repository.AssetSwap, forceAmount decimal.Decimal, liquidationType domain.LiquidationType) error {
// 	if forceAmount.IsZero() {
// 		forceAmount = decimal.Min(longPos, shortPos)
// 	}

// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[bs.contractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "depth5Close ContractSettings not exists")
// 		return nil
// 	}

// 	forceAmount = forceAmount.Truncate(settingInfo.AmountPrecision)
// 	logrus.Info(0, "selfForceBurst forceAmount", forceAmount)

// 	lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		errMsg := fmt.Sprintf("selfForceBurst GetContractLastPrice error: %s", err)
// 		logrus.Error(0, errMsg)
// 		return errors.New(errMsg)
// 	}

// 	pricePrecision := int32(domain.PricePrecision)
// 	if bs.coinPairInfo.PricePrecision > 0 {
// 		pricePrecision = bs.coinPairInfo.PricePrecision
// 	}

// 	longPosInfo := assetInfo.LongPos
// 	shortPosInfo := assetInfo.ShortPos
// 	if liquidation.IsTrialPos {
// 		longPosInfo = assetInfo.TrialLongPos
// 		shortPosInfo = assetInfo.TrialShortPos
// 	}

// 	// 非保证金仓位才会撤单
// 	if !liquidation.IsTrialPos {
// 		_, _ = match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, domain.Close, 0, int32(liquidation.MarginMode), int32(liquidation.CancelType), 0)
// 		time.Sleep(time.Second)
// 	}

// 	res, err := match.Service.BothReducePositions(&match.ReducePositionsParams{
// 		UserSide: match.SideSell,

// 		UID:                 assetInfo.UID,
// 		UserLeverage:        longPosInfo.Leverage,
// 		UserMarginMode:      int(longPosInfo.MarginMode),
// 		UserLiquidationType: int(liquidationType),
// 		UserHoldMode:        assetInfo.PositionMode,
// 		UserIsMixMargin:     assetInfo.AssetMode,
// 		UserTimeInForce:     match.TimeInForceTypeGTC,
// 		UserAwardOpIds:      longPosInfo.AwardOpIds,

// 		TargetUserId:              assetInfo.UID,
// 		TargetUserLeverage:        shortPosInfo.Leverage,
// 		TargetUserMarginMode:      int(shortPosInfo.MarginMode),
// 		TargetUserLiquidationType: int(liquidationType),
// 		TargetUserHoldMode:        assetInfo.PositionMode,
// 		TargetUserIsMixMargin:     assetInfo.AssetMode,
// 		TargetUserTimeInForce:     match.TimeInForceTypeGTC,
// 		TargetUserAwardOpIds:      shortPosInfo.AwardOpIds,

// 		AccountType: match.AccountTypeSwap,
// 		Base:        bs.base,
// 		Quote:       bs.quote,
// 		Price:       lastPrice.Truncate(pricePrecision),
// 		Amount:      forceAmount.Truncate(domain.CurrencyPrecision),
// 		Platform:    match.PlatformSystem,
// 		BurstId:     liquidation.BurstId,
// 		BurstTime:   liquidation.BurstTime,
// 	})
// 	if err != nil {
// 		errMsg := fmt.Sprintf("SelfForceBurst ForceRivalBurst BothReducePositions error: %s", err)
// 		logrus.Error(0, errMsg)
// 		err = errors.New(errMsg)
// 	} else if res.Code != 200 {
// 		errMsg := fmt.Sprintf("SelfForceBurst ForceRivalBurst BothReducePositions response error: %d -- %s", res.Code, res.Msg)
// 		logrus.Error(0, errMsg)
// 		err = errors.New(errMsg)
// 	}
// 	return err
// }

// func (bs *workingBurstService) forceDownPosLevel(liquidation cache.LiquidationInfo, posInfo repository.PosSwap, liquidationType domain.LiquidationType) (bool, bool, decimal.Decimal, error) {
// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, "forceDownPosLevel updatePosInfo error:", err)
// 		return false, false, decimal.Zero, errors.New("forceDownPosLevel updatePosInfo error")
// 	}

// 	// 总仓位价值
// 	var totalPosValue decimal.Decimal
// 	if liquidation.IsTrialPos {
// 		// 体验金仓位
// 		totalPosValue = assetInfo.TrialLongPos.CalcPosHoldValue().Add(assetInfo.TrialShortPos.CalcPosHoldValue().Add(assetInfo.TrialBothPos.CalcPosHoldValue()))
// 	} else {
// 		// 真实仓位
// 		totalPosValue = assetInfo.LongPos.CalcPosHoldValue().Add(assetInfo.ShortPos.CalcPosHoldValue().Add(assetInfo.BothPos.CalcPosHoldValue()))
// 	}

// 	if totalPosValue.LessThanOrEqual(liquidation.TargetLimit) {
// 		return true, false, decimal.Zero, nil
// 	}

// 	// 目标减持价值
// 	targetHighLimit := liquidation.TargetLimit.Mul(decimal.NewFromFloat(0.99))
// 	if totalPosValue.Sub(targetHighLimit).LessThanOrEqual(decimal.Zero) {
// 		return true, false, decimal.Zero, nil
// 	}

// 	if totalPosValue.LessThan(targetHighLimit) {
// 		return true, false, decimal.Zero, nil
// 	}

// 	lastPrice, err := match.Service.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "force down level GetContractLastPrice error:", err)
// 		return false, false, decimal.Zero, errors.New("force down level long MarkPrice is zero")
// 	}

// 	// 目标减持数量
// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "closeUserPos ContractSettings not exists")
// 		return false, false, decimal.Zero, errors.New("force down level setting not exist")
// 	}
// 	targetReduceAmount := totalPosValue.Sub(targetHighLimit).Div(lastPrice).Truncate(settingInfo.AmountPrecision)
// 	if targetReduceAmount.IsZero() {
// 		targetReduceAmount = settingInfo.MinAmount
// 	}

// 	fmt.Println("closeUserPos targetReduceAmount", assetInfo.UID, targetReduceAmount)

// 	// 双向持仓可抵消
// 	if liquidation.MarginMode == domain.MarginModeCross && posInfo.PosSide != domain.BothPos {
// 		longPosAmount := assetInfo.LongPos.Pos
// 		shortPosAmount := assetInfo.ShortPos.Pos
// 		if liquidation.IsTrialPos {
// 			longPosAmount = assetInfo.TrialLongPos.Pos
// 			shortPosAmount = assetInfo.TrialShortPos.Pos
// 		}
// 		// 可以自己抵消
// 		if longPosAmount.GreaterThan(decimal.Zero) && shortPosAmount.GreaterThan(decimal.Zero) {
// 			// 抵消数量
// 			tradeReduceAmount := decimal.Min(decimal.Min(longPosAmount, shortPosAmount), targetReduceAmount)

// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation fetch self reduce amount", fmt.Sprintf("%+v", tradeReduceAmount))
// 			err := bs.selfForceBurst(longPosAmount, shortPosAmount, liquidation, assetInfo, tradeReduceAmount, liquidationType)
// 			if err != nil {
// 				return false, true, targetReduceAmount, err
// 			}

// 			time.Sleep(time.Second * 2)

// 			_, _ = match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, domain.Close, 0, int32(liquidation.MarginMode), int32(liquidation.CancelType), 0)

// 			if targetReduceAmount.Equal(tradeReduceAmount) {
// 				logrus.Info(0, bs.contractCode, assetInfo.UID, "liquidation forceDownPosLevel tradeReduceAmount", tradeReduceAmount, "force down level done")
// 				return true, true, decimal.Zero, nil
// 			}
// 			targetReduceAmount = targetReduceAmount.Sub(tradeReduceAmount)
// 		}
// 	}

// 	checkDeal := true
// 	err = bs.closeUserPos(liquidation, posInfo, decimal.Zero, targetReduceAmount)
// 	if err != nil {
// 		// checkDeal = false
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "closeUserPos error:", err)
// 	} else {
// 		targetReduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, assetInfo.UID, "forceDownPosLevel fetchDownLevelAmount error:", err)
// 			return false, false, targetReduceAmount, nil
// 		}
// 	}

// 	if checkDeal {
// 		err = bs.checkDeal(liquidation, posInfo, false, liquidation.CancelType, liquidation.LiquidationType, targetReduceAmount)
// 		if err == nil {
// 			return true, false, targetReduceAmount, nil
// 		}
// 	}
// 	return false, false, decimal.Zero, nil
// }

// func (bs *workingBurstService) checkDeal(liquidation cache.LiquidationInfo, posInfo repository.PosSwap, skipRiskMargin bool, cancelType domain.CancelType, liquidationType domain.LiquidationType, reduceAmount decimal.Decimal) error {
// 	logrus.Info(0, fmt.Sprintf("%s burstService Check %s Deal", bs.contractCode, liquidation.UID), "skipRiskMargin:", skipRiskMargin, "reduceAmount", reduceAmount)
// 	if bs.burstStop {
// 		logrus.Info(0, "================================= burstStop", bs.burstStop)
// 		return errors.New("burst stop")
// 	}

// 	var err error = nil

// 	if skipRiskMargin {
// 		reduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Error(0, "checkDeal fetchDownLevelAmount error:", err)
// 			return err
// 		}
// 	} else {
// 		if reduceAmount.GreaterThan(posInfo.Pos.Abs()) {
// 			reduceAmount = posInfo.Pos.Abs()
// 		}
// 	}

// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, "checkDeal updatePosInfo error:", err)
// 		return err
// 	}

// 	pCache := price.New()

// 	// 检查是否有仓位
// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		closeAmount, err := bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Info(0, "closeUserPos fetchCloseAmount error", err)
// 			return errors.New("closeUserPos fetchCloseAmount error")
// 		}
// 		reduceAmount = closeAmount
// 		if reduceAmount.IsZero() {
// 			return nil
// 		}
// 		if posInfo.Pos.IsZero() && posInfo.MarginMode == int32(domain.MarginModeCross) {
// 			switch posInfo.PosSide {
// 			case domain.LongPos:
// 				posInfo = assetInfo.ShortPos

// 			case domain.ShortPos:
// 				posInfo = assetInfo.LongPos

// 			default:

// 			}
// 		}
// 		fallthrough

// 	default:
// 		if posInfo.Pos.IsZero() {
// 			return nil
// 		}

// 	}

// 	success, _ := match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, domain.Close, 0, int32(liquidation.MarginMode), int32(cancelType), 0)
// 	if !success {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "CheckDeal ConditionCancel failed")
// 		return errors.New("CheckDeal ConditionCancel failed")
// 	}
// 	time.Sleep(time.Second)

// 	// 获取风险准备金钱包余额
// 	riskMargin := bs.getRiskMarginBalance()
// 	logrus.Info(0, bs.contractCode, liquidation.UID, "checkDeal get risk balance, riskMargin", riskMargin, "skipRiskMargin", skipRiskMargin)

// 	// 未实现盈亏
// 	lossUnReal := posInfo.CalcProfitUnreal(pCache)

// 	// 未实现盈亏会把风险准备减成负数时候，强制跳过，防止穿仓补贴过多
// 	logrus.Info(0, bs.contractCode, liquidation.UID, "riskMargin", riskMargin, "lossUnReal", lossUnReal)
// 	if riskMargin.Add(lossUnReal).LessThanOrEqual(decimal.Zero) {
// 		skipRiskMargin = true
// 	}

// 	// 风险准备金负数
// 	if !skipRiskMargin && riskMargin.LessThanOrEqual(decimal.Zero) {
// 		// 对手方强制减仓
// 		err = bs.forceRivalBurst(liquidation, posInfo, reduceAmount)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, liquidation.UID, "CheckDeal forceRivalBurst failed")
// 			return err
// 		}
// 		fmt.Println("------ ForceRivalBurst")
// 		// todo 强制对手方失败处理
// 		time.Sleep(time.Second * 2)

// 		reduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Error(0, "checkDeal fetchDownLevelAmount error:", err)
// 			return err
// 		}

// 		return bs.checkDeal(liquidation, posInfo, true, cancelType, liquidationType, reduceAmount)
// 	}

// 	fmt.Println("------", 6)

// 	reduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 	if err != nil {
// 		logrus.Error(0, "checkDeal fetchDownLevelAmount error:", err)
// 		return err
// 	}

// 	err = bs.depth5Close(liquidation, posInfo, reduceAmount)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "CheckDeal depth5Close error:", err)
// 	}

// 	reduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 	if err != nil {
// 		logrus.Error(0, "checkDeal fetchDownLevelAmount error:", err)
// 		return err
// 	}

// 	// 对手方强制减仓
// 	err = bs.forceRivalBurst(liquidation, posInfo, reduceAmount)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "CheckDeal forceRivalBurst failed", err)
// 		return err
// 	}
// 	fmt.Println("------ ForceRivalBurst", 7)
// 	// todo 强制对手方失败处理
// 	time.Sleep(time.Second * 2)

// 	reduceAmount, err = bs.fetchCloseAmount(liquidation, posInfo)
// 	if err != nil {
// 		logrus.Error(0, "checkDeal fetchDownLevelAmount error:", err)
// 		return err
// 	}

// 	return bs.checkDeal(liquidation, posInfo, true, cancelType, liquidationType, reduceAmount)
// }

// func (bs *workingBurstService) forceRivalBurst(liquidation cache.LiquidationInfo, posInfo repository.PosSwap, reduceAmount decimal.Decimal) error {
// 	// 更新仓位
// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "forceRivalBurst updatePosInfo error:", err)
// 		return err
// 	}

// 	// 检查是否有仓位
// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		closeAmount, err := bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Info(0, "closeUserPos fetchCloseAmount error", err)
// 			return errors.New("closeUserPos fetchCloseAmount error")
// 		}
// 		reduceAmount = closeAmount
// 		if reduceAmount.IsZero() {
// 			return nil
// 		}
// 		if posInfo.Pos.IsZero() && posInfo.MarginMode == int32(domain.MarginModeCross) {
// 			switch posInfo.PosSide {
// 			case domain.LongPos:
// 				posInfo = assetInfo.ShortPos

// 			case domain.ShortPos:
// 				posInfo = assetInfo.LongPos

// 			default:

// 			}
// 		}
// 		fallthrough

// 	default:
// 		if posInfo.Pos.IsZero() {
// 			return nil
// 		}

// 	}

// 	// 更新爆仓查询强制减仓状态
// 	burstEvent := monitor.BurstEvent{
// 		UID:       liquidation.UID,
// 		BurstId:   liquidation.BurstId,
// 		BurstTime: time.Now().UnixNano(),
// 	}
// 	rivalSide := domain.ShortPos
// 	tradeSide := match.SideSell
// 	tradeAmount := assetInfo.LongPos.Pos
// 	// 体验金仓位
// 	if liquidation.IsTrialPos {
// 		tradeAmount = assetInfo.TrialLongPos.Pos
// 	}
// 	rivalListKey := cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)

// 	switch posInfo.PosSide {
// 	case domain.ShortPos:
// 		rivalSide = domain.LongPos
// 		tradeSide = match.SideBuy
// 		tradeAmount = assetInfo.ShortPos.Pos
// 		// 体验金仓位
// 		if liquidation.IsTrialPos {
// 			tradeAmount = assetInfo.TrialShortPos.Pos
// 		}
// 		rivalListKey = cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)

// 	case domain.BothPos:
// 		if assetInfo.BothPos.Pos.LessThan(decimal.Zero) {
// 			rivalSide = domain.LongPos
// 			tradeSide = match.SideBuy
// 			tradeAmount = assetInfo.BothPos.Pos.Abs()
// 			// 体验金仓位
// 			if liquidation.IsTrialPos {
// 				tradeAmount = assetInfo.TrialBothPos.Pos.Abs()
// 			}
// 			rivalListKey = cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)
// 		}

// 	default:

// 	}

// 	if reduceAmount.GreaterThan(decimal.Zero) {
// 		tradeAmount = reduceAmount
// 	}

// 	if tradeAmount.LessThanOrEqual(decimal.Zero) {
// 		logrus.Error(0, "ForceRivalBurst tradeAmount is zero:", tradeAmount)
// 		return errors.New("ForceRivalBurst tradeAmount is zero")
// 	}

// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "depth5Close ContractSettings not exists")
// 		return nil
// 	}
// 	reduceAmount = reduceAmount.Truncate(settingInfo.AmountPrecision)

// 	rivalUsers, err := redisCli.ZRevRangeByScoreWithScores(rivalListKey, 0, 500)
// 	if err != nil {
// 		logrus.Error(0, "ForceRivalBurst ZRevRangeByScoreWithScores error:", err)
// 		return errors.New("ForceRivalBurst ZRevRangeByScoreWithScores error")
// 	}
// 	if len(rivalUsers) < 1 {
// 		logrus.Error(0, bs.contractCode, "ForceRivalBurst no rival user from", rivalListKey)
// 	}
// 	for _, userInfo := range rivalUsers {
// 		if rivalUserId, ok := userInfo.Member.(string); ok {
// 			// 检查是否减掉体验金仓位,体验金仓位不减仓
// 			// 减仓的value的格式: 真实仓位=uid 体验金仓位=uid-trial
// 			// isRivalTrialPos := false
// 			if strings.Contains(rivalUserId, "-trial") {
// 				continue
// 			}

// 			userLiquidationType := liquidation.LiquidationType
// 			rivalUserLiquidationType := liquidation.LiquidationType
// 			switch userLiquidationType {
// 			case domain.LiquidationTypeBurst, domain.LiquidationTypeReduce:
// 				if liquidation.UID != rivalUserId {
// 					rivalUserLiquidationType = domain.LiquidationTypeStopProfitReduce
// 				}

// 			default:

// 			}

// 			_, rivalAsset, err := swapcache.GetUserCacheData(bs.base, bs.quote, rivalUserId)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, rivalUserId, "CheckDeal GetUserCacheData error:", err)
// 				return errors.New("CheckDeal GetUserCacheData error")
// 			}

// 			rivalPos := repository.PosSwap{}
// 			switch rivalAsset.PositionMode {
// 			case domain.HoldModeBoth:
// 				rivalPos = rivalAsset.BothPos

// 			default:
// 				switch rivalSide {
// 				case domain.ShortPos:
// 					rivalPos = rivalAsset.ShortPos

// 				case domain.LongPos:
// 					rivalPos = rivalAsset.LongPos

// 				default:
// 				}
// 			}

// 			pricePrecision := int32(domain.PricePrecision)
// 			if bs.coinPairInfo.PricePrecision > 0 {
// 				pricePrecision = bs.coinPairInfo.PricePrecision
// 			}
// 			lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 			if err != nil {
// 				logrus.Error(0, "ForceRivalBurst GetContractLastPrice error:", err)
// 				return err
// 			}
// 			reducePosParam := &match.ReducePositionsParams{
// 				UserSide: tradeSide,

// 				UID:                 liquidation.UID,
// 				UserLeverage:        posInfo.Leverage,
// 				UserMarginMode:      int(posInfo.MarginMode),
// 				UserLiquidationType: int(userLiquidationType),
// 				UserHoldMode:        assetInfo.PositionMode,
// 				UserIsMixMargin:     assetInfo.AssetMode,
// 				UserTimeInForce:     match.TimeInForceTypeGTC,
// 				UserAwardOpIds:      posInfo.AwardOpIds,

// 				TargetUserId:              rivalUserId,
// 				TargetUserLeverage:        rivalPos.Leverage,
// 				TargetUserMarginMode:      int(rivalPos.MarginMode),
// 				TargetUserLiquidationType: int(rivalUserLiquidationType),
// 				TargetUserHoldMode:        rivalAsset.PositionMode,
// 				TargetUserIsMixMargin:     rivalAsset.AssetMode,
// 				TargetUserTimeInForce:     match.TimeInForceTypeGTC,
// 				TargetUserAwardOpIds:      rivalPos.AwardOpIds,

// 				AccountType: match.AccountTypeSwap,
// 				Platform:    match.PlatformSystem,
// 				Base:        bs.base,
// 				Quote:       bs.quote,
// 				Price:       lastPrice.Truncate(pricePrecision),
// 				BurstId:     liquidation.BurstId,
// 				BurstTime:   liquidation.BurstTime,
// 			}
// 			if rivalPos.Pos.IsZero() {
// 				continue
// 			}

// 			if rivalPos.Pos.Equal(rivalPos.PosAvailable) {
// 				rivalTradeSide := domain.Buy
// 				if rivalSide == domain.LongPos {
// 					rivalTradeSide = domain.Sell
// 				}
// 				rivalLeverageInfo := rivalAsset.GetLeverage(bs.contractCode)
// 				_, _ = match.Service.ConditionCancel(rivalUserId, bs.base, bs.quote, domain.Close, rivalTradeSide, int32(rivalLeverageInfo.MarginMode), int32(domain.CancelTypeReduce), 0)
// 				time.Sleep(time.Second)
// 			}

// 			tempTradeAmount, err := bs.fetchCloseAmount(liquidation, posInfo)
// 			if err != nil {
// 				logrus.Error(0, "rivalAmount fetchCloseAmount error:", err)
// 			} else {
// 				tradeAmount = tempTradeAmount
// 			}
// 			tradeAmount = tradeAmount.RoundCeil(settingInfo.AmountPrecision)
// 			if tradeAmount.GreaterThan(posInfo.Pos.Abs()) {
// 				tradeAmount = posInfo.Pos.Abs()
// 			}
// 			logrus.Info(0, fmt.Sprintf("forceRivalBurst user: %s, tradeAmount: %s pos: %s", rivalUserId, tradeAmount.String(), posInfo.Pos.Abs().String()))

// 			retryTimes := 0
// 		retryReduce:
// 			if rivalPos.PosAvailable.Abs().GreaterThanOrEqual(tradeAmount) {
// 				reducePosParam.Amount = tradeAmount.Truncate(settingInfo.AmountPrecision)
// 				res, err := match.Service.BothReducePositions(reducePosParam)
// 				logrus.Info(0, "ForceRivalBurst BothReducePositions returned:", fmt.Sprintf("%+v", res), "err", err)
// 				if err != nil {
// 					logrus.Error(0, "rivalAmount > 0 ForceRivalBurst BothReducePositions error:", err)
// 				}
// 				logrus.Info(0, "rivalAmount.GreaterThanOrEqual(tradeAmount)", fmt.Sprintf("%+v", res))
// 				if res.Code == 200 {
// 					// 发送减仓盈利方通知
// 					go util.SendMailAndSmsToNoticeBurst(rivalUserId, bs.contractCode, domain.MSForceReduceTarget, "", decimal.Zero)
// 					// 更新爆仓记录  是否对手方减仓 和 对手方减仓数量
// 					burstEvent.ForceRivalAmount = tradeAmount.Truncate(domain.CurrencyPrecision)
// 					jsonBytes, _ := json.Marshal(burstEvent)
// 					swapcache.SendSwapTask(bs.contractCode, cache.SwapBurstTask{
// 						Type:     cache.TaskTypeBurstEvent,
// 						Data:     jsonBytes,
// 						FuncName: "UpdateBurstIsForceRival",
// 					})
// 					return errors.New(fmt.Sprintln("ForceRivalBurst BothReducePositions failed", fmt.Sprintf("%+v", res)))
// 				} else {
// 					logrus.Error(0, liquidation.UID, fmt.Sprintf("rivalAmount > 0 ForceRivalBurst BothReducePositions: %+v %+v", res, reducePosParam), err)
// 				}
// 				if res.Code == domain.Code251119 && retryTimes < 5 {
// 					time.Sleep(time.Second)
// 					retryTimes += 1
// 					goto retryReduce
// 				} else {
// 					return errors.New("ForceRivalBurst BothReducePositions retry times limit 5")
// 				}
// 			} else {
// 				reducePosParam.Amount = rivalPos.Pos.Abs()
// 				res, err := match.Service.BothReducePositions(reducePosParam)
// 				logrus.Error(0, "ForceRivalBurst BothReducePositions failed:", fmt.Sprintf("%+v", res), "err", err)
// 				if err != nil {
// 					logrus.Error(0, "ForceRivalBurst BothReducePositions error:", err)
// 				}
// 				logrus.Info(0, "rivalAmount.LessThan(tradeAmount)", fmt.Sprintf("%+v", res))
// 				if res.Code == 200 {
// 					// 发送减仓盈利方通知
// 					go util.SendMailAndSmsToNoticeBurst(rivalUserId, bs.contractCode, domain.MSForceReduceTarget, "", decimal.Zero)
// 					// 更新爆仓记录  是否对手方减仓 和 对手方减仓数量
// 					burstEvent.ForceRivalAmount = rivalPos.PosAvailable.Abs().Truncate(domain.CurrencyPrecision)
// 					jsonBytes, _ := json.Marshal(burstEvent)
// 					swapcache.SendSwapTask(bs.contractCode, cache.SwapBurstTask{
// 						Type:     cache.TaskTypeBurstEvent,
// 						Data:     jsonBytes,
// 						FuncName: "UpdateBurstIsForceRival",
// 					})

// 					tradeAmount = tradeAmount.Sub(reducePosParam.Amount)
// 				} else {
// 					logrus.Error(0, liquidation.UID, fmt.Sprintf("ForceRivalBurst BothReducePositions: %+v %+v", res, reducePosParam), err)
// 				}
// 				if res.Code == domain.Code251119 && retryTimes < 5 {
// 					time.Sleep(time.Second)
// 					retryTimes += 1
// 					goto retryReduce
// 				} else {
// 					logrus.Error(0, "ForceRivalBurst BothReducePositions failed retry > 5")
// 					return err
// 				}
// 			}
// 		}
// 	}

// 	return nil
// }

// func (bs *workingBurstService) depth5Close(liquidation cache.LiquidationInfo, posInfo repository.PosSwap, closeAmount decimal.Decimal) error {
// 	// 更新仓位
// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "depth5Close updatePosInfo error:", err)
// 		return err
// 	}

// 	// 检查是否有仓位
// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		reduceAmount, err := bs.fetchCloseAmount(liquidation, posInfo)
// 		if err != nil {
// 			logrus.Info(0, "closeUserPos fetchCloseAmount error", err)
// 			return errors.New("closeUserPos fetchCloseAmount error")
// 		}
// 		closeAmount = reduceAmount
// 		if closeAmount.IsZero() {
// 			return nil
// 		}
// 		if posInfo.Pos.IsZero() && posInfo.MarginMode == int32(domain.MarginModeCross) {
// 			switch posInfo.PosSide {
// 			case domain.LongPos:
// 				posInfo = assetInfo.ShortPos

// 			case domain.ShortPos:
// 				posInfo = assetInfo.LongPos

// 			default:

// 			}
// 		}
// 		fallthrough

// 	default:
// 		if posInfo.Pos.IsZero() {
// 			return nil
// 		}

// 	}

// 	lastPrice, err := match.Service.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "depth5Close get match last price error:", err)
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}
// 	if lastPrice.IsZero() {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "depth5Close last price is zero", err)
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}

// 	logrus.Info(0, "depth5Close lastPrice check done", lastPrice)

// 	closeOrderSide := domain.Sell
// 	closePosSide := domain.Long
// 	switch posInfo.PosSide {
// 	case domain.ShortPos:
// 		closeOrderSide = match.SideBuy
// 		closePosSide = domain.Short
// 	case domain.BothPos:
// 		if posInfo.Pos.LessThan(decimal.Zero) {
// 			closeOrderSide = match.SideBuy
// 			closePosSide = domain.Short
// 		}

// 	default:

// 	}

// 	closePrice := decimal.Zero
// 	sellPrice, buyPrice, err := match.Service.GetDepthPrice(&match.DepthPriceParams{
// 		AccountType: match.AccountTypeSwap,
// 		Base:        bs.base,
// 		Quote:       bs.quote,
// 		Depth:       5,
// 	})
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "depth5Close get depth 5 price error:", err)
// 		return err
// 	}

// 	// 确定平仓数量
// 	if closeAmount.LessThanOrEqual(decimal.Zero) {
// 		closeAmount = posInfo.Pos.Abs()
// 	}

// 	switch closeOrderSide {
// 	case domain.Sell:
// 		closePrice = sellPrice
// 		if closePrice.Div(lastPrice).LessThanOrEqual(decimal.NewFromFloat(1.05)) &&
// 			closePrice.Div(lastPrice).GreaterThanOrEqual(decimal.NewFromFloat(0.95)) {
// 			success, _ := match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, int32(closePosSide), int32(closeOrderSide), posInfo.MarginMode, int32(liquidation.CancelType), 0)
// 			if !success {
// 			}
// 			time.Sleep(time.Second)
// 		} else {
// 			errStr := fmt.Sprintln(bs.contractCode, "depth5Close sellPrice(", sellPrice, ") overflow 5% lastPrice(", lastPrice, ") error")
// 			logrus.Error(0, errStr)
// 			time.Sleep(time.Millisecond * 200)

// 			err = bs.forceRivalBurst(liquidation, posInfo, closeAmount)
// 			if err != nil {
// 				logrus.Error(0, err)
// 				return errors.New(errStr)
// 			}
// 			return nil
// 		}

// 	case domain.Buy:
// 		closePrice = buyPrice
// 		if closePrice.Div(lastPrice).LessThanOrEqual(decimal.NewFromFloat(1.05)) &&
// 			closePrice.Div(lastPrice).GreaterThanOrEqual(decimal.NewFromFloat(0.95)) {
// 			success, _ := match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, int32(closePosSide), int32(closeOrderSide), posInfo.MarginMode, int32(liquidation.CancelType), 0)
// 			if !success {
// 			}
// 			time.Sleep(time.Second)
// 		} else {
// 			errStr := fmt.Sprintln(bs.contractCode, "depth5Close buyPrice(", buyPrice, ") overflow 5% lastPrice(", lastPrice, ") error")
// 			logrus.Error(0, errStr)
// 			time.Sleep(time.Millisecond * 200)

// 			err = bs.forceRivalBurst(liquidation, posInfo, closeAmount)
// 			if err != nil {
// 				logrus.Error(0, err)
// 				return errors.New(errStr)
// 			}
// 			return nil
// 		}

// 	default:

// 	}

// 	if closeAmount.LessThanOrEqual(decimal.Zero) || closePrice.LessThanOrEqual(decimal.Zero) {
// 		return errors.New(fmt.Sprintln("closeAmount:", closeAmount, "or", "closePrice:", closePrice, "less than or equal zero"))
// 	}

// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "depth5Close ContractSettings not exists")
// 		return nil
// 	}
// 	closeAmount = closeAmount.RoundCeil(settingInfo.AmountPrecision)
// 	if closeAmount.Abs().GreaterThan(posInfo.Pos.Abs()) {
// 		closeAmount = posInfo.Pos.Abs()
// 	}

// 	logrus.Info(0, fmt.Sprintf("depth5Close fetch closeAmount user: %s closeAmount: %s closePrice: %s", liquidation.UID, closeAmount, closePrice))
// 	if closeAmount.GreaterThan(decimal.Zero) && closePrice.GreaterThan(decimal.Zero) {
// 		closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 			PositionParams: match.PositionParams{
// 				Side:         closeOrderSide,
// 				PosSide:      closePosSide,
// 				IsLimitOrder: match.IsLimitOrderYes,
// 				OrderType:    match.OrderTypeNormal,
// 				Price:        closePrice,
// 				Amount:       closeAmount.Truncate(domain.CurrencyPrecision),
// 				TriggerPrice: decimal.Zero,
// 			},
// 			UID:             liquidation.UID,
// 			AccountType:     match.AccountTypeSwap,
// 			Base:            bs.base,
// 			Quote:           bs.quote,
// 			Leverage:        posInfo.Leverage,
// 			Platform:        match.PlatformSystem,
// 			LiquidationType: int(liquidation.LiquidationType),
// 			MarginMode:      int(posInfo.MarginMode),
// 			TimeInForce:     match.TimeInForceTypeGTC,
// 			PositionMode:    assetInfo.PositionMode,
// 			IsMixMargin:     assetInfo.AssetMode,
// 			BurstId:         liquidation.BurstId,
// 			BurstTime:       liquidation.BurstTime,
// 			Depth:           5,
// 			AwardOpIds:      posInfo.AwardOpIds,
// 		})
// 		if err != nil {
// 			logrus.Error(0, "depth5Close ClosePositions error:", err)
// 			return err
// 		}
// 		if closeResponse.Code != 200 {
// 			closeFlag := "long"
// 			if closeOrderSide == match.SideBuy {
// 				closeFlag = "short"
// 			}
// 			logrus.Error(0, bs.contractCode, "depth5Close ClosePositions", closeFlag, "pos fail:", fmt.Sprintf("%+v", closeResponse))
// 			return errors.New("depth5Close failed")
// 		}

// 		time.Sleep(time.Second * 2)

// 		_, _ = match.Service.ConditionCancel(liquidation.UID, bs.base, bs.quote, domain.Close, 0, int32(liquidation.MarginMode), int32(liquidation.CancelType), 0)

// 	}

// 	return nil
// }

// func (bs *workingBurstService) selfUnlock(liquidation cache.LiquidationInfo, marginMode domain.MarginMode, posSide int) {
// 	// 自解锁（只用于减仓结束）
// 	// 更新爆仓状态
// 	burstEvent := monitor.BurstEvent{
// 		UID:          liquidation.UID,
// 		MarginMode:   liquidation.MarginMode,
// 		ContractCode: bs.contractCode,
// 		BurstId:      liquidation.BurstId,
// 		BurstTime:    liquidation.BurstTime,
// 		IsTrialPos:   liquidation.IsTrialPos,
// 	}
// 	unlockParams := cachelock.BurstLockParams{
// 		ContractCode: bs.contractCode,
// 		Liquidation: cache.LiquidationInfo{
// 			UID:        liquidation.UID,
// 			MarginMode: liquidation.MarginMode,
// 			IsTrialPos: liquidation.IsTrialPos,
// 		},
// 	}
// 	if marginMode == domain.MarginModeIsolated {
// 		burstEvent.PosSide = int32(posSide)
// 		unlockParams.Liquidation.PosSide = int32(posSide)
// 	}
// 	jsonBytes, _ := json.Marshal(burstEvent)
// 	swapcache.SendSwapTask(bs.contractCode, cache.SwapBurstTask{
// 		Type:     cache.TaskTypeBurstEvent,
// 		Data:     jsonBytes,
// 		FuncName: "UpdateBurstStatus",
// 	})

// 	time.Sleep(time.Second)

// 	// 手动强制解锁
// 	err := cachelock.UnlockBurstUser(unlockParams, false)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "burstTradeWork UnlockBurstUser error:", err, "params:", fmt.Sprintf("%+v", unlockParams))
// 	}
// }

// func (bs *workingBurstService) updatePosInfo(posInfo repository.PosSwap) (*repository.AssetSwap, repository.PosSwap, error) {
// 	_, assetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, posInfo.UID)
// 	if err != nil {
// 		return nil, posInfo, err
// 	}
// 	// 判断是否体验金仓位
// 	if posInfo.IsTrial() {
// 		switch posInfo.PosSide {
// 		case domain.LongPos:
// 			return assetInfo, assetInfo.TrialLongPos, nil

// 		case domain.ShortPos:
// 			return assetInfo, assetInfo.TrialShortPos, nil

// 		case domain.BothPos:
// 			return assetInfo, assetInfo.TrialBothPos, nil

// 		default:
// 		}
// 	} else {
// 		switch posInfo.PosSide {
// 		case domain.LongPos:
// 			return assetInfo, assetInfo.LongPos, nil

// 		case domain.ShortPos:
// 			return assetInfo, assetInfo.ShortPos, nil

// 		case domain.BothPos:
// 			return assetInfo, assetInfo.BothPos, nil

// 		default:
// 		}
// 	}

// 	return nil, posInfo, nil
// }

// func (bs *workingBurstService) fetchCloseAmount(liquidation cache.LiquidationInfo, posInfo repository.PosSwap) (decimal.Decimal, error) {
// 	// 更新仓位
// 	assetInfo, posInfo, err := bs.updatePosInfo(posInfo)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, liquidation.UID, "fetchDownLevelAmount updatePosInfo error:", err)
// 		return decimal.Zero, err
// 	}

// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "fetchCloseAmount ContractSettings not exists")
// 		return decimal.Zero, err
// 	}

// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		pCache := price.New()
// 		markPrice := pCache.GetMarkPrice(bs.contractCode)
// 		if markPrice.IsZero() {
// 			logrus.Error(0, bs.contractCode, assetInfo.UID, "fetchDownLevelAmount markPrice is zero")
// 			return decimal.Zero, errors.New("fetchDownLevelAmount markPrice is zero")
// 		}

// 		switch liquidation.MarginMode {
// 		case domain.MarginModeCross:
// 			posValue := assetInfo.LongPos.CalcPosHoldValue().
// 				Add(assetInfo.ShortPos.CalcPosHoldValue().
// 					Add(assetInfo.BothPos.CalcPosHoldValue()))
// 			if posValue.GreaterThan(liquidation.TargetLimit.Mul(decimal.NewFromFloat(0.987))) {
// 				liquidationAmount := posValue.Sub(liquidation.TargetLimit).Div(markPrice).Truncate(settingInfo.AmountPrecision)
// 				if liquidationAmount.IsZero() {
// 					liquidationAmount = settingInfo.MinAmount
// 				}
// 				return liquidationAmount, nil
// 			}

// 		case domain.MarginModeIsolated:
// 			posValue := posInfo.CalcPosHoldValue()
// 			if posValue.GreaterThan(liquidation.TargetLimit.Mul(decimal.NewFromFloat(0.987))) {
// 				liquidationAmount := posValue.Sub(liquidation.TargetLimit).Div(markPrice).Truncate(settingInfo.AmountPrecision)
// 				if liquidationAmount.IsZero() {
// 					liquidationAmount = settingInfo.MinAmount
// 				}
// 				return liquidationAmount, nil
// 			}

// 		default:

// 		}

// 	case domain.LiquidationTypeBurst:
// 		return posInfo.Pos.Abs(), nil

// 	default:

// 	}

// 	return decimal.Zero, nil
// }
