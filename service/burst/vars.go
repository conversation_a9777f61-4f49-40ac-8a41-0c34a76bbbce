package burst

import (
	"context"
	"sync"

	"futures-asset/pkg/setting"
	"futures-asset/pkg/user"
)

var (
	RunningServices struct {
		sync.RWMutex
		Map map[string]*workingBurstService
	}

	snapCronContracts struct {
		sync.RWMutex
		List []string
	}
	snapCronContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	snapScannerContracts struct {
		sync.RWMutex
		List []string
	}
	snapScannerContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	snapProcessorContracts struct {
		sync.RWMutex
		List []string
	}
	snapProcessorContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	cacheBurstLevels struct {
		sync.RWMutex
		Map map[string]setting.LevelHoldSortList
	}
	ContractSettings struct {
		sync.RWMutex
		Map map[string]setting.ContractPair
	}
	PlatformUserLevelRate struct {
		sync.RWMutex
		Map map[string]user.LevelRate
	}
	DefaultUserLevelRate struct {
		sync.RWMutex
		Map map[int]setting.UserLevelInfo
	}
)
