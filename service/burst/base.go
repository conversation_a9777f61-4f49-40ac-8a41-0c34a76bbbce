package burst

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"os"
// 	"reflect"
// 	"runtime"
// 	"runtime/debug"
// 	"sort"
// 	"strconv"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/cache/cachelock"
// 	"futures-asset/cache/price"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/push"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/internal/message"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/pkg/user"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// func init() {
// 	RunningServices.Map = make(map[string]*workingBurstService)
// 	ContractSettings.Map = make(map[string]setting.ContractPair)
// 	PlatformUserLevelRate.Map = make(map[string]user.LevelRate)
// 	DefaultUserLevelRate.Map = make(map[int]setting.UserLevelInfo)
// 	snapCronContracts.List = make([]string, 0)
// 	snapCronContext.Map = make(map[string][]context.CancelFunc)
// 	snapScannerContracts.List = make([]string, 0)
// 	snapScannerContext.Map = make(map[string][]context.CancelFunc)
// 	snapProcessorContracts.List = make([]string, 0)
// 	snapProcessorContext.Map = make(map[string][]context.CancelFunc)
// 	cacheBurstLevels.Map = make(map[string]setting.LevelHoldSortList)
// }

// func NewBurstService(_base, _quote, _currency string, _checkEs bool) *workingBurstService {
// 	_base, _quote = strings.ToUpper(_base), strings.ToUpper(_quote)
// 	coinPairSetting, err := setting.Service.GetPairSettingInfo(_base, _quote)
// 	if err != nil {
// 		logrus.Error(0, fmt.Sprintln(fmt.Sprintf("%s-%s", _base, _quote), "NewBurstService GetPairSettingInfo error:", err))
// 		return nil
// 	}
// 	if coinPairSetting == nil {
// 		logrus.Error(0, fmt.Sprintln(fmt.Sprintf("%s-%s", _base, _quote), "NewBurstService GetPairSettingInfo is nil"))
// 		coinPairSetting = &setting.ContractPair{}
// 	}

// 	burstWorker := &workingBurstService{
// 		RWMutex:      sync.RWMutex{},
// 		base:         _base,
// 		quote:        _quote,
// 		contractCode: fmt.Sprintf("%s-%s", _base, _quote),
// 		currency:     _currency,
// 		coinPairInfo: *coinPairSetting,
// 		lastTicker: swapMirrorTicker{
// 			RWMutex:   new(sync.RWMutex),
// 			LastPrice: decimal.Zero,
// 		},
// 		ticker:    make(chan interface{}, 0),
// 		userGroup: make([]chan []string, 0),
// 	}
// 	return burstWorker
// }

// func buildService(_symbol string) *workingBurstService {
// 	base, quote := util.BaseQuote(_symbol)
// 	if len(quote) < 1 {
// 		logrus.Error(0, "buildService", _symbol, "error:", _symbol, "is not symbol")
// 		return nil
// 	}
// 	// 启动时清理禁用币对残留数据
// 	closeSymbolUserList := cache.GetCloseAllUserPosListRedisKey(base, quote)
// 	deleteNum, err := redisCli.DelKey(closeSymbolUserList)
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, "buildService", _symbol, "delete", closeSymbolUserList, "error:", err)
// 		}
// 	}
// 	if deleteNum > 0 {
// 		logrus.Error(0, "buildService", _symbol, "delete", closeSymbolUserList, "done")
// 	} else {
// 		logrus.Info(0, "buildService", _symbol, closeSymbolUserList, "not exists")
// 	}
// 	burstWorker := NewBurstService(base, quote, quote, true)
// 	if burstWorker == nil {
// 		logrus.Error(0, _symbol, "NewBurstService failed")
// 		return nil
// 	}
// 	RunningServices.Lock()
// 	RunningServices.Map[strings.ToUpper(_symbol)] = burstWorker
// 	RunningServices.Unlock()
// 	return burstWorker
// }

// func initBurstLevels(_contractCode string) (setting.LevelHoldSortList, error) {
// 	base, quote := util.BaseQuote(_contractCode)
// 	tempLevelHoldSortList, err := setting.Service.GetMarginRateLevel(base, quote)
// 	if err != nil {
// 		logrus.Error(0, _contractCode, "burst initBurstLevels GetMarginRateLevel error:", err, "tempLevelHoldSortList:", tempLevelHoldSortList)
// 		return make(setting.LevelHoldSortList, 0), err
// 	}
// 	if len(tempLevelHoldSortList) < 1 {
// 		logrus.Error(0, _contractCode, "burst initBurstLevels tempLevelList is empty")
// 		return make(setting.LevelHoldSortList, 0), err
// 	} else {
// 		sort.Sort(sort.Reverse(tempLevelHoldSortList))
// 		cacheBurstLevels.Lock()
// 		cacheBurstLevels.Map[_contractCode] = tempLevelHoldSortList
// 		cacheBurstLevels.Unlock()
// 	}
// 	return tempLevelHoldSortList, nil
// }

// func GetContractUserLevelRate(_userId string) (user.LevelRate, error) {
// 	userLevelRate := user.LevelRate{}
// 	PlatformUserLevelRate.RLock()
// 	userLevelRate, ok := PlatformUserLevelRate.Map[_userId]
// 	PlatformUserLevelRate.RUnlock()
// 	if !ok || time.Now().Unix()-userLevelRate.LastRequestTime > 60*15 {
// 		var err error
// 		userLevelRate, err = user.Service.GetUserLevelRate(_userId)
// 		if err != nil {
// 			logrus.Error(0, "GetGetContractUserLevelRateRedisKey", _userId, "GetUserLevelRate error:", err)
// 			return userLevelRate, err
// 		}
// 		PlatformUserLevelRate.Lock()
// 		userLevelRate.LastRequestTime = time.Now().Unix()
// 		PlatformUserLevelRate.Map[_userId] = userLevelRate
// 		PlatformUserLevelRate.Unlock()
// 		return userLevelRate, nil
// 	}
// 	return userLevelRate, nil
// }

// func updateAllCoinPairInfo() error {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "updateAllCoinPairInfo error:", err)
// 		}
// 	}()

// 	err := setting.Service.UpdateAllPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "updateAllCoinPairInfo redis UpdateAllPairInfo error:", err)
// 		return err
// 	}

// 	pairSettingMap, err := setting.Service.GetAllPairSettingInfo()
// 	if err != nil {
// 		logrus.Error(0, "updateAllCoinPairInfo GetAllPairSettingInfo error:", err, fmt.Sprintf("%+v", pairSettingMap))
// 		return err
// 	}

// 	for contractCode, contractSetting := range pairSettingMap {
// 		ContractSettings.Lock()
// 		ContractSettings.Map[contractCode] = contractSetting
// 		ContractSettings.Unlock()
// 	}
// 	return nil
// }

// func UpdateIndexPrice(_contractCode, _indexPrice string, _platforms []string) {
// 	err := redisCli.HSet(cachekey.GetPlatformIndexPriceRedisKey(), strings.ToUpper(_contractCode), _indexPrice)
// 	if err != nil {
// 		logrus.Error(0, _contractCode, "UpdateIndexPrice HSet error:", err, _indexPrice)
// 	}
// 	jsonBytes, err := json.Marshal(map[string]interface{}{
// 		"index_price": _indexPrice,
// 		"platforms":   _platforms,
// 		"type":        "index",
// 	})
// 	if err != nil {
// 		logrus.Error(0, _contractCode, "UpdateIndexPrice Marshal error:", err, _indexPrice, fmt.Sprintf("%+v", _platforms))
// 	} else {
// 		err = redisCli.HSet(cachekey.GetPlatformIndexPriceComposeInfoRedisKey(), strings.ToUpper(_contractCode), string(jsonBytes))
// 		if err != nil {
// 			logrus.Error(0, _contractCode, "UpdateIndexPrice HSet error:", err, _indexPrice, fmt.Sprintf("%+v", _platforms))
// 		}
// 		base, quote := util.BaseQuote(_contractCode)
// 		pushData := mqlib.NotifyData{
// 			NotifyTopic: push.GetIndexPriceTopic(base, quote),
// 			Data: mqlib.IndexPush{
// 				ContractCode: _contractCode,
// 				IndexPrice:   _indexPrice,
// 				Timestamp:    time.Now().Unix(),
// 			},
// 		}
// 		mqlib.PublishIndex(base, quote, pushData)
// 	}
// 	return
// }

// func (bs *workingBurstService) UpdateSyncFundRate(_contractCode string, _fundRateInfo map[string]string) {
// 	jsonBytes, err := json.Marshal(_fundRateInfo)
// 	if err != nil {
// 		logrus.Error(0, _contractCode, "UpdateSyncFundRate Marshal error:", err, fmt.Sprintf("%+v", _fundRateInfo))
// 	} else {
// 		err = redisCli.HSet(cachekey.GetPlatformFundRateRedisKey(), strings.ToUpper(_contractCode), string(jsonBytes))
// 		if err != nil {
// 			logrus.Error(0, _contractCode, "UpdateSyncFundRate HSet error:", err, fmt.Sprintf("%+v", _fundRateInfo))
// 		}
// 	}
// 	return
// }

// func CronCleanUserLevelMap(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Errorln("CronCleanUserLevelMap recover error:", err)
// 			logrus.Error(0, "CronCleanUserLevelMap recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			CronCleanUserLevelMap(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}
// 	logrus.Info(0, "burst cron clean user level started")
// 	defer logrus.Info(0, "burst cron clean user level stopped")

// 	Hour1Ticker := time.NewTicker(time.Hour)
// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		case <-Hour1Ticker.C:
// 			PlatformUserLevelRate.Lock()
// 			PlatformUserLevelRate.Map = make(map[string]user.LevelRate)
// 			PlatformUserLevelRate.Unlock()

// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// func UserLevelSettingListener() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "UserLevelSettingListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			UserLevelSettingListener()
// 		}
// 	}()

// 	logrus.Info(0, "burst user level setting listener started")
// 	defer logrus.Info(0, "burst user level setting listener stopped")

// 	subCli := redisCli.Subscribe("contract_swap_update_level_rate")
// 	logrus.Info(0, "redisCli Subscribe contract_swap_update_level_rate")

// 	for {
// 		_, err := subCli.ReceiveMessage(context.Background())
// 		if err != nil {
// 			logrus.Error(0, fmt.Sprintln("UserLevelSettingListener ReceiveMessage error:", err))
// 			continue
// 		}
// 		userLevelRate, err := setting.Service.GetUserLevelsRate()
// 		if err != nil {
// 			logrus.Error(0, fmt.Sprintln("UserLevelSettingListener GetUserLevelsRate error:", err))
// 			continue
// 		}
// 		DefaultUserLevelRate.Lock()
// 		for _, levelRateInfo := range userLevelRate.List {
// 			DefaultUserLevelRate.Map[levelRateInfo.Level] = levelRateInfo
// 		}
// 		DefaultUserLevelRate.Unlock()
// 		runtime.Gosched()
// 	}
// }

// func UserLevelRateListener() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "UserLevelRateListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			UserLevelRateListener()
// 		}
// 	}()

// 	logrus.Info(0, "burst user level rate listener started")
// 	defer logrus.Info(0, "burst user level rate listener stop")

// 	userLevelRate, err := setting.Service.GetUserLevelsRate()
// 	if err != nil {
// 		logrus.Error(0, fmt.Sprintln("UserLevelRateListener GetUserLevelsRate error:", err))
// 	}
// 	DefaultUserLevelRate.Lock()
// 	for _, levelRateInfo := range userLevelRate.List {
// 		DefaultUserLevelRate.Map[levelRateInfo.Level] = levelRateInfo
// 	}
// 	DefaultUserLevelRate.Unlock()

// 	subCli := redisCli.Subscribe("User_Swap_Level_Rate_Update")
// 	logrus.Info(0, "redisCli Subscribe User_Swap_Level_Rate_Update")
// 	for {
// 		msg, err := subCli.ReceiveMessage(context.Background())
// 		if err != nil {
// 			panic(fmt.Sprintln("UserLevelRateListener ReceiveMessage error:", err))
// 		}

// 		levelRate := user.LevelRate{}
// 		err = json.Unmarshal([]byte(msg.Payload), &levelRate)
// 		if err != nil {
// 			logrus.Error(0, "UserLevelRateListener Unmarshal err:", err)
// 			continue
// 		}
// 		logrus.Info(0, "UserLevelRateListener levelRate:", fmt.Sprintf("%+v", levelRate))
// 		PlatformUserLevelRate.Lock()
// 		if levelRate.UID == "-1" {
// 			PlatformUserLevelRate.Map = make(map[string]user.LevelRate)
// 		} else {
// 			PlatformUserLevelRate.Map[levelRate.UID] = levelRate
// 		}
// 		PlatformUserLevelRate.Unlock()
// 		runtime.Gosched()
// 	}
// }

// func (bs *workingBurstService) Close() {
// 	bs.closeFlag = true
// 	for {
// 		if !bs.working {
// 			break
// 		}
// 	}
// 	close(bs.ticker)
// 	return
// }

// func (bs *workingBurstService) _serviceIsExpired() bool {
// 	ContractSettings.RLock()
// 	settingInfo := ContractSettings.Map[bs.contractCode]
// 	ContractSettings.RUnlock()
// 	return (settingInfo.State == domain.ContractDisablePre || settingInfo.State == domain.ContractDisable) && settingInfo.ForceLiquidationTime > 0 &&
// 		(time.Now().Unix() > settingInfo.ForceLiquidationTime)
// }

// func _forcePushRivalScoreZero(_userId string, _contractCode string, _posSide int32, _isTrialPos bool) {
// 	// 推送需要对手方比率
// 	message.New(_userId, message.PosQueueIndex, mqlib.CommonAmqp).
// 		TopicRivalRatePrice(message.SwapAccount).
// 		Push(map[string]interface{}{
// 			"uid":            _userId,
// 			"isTrialPos":     _isTrialPos,
// 			"contractCode":   _contractCode,
// 			"posSide":        _posSide,
// 			"rivalScoreRate": decimal.Zero.String(),
// 		})

// 	return
// }

// // _logBurstData 记录触发爆仓记录（用于记录线上爆仓锁定用户，没有爆仓记录的情况）
// func (bs *workingBurstService) _logBurstData(_contractCode string, _holdingMarginRate decimal.Decimal, _holdingMargin decimal.Decimal, _burstListRedisKey string, _jsonBytes []byte) {
// 	logFile := fmt.Sprintf("/data/burst/logs/burst_record_%s.log", time.Now().Format("********"))
// 	f, err := os.OpenFile(logFile, os.O_CREATE|os.O_APPEND|os.O_WRONLY, os.ModePerm)
// 	if err != nil {
// 		logrus.Error(0, "burstService logBurstData OpenFile error:", err, logFile)
// 		return
// 	}
// 	defer f.Close()
// 	logString := fmt.Sprintln(time.Now(),
// 		"contractCode:", _contractCode,
// 		"holdingMarginRate:", _holdingMarginRate,
// 		"holdingMargin:", _holdingMargin,
// 		"burstListRedisKey:", _burstListRedisKey,
// 		"jsonString:", string(_jsonBytes))
// 	_, err = f.WriteString(logString)
// 	if err != nil {
// 		logrus.Error(0, "burstService logBurstData WriteString error:", err, logFile, logString)
// 	}
// 	return
// }

// // GetUserCacheAndAssetInfo 获取用户资产信息
// //
// //	Params:
// //	  _userId string: 用户id
// //	Return:
// //	  0 *swapcache.PosCache: 用户缓存信息
// //	  1 *repository.AssetSwap: 用户资产信息
// //	  2 error: 错误信息
// func (bs *workingBurstService) GetUserCacheAndAssetInfo(_userId string) (*swapcache.PosCache, *repository.AssetSwap, error) {
// 	// 获取用户
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: repository.TradeCommon{
// 			Base:  bs.base,
// 			Quote: bs.quote,
// 		},
// 	}, _userId)
// 	assetInfo, err := userCache.Load()
// 	if err != nil {
// 		return nil, nil, err
// 	}
// 	return userCache, assetInfo, err
// }

// func (bs *workingBurstService) _composeCurrentUserBurstPosByPosSide(base, quote, uid, burstId string, _posSide int32, settingInfo setting.ContractPair, isTrialPos bool) (*burstPosInfo, *repository.AssetSwap) {
// 	_base, _quote := strings.ToUpper(base), strings.ToUpper(quote)
// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(_base, _quote, uid)
// 	if err != nil {
// 		logrus.Error(0, _base, _quote, "composeCurrentUserBurstPosByPosSide GetUserCacheData error:", err, "currentUserId:", uid)
// 		return nil, nil
// 	}

// 	var posInfo repository.PosSwap
// 	if assetInfo.PositionMode == domain.HoldModeBoth {
// 		posInfo = assetInfo.BothPos
// 		if isTrialPos {
// 			posInfo = assetInfo.TrialBothPos
// 		}
// 	} else {
// 		switch _posSide {
// 		case domain.LongPos:
// 			posInfo = assetInfo.LongPos
// 			if isTrialPos {
// 				posInfo = assetInfo.TrialLongPos
// 			}
// 		case domain.ShortPos:
// 			posInfo = assetInfo.ShortPos
// 			if isTrialPos {
// 				posInfo = assetInfo.TrialShortPos
// 			}
// 		default:
// 		}
// 	}

// 	logrus.Info(0, "~~ compose posInfo", fmt.Sprintf("%+v", posInfo), "assetInfo", fmt.Sprintf("%+v", assetInfo))

// 	pCache := price.New()
// 	var levelFilter setting.LevelFilter
// 	switch domain.MarginMode(posInfo.MarginMode) {
// 	case domain.MarginModeCross:
// 		_, _, _, levelFilterMap, err := userCache.TotalCrossMaintainMargin(pCache, "")
// 		if err != nil {
// 			logrus.Error(0, "composeCurrentUserBurstPosByPosSide TotalCrossMaintainMargin", err)
// 		}
// 		levelFilter = levelFilterMap[posInfo.ContractCode]

// 	case domain.MarginModeIsolated:
// 		posValue := posInfo.CalcPosValue(pCache)
// 		// 仓位级别及维持保证金率
// 		levelFilter, _, err = setting.FetchMarginLevel(_base, _quote, posValue)
// 		if err != nil {
// 			logrus.Error(0, "composeCurrentUserBurstPosByPosSide pos info FetchMarginLevel error:", err)
// 			return nil, nil
// 		}

// 	default:

// 	}

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			return nil, nil
// 		}
// 	}

// 	// 制作爆仓信息
// 	burstInfo := new(burstPosInfo)
// 	burstInfo._updatePos(burstId, assetInfo, userCache, settingInfo, posInfo, []repository.PosSwap{},
// 		levelFilter, time.Now().UnixNano(), pCache, userLevelRateInfo)

// 	return burstInfo, assetInfo
// }

// func UserBurstProcessorListener(marginMode domain.MarginMode, ctx context.Context, wg *sync.WaitGroup, isRecover bool, isTrialPos bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "UserBurstProcessorListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			UserBurstProcessorListener(marginMode, ctx, wg, true, isTrialPos)
// 		}
// 	}()

// 	if !isRecover {
// 		wg.Add(1)
// 	}

// 	_ctx := context.WithValue(ctx, "name", "UserBurstProcessorListener")
// 	logrus.Info(0, _ctx.Value("name"), "start")
// 	defer logrus.Info(0, _ctx.Value("name"), "stop")

// 	burstWorkingUsersSet := cachekey.GetBurstWorkingUsersRedisKey(marginMode)
// 	if isTrialPos {
// 		burstWorkingUsersSet = cachekey.GetTrialBurstWorkingUsersRedisKey(marginMode)
// 	}
// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			// 从工作列表中获取所有爆仓中的用户id
// 			users, err := redisCli.Client().SMembers(context.Background(), burstWorkingUsersSet).Result()
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, "UserBurstProcessorListener SMembers", burstWorkingUsersSet, "error:", err)
// 				}
// 			} else {
// 				for _, uid := range users {
// 					_buildProcessor(uid, marginMode, isTrialPos)
// 					time.Sleep(time.Millisecond * 300)
// 				}
// 			}
// 			time.Sleep(time.Second)

// 		}
// 		runtime.Gosched()
// 	}

// 	wg.Done()
// }

// func _buildProcessor(uid string, marginMode domain.MarginMode, isTrialPos bool) {
// 	userBurstLockKey := cachekey.GetBurstLockRedisKey(uid, marginMode)
// 	burstWorkingReChecksSet := cachekey.GetBurstWorkingRecheckRedisKey(marginMode)
// 	if isTrialPos {
// 		userBurstLockKey = cachekey.GetTrialBurstLockRedisKey(uid, marginMode)
// 		burstWorkingReChecksSet = cachekey.GetTrialBurstWorkingRecheckRedisKey(marginMode)
// 	}

// 	// 获取当前用户所有爆仓锁
// 	lockMap, err := redisCli.Client().HGetAll(context.Background(), userBurstLockKey).Result()
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, "buildProcessor HKeys", userBurstLockKey, "error:", err)
// 		} else {
// 			// 如果当前用户没有爆仓锁，但是工作队列中还有，发送到复验环节
// 			err = redisCli.Client().SAdd(context.Background(), burstWorkingReChecksSet, uid).Err()
// 			if err != nil {
// 				logrus.Error(0, uid, "buildProcessor SAdd", burstWorkingReChecksSet, "error:", err)
// 			}
// 		}
// 	} else {
// 		for burstCode, liquidationData := range lockMap {
// 			// 如果爆仓锁是用户锁，说明是全仓爆仓。（其他的可能是全仓减仓，逐仓爆仓、逐仓减仓，逐仓需要在处理爆仓环节判断是爆仓还是减仓）
// 			if burstCode == uid {
// 				continue
// 			}

// 			// 旧数据有空的
// 			if len(burstCode) < 1 {
// 				continue
// 			}

// 			mode := "crossed"
// 			contractCode := burstCode
// 			posSide := int64(0)
// 			if marginMode == domain.MarginModeIsolated {
// 				mode = "isolated"
// 				burstCodeList := strings.Split(burstCode, "_")
// 				if len(burstCodeList) > 1 {
// 					contractCode = burstCodeList[0]
// 					posSide, err = strconv.ParseInt(burstCodeList[1], 10, 64)
// 					if err != nil {
// 						logrus.Error(0, uid, "buildProcessor ParseInt", burstCodeList[1], "error:", err)
// 					}
// 				}
// 			}

// 			// 写入爆仓触发状态
// 			setOk, err := updateBurstWorkStatus(marginMode, contractCode, posSide, uid, domain.BurstTrigged, isTrialPos)
// 			if err != nil {
// 				logrus.Error(0, contractCode, uid, "buildProcessor HSet error:", err)
// 			}

// 			if setOk {
// 				// 写入爆仓状态成功后，将用户id发送到执行队列。
// 				lPushBurstTask(marginMode, contractCode, posSide, uid, liquidationData)
// 				logrus.Info(0, uid, mode, contractCode, "Burst Trigged")
// 			} else {
// 				// 没有写入爆仓状态，获取爆仓状态
// 				burstStatus, err := getBurstWorkStatus(marginMode, contractCode, posSide, uid, isTrialPos)
// 				if err != nil {
// 					if err != redis.Nil {
// 						logrus.Error(0, contractCode, uid, "buildProcessor HGet error:", err)
// 					}
// 				} else {
// 					// 爆仓状态为处理完成时，发送到复验队列
// 					switch burstStatus {
// 					case domain.BurstProcessed:
// 						err = redisCli.Client().SAdd(context.Background(), burstWorkingReChecksSet, uid).Err()
// 						if err != nil {
// 							logrus.Error(0, contractCode, uid, "buildProcessor SAdd", burstWorkingReChecksSet, "error:", err)
// 						}

// 						_, err = updateBurstWorkStatus(marginMode, contractCode, posSide, uid, domain.BurstReChecking, isTrialPos)
// 						if err != nil {
// 							logrus.Error(0, contractCode, uid, "buildProcessor SAdd", burstWorkingReChecksSet, "error:", err)
// 						}

// 					case domain.BurstTaskError:
// 						err = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 							Liquidation: cache.LiquidationInfo{UID: uid, IsTrialPos: isTrialPos},
// 						}, true)
// 						if err != nil {
// 							logrus.Error(0, contractCode, uid, "buildProcessor UnlockBurstUser error:", err)
// 						} else {
// 							_, err := updateBurstWorkStatus(marginMode, contractCode, posSide, uid, domain.BurstReset, isTrialPos)
// 							if err != nil {
// 								logrus.Error(0, contractCode, uid, "buildProcessor updateBurstWorkStatus error:", err)
// 							}
// 						}

// 					default:

// 					}
// 				}
// 			}
// 		}
// 	}
// }

// func UserBurstMonitorListener(marginMode domain.MarginMode, ctx context.Context, wg *sync.WaitGroup, isRecover bool, isTrialPos bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "UserBurstMonitorListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			UserBurstMonitorListener(marginMode, ctx, wg, true, isTrialPos)
// 		}
// 	}()

// 	if !isRecover {
// 		wg.Add(1)
// 	}

// 	_ctx := context.WithValue(ctx, "name", "UserBurstMonitorListener")
// 	logrus.Info(0, _ctx.Value("name"), "start")
// 	defer logrus.Info(0, _ctx.Value("name"), "stop")

// 	burstWorkingReCheckUsersSet := cachekey.GetBurstWorkingRecheckRedisKey(marginMode)
// 	if isTrialPos {
// 		burstWorkingReCheckUsersSet = cachekey.GetTrialBurstWorkingRecheckRedisKey(marginMode)
// 	}

// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			users, err := redisCli.Client().SMembers(context.Background(), burstWorkingReCheckUsersSet).Result()
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, "UserBurstMonitorListener SMembers error:", err)
// 				}
// 			} else {
// 				for _, uid := range users {
// 					_monitorProcessor(uid, marginMode, isTrialPos)
// 				}
// 			}
// 		}
// 		runtime.Gosched()
// 		time.Sleep(time.Millisecond * 500)
// 	}

// 	wg.Done()
// }

// func _monitorProcessor(uid string, marginMode domain.MarginMode, isTrialPos bool) {
// 	userBurstStatusKey := cachekey.GetBurstWorkingStatusRedisKey(marginMode, uid)
// 	if isTrialPos {
// 		userBurstStatusKey = cachekey.GetTrialBurstWorkingStatusRedisKey(marginMode, uid)
// 	}
// 	statusMap, err := redisCli.Client().HGetAll(context.Background(), userBurstStatusKey).Result()
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, "buildProcessor HKeys error:", err)
// 		}
// 	} else {
// 		for contractCode, processStatus := range statusMap {
// 			switch processStatus {
// 			case strconv.Itoa(domain.BurstReChecking):
// 				reCheckBursTask(contractCode, uid, marginMode, isTrialPos)

// 			default:

// 			}

// 			return
// 		}
// 	}
// }

// func getBurstWorkStatus[T SideNumber](marginMode domain.MarginMode, contractCode string, posSide T, uid string, isTrialPos bool) (int, error) {
// 	userBurstStatusKey := cachekey.GetBurstWorkingStatusRedisKey(marginMode, uid)
// 	if isTrialPos {
// 		userBurstStatusKey = cachekey.GetTrialBurstWorkingStatusRedisKey(marginMode, uid)
// 	}
// 	burstCode := contractCode
// 	if marginMode == domain.MarginModeIsolated {
// 		burstCode = fmt.Sprintf("%s_%d", contractCode, posSide)
// 	}

// 	processStatus, err := redisCli.Client().HGet(context.Background(), userBurstStatusKey, burstCode).Int()
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, contractCode, uid, "getBurstWorkStatus HGet", userBurstStatusKey, burstCode, "error:", err)
// 			return -1, err
// 		}
// 	}
// 	return processStatus, nil
// }

// func updateBurstWorkStatus[T SideNumber](marginMode domain.MarginMode, contractCode string, posSide T, uid string, burstStatus int, isTrialPos bool) (bool, error) {
// 	userBurstStatusKey := cachekey.GetBurstWorkingStatusRedisKey(marginMode, uid)
// 	if isTrialPos {
// 		userBurstStatusKey = cachekey.GetTrialBurstWorkingStatusRedisKey(marginMode, uid)
// 	}
// 	burstCode := contractCode
// 	if marginMode == domain.MarginModeIsolated {
// 		burstCode = fmt.Sprintf("%s_%d", contractCode, posSide)
// 	}

// 	switch burstStatus {
// 	case domain.BurstTrigged:
// 		setOk, err := redisCli.Client().HSetNX(context.Background(), userBurstStatusKey, burstCode, burstStatus).Result()
// 		if err != nil {
// 			logrus.Error(0, contractCode, uid, "buildProcessor HSetNX", userBurstStatusKey, "error:", err)
// 			return false, err
// 		}
// 		return setOk, nil

// 	case domain.BurstReset:
// 		_, err := redisCli.Client().HDel(context.Background(), userBurstStatusKey, burstCode).Result()
// 		if err != nil {
// 			logrus.Error(0, contractCode, uid, "buildProcessor HDel", userBurstStatusKey, "error:", err)
// 			return false, err
// 		}
// 		return true, nil

// 	default:

// 	}
// 	_, err := redisCli.Client().HSet(context.Background(), userBurstStatusKey, burstCode, burstStatus).Result()
// 	if err != nil {
// 		logrus.Error(0, contractCode, uid, "buildProcessor HSet", userBurstStatusKey, "error:", err)
// 		return false, err
// 	}
// 	return true, nil
// }

// func lPushBurstTask[T SideNumber](marginMode domain.MarginMode, contractCode string, posSide T, uid, liquidationData string) {
// 	burstTaskKey := cachekey.GetBurstWorkingTaskRedisKey(marginMode, contractCode)
// 	if marginMode == domain.MarginModeIsolated {
// 		burstTaskKey = cachekey.GetBurstWorkingTaskRedisKey(marginMode, fmt.Sprintf("%s_%d", contractCode, posSide))
// 	}

// 	tempInfo := cache.LiquidationInfo{}
// 	err := json.Unmarshal([]byte(liquidationData), &tempInfo)
// 	if err != nil || len(tempInfo.UID) < 1 || tempInfo.MarginMode < 1 {
// 		logrus.Error(0, "lPushBurstTask check Unmarshal liquidationData error:", err, string(liquidationData))

// 		if marginMode == domain.MarginModeIsolated {
// 			_ = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 				ContractCode: contractCode,
// 				Liquidation:  cache.LiquidationInfo{UID: uid, PosSide: int32(posSide), IsTrialPos: tempInfo.IsTrialPos},
// 			}, false)
// 		} else {
// 			_ = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 				ContractCode: contractCode,
// 				Liquidation:  cache.LiquidationInfo{UID: uid},
// 			}, true)
// 		}

// 		_, _ = updateBurstWorkStatus(marginMode, contractCode, posSide, uid, domain.BurstReset, tempInfo.IsTrialPos)

// 		return
// 	}

// 	err = redisCli.Client().LPush(context.Background(), burstTaskKey, liquidationData).Err()
// 	if err != nil {
// 		logrus.Error(0, contractCode, uid, "lPushBurstTask LPush ", burstTaskKey, " error:", err)
// 	}
// }

// func rPopBurstTask[T SideNumber](marginMode domain.MarginMode, contractCode string, posSide T) (string, error) {
// 	burstTaskKey := cachekey.GetBurstWorkingTaskRedisKey(marginMode, contractCode)
// 	if marginMode == domain.MarginModeIsolated {
// 		burstTaskKey = cachekey.GetBurstWorkingTaskRedisKey(marginMode, fmt.Sprintf("%s_%d", contractCode, posSide))
// 	}
// 	dataInfo, err := redisCli.Client().BRPop(context.Background(), time.Second*5, burstTaskKey).Result()
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, contractCode, "rPopBurstTask BRPop ", burstTaskKey, " error:", err)
// 		}
// 		return "", err
// 	}
// 	for i, data := range dataInfo {
// 		if i%2 == 1 {
// 			return data, nil
// 		}
// 	}
// 	return "", errors.New(fmt.Sprintln("rPopBurstTask data failed:", fmt.Sprintf("%+v", dataInfo)))
// }

// func reCheckBursTask(burstCode, uid string, marginMode domain.MarginMode, isTrialPos bool) {
// 	burstWorkingUsersSet := cachekey.GetBurstWorkingUsersRedisKey(marginMode)
// 	userBurstStatusKey := cachekey.GetBurstWorkingStatusRedisKey(marginMode, uid)
// 	burstWorkingReCheckUsersSet := cachekey.GetBurstWorkingRecheckRedisKey(marginMode)
// 	burstLockKey := cachekey.GetBurstLockRedisKey(uid, marginMode)
// 	if isTrialPos {
// 		burstWorkingUsersSet = cachekey.GetTrialBurstWorkingUsersRedisKey(marginMode)
// 		userBurstStatusKey = cachekey.GetTrialBurstWorkingStatusRedisKey(marginMode, uid)
// 		burstWorkingReCheckUsersSet = cachekey.GetTrialBurstWorkingRecheckRedisKey(marginMode)
// 		burstLockKey = cachekey.GetTrialBurstLockRedisKey(uid, marginMode)
// 	}

// 	liquidationData, err := redisCli.Client().HGet(context.Background(), burstLockKey, burstCode).Bytes()
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, burstCode, uid, "reCheckBursTask get user lock info error:", err)
// 		}
// 		return
// 	}

// 	base, quote := util.BaseQuote(burstCode)
// 	contractCode := burstCode
// 	if marginMode == domain.MarginModeIsolated {
// 		burstCodeList := strings.Split(burstCode, "_")
// 		if len(burstCodeList) > 1 {
// 			base, quote = util.BaseQuote(burstCodeList[0])
// 			contractCode = util.ContractCode(base, quote)
// 		}
// 	}

// 	liquidation := cache.LiquidationInfo{}
// 	err = json.Unmarshal(liquidationData, &liquidation)
// 	if err != nil {
// 		logrus.Error(0, contractCode, uid, "reCheckBursTask Unmarshal error:", err, "data:", string(liquidationData))
// 		return
// 	}

// 	_, assertInfo, err := swapcache.GetUserCacheData(base, quote, uid)
// 	if err != nil {
// 		logrus.Error(0, contractCode, uid, "reCheckBursTask GetUserCacheData error:", err)
// 		return
// 	}

// 	switch liquidation.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		switch liquidation.MarginMode {
// 		case domain.MarginModeIsolated:
// 			posInfo := repository.PosSwap{}
// 			switch liquidation.PosSide {
// 			case domain.LongPos:
// 				posInfo = assertInfo.LongPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialLongPos
// 				}

// 			case domain.ShortPos:
// 				posInfo = assertInfo.ShortPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialShortPos
// 				}

// 			case domain.BothPos:
// 				posInfo = assertInfo.BothPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialBothPos
// 				}

// 			default:

// 			}

// 			if posInfo.CalcPosHoldValue().LessThanOrEqual(liquidation.TargetLimit) {
// 				logrus.Info(0, contractCode, uid, "reCheckBursTask self unlock isolated:", string(liquidationData))
// 				err = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 					ContractCode: contractCode,
// 					Liquidation: cache.LiquidationInfo{
// 						UID:        uid,
// 						MarginMode: liquidation.MarginMode,
// 						PosSide:    posInfo.PosSide,
// 						IsTrialPos: posInfo.IsTrial(),
// 					},
// 				}, false)
// 				if err != nil {
// 					logrus.Error(0, contractCode, uid, "reCheckBursTask UnlockBurstUser", burstLockKey, contractCode, "error:", err)
// 					return
// 				}
// 				redisCli.Client().SRem(context.Background(), burstWorkingReCheckUsersSet, uid)
// 				redisCli.Client().SRem(context.Background(), burstWorkingUsersSet, uid)
// 				redisCli.Client().HDel(context.Background(), userBurstStatusKey, uid, fmt.Sprintf("%s_%d", contractCode, posInfo.PosSide))
// 			} else {
// 				_, _ = updateBurstWorkStatus(marginMode, contractCode, posInfo.PosSide, uid, domain.BurstReady, isTrialPos)
// 				lPushBurstTask(marginMode, contractCode, posInfo.PosSide, uid, string(liquidationData))
// 			}

// 		case domain.MarginModeCross:
// 			crossedMarketValue := assertInfo.LongPos.CalcPosHoldValue().Add(assertInfo.ShortPos.CalcPosHoldValue().Add(assertInfo.BothPos.CalcPosHoldValue()))
// 			if crossedMarketValue.LessThanOrEqual(liquidation.TargetLimit) {
// 				logrus.Info(0, contractCode, uid, "reCheckBursTask self unlock crossed:", string(liquidationData))
// 				err = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 					ContractCode: contractCode,
// 					Liquidation: cache.LiquidationInfo{
// 						UID:        uid,
// 						MarginMode: liquidation.MarginMode,
// 					},
// 				}, false)
// 				if err != nil {
// 					logrus.Error(0, contractCode, uid, "reCheckBursTask UnlockBurstUser", burstLockKey, contractCode, "error:", err)
// 					return
// 				}
// 				redisCli.Client().SRem(context.Background(), burstWorkingReCheckUsersSet, uid)
// 				redisCli.Client().SRem(context.Background(), burstWorkingUsersSet, uid)
// 				redisCli.Client().HDel(context.Background(), userBurstStatusKey, uid, contractCode)
// 			} else {
// 				logrus.Info(0, contractCode, uid, "reCheckBursTask self unlock crossed failed:", crossedMarketValue, "great than", liquidation.TargetLimit)
// 				_, _ = updateBurstWorkStatus(marginMode, contractCode, 0, uid, domain.BurstReady, isTrialPos)
// 				lPushBurstTask(marginMode, contractCode, 0, uid, string(liquidationData))
// 			}

// 		default:

// 		}

// 	case domain.LiquidationTypeBurst:
// 		switch liquidation.MarginMode {
// 		case domain.MarginModeIsolated:
// 			posInfo := repository.PosSwap{}
// 			switch liquidation.PosSide {
// 			case domain.LongPos:
// 				posInfo = assertInfo.LongPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialLongPos
// 				}

// 			case domain.ShortPos:
// 				posInfo = assertInfo.ShortPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialShortPos
// 				}

// 			case domain.BothPos:
// 				posInfo = assertInfo.BothPos
// 				if liquidation.IsTrialPos {
// 					posInfo = assertInfo.TrialBothPos
// 				}

// 			default:

// 			}

// 			if posInfo.Pos.IsZero() {
// 				logrus.Info(0, contractCode, uid, "reCheckBursTask final Unlock isolated:", string(liquidationData))
// 				sendUnlockTask(contractCode, liquidation.UID, liquidation.BurstId, liquidation.PosId, liquidation.BurstTime, domain.MarginModeIsolated, posInfo.PosSide, liquidation.IsTrialPos)
// 				redisCli.Client().SRem(context.Background(), burstWorkingReCheckUsersSet, uid)
// 				redisCli.Client().SRem(context.Background(), burstWorkingUsersSet, uid)
// 				redisCli.Client().HDel(context.Background(), userBurstStatusKey, uid, fmt.Sprintf("%s_%d", contractCode, posInfo.PosSide))
// 			} else {
// 				_, _ = updateBurstWorkStatus(marginMode, contractCode, posInfo.PosSide, uid, domain.BurstReady, isTrialPos)
// 				lPushBurstTask(marginMode, contractCode, posInfo.PosSide, uid, string(liquidationData))
// 			}

// 		case domain.MarginModeCross:
// 			if assertInfo.LongPos.Pos.Add(assertInfo.ShortPos.Pos.Add(assertInfo.BothPos.Pos.Abs())).IsZero() {
// 				logrus.Info(0, contractCode, uid, "reCheckBursTask send crossed burst unlock:", string(liquidationData))
// 				err = cachelock.UnlockBurstUser(cachelock.BurstLockParams{
// 					ContractCode: contractCode,
// 					Liquidation: cache.LiquidationInfo{
// 						UID:        uid,
// 						MarginMode: liquidation.MarginMode,
// 					},
// 				}, false)
// 				if err != nil {
// 					logrus.Error(0, contractCode, uid, "reCheckBursTask UnlockBurstUser", burstLockKey, contractCode, "error:", err)
// 					return
// 				}
// 				lockMap, err := redisCli.Client().HGetAll(context.Background(), burstLockKey).Result()
// 				if err != nil {
// 					logrus.Error(0, contractCode, uid, "reCheckBursTask HGetAll", burstLockKey, "error:", err)
// 					return
// 				}
// 				if len(lockMap) == 1 {
// 					for lockUserId := range lockMap {
// 						if lockUserId == uid {
// 							logrus.Info(0, contractCode, uid, "reCheckBursTask final Unlock crossed:", string(liquidationData))
// 							sendUnlockTask(contractCode, liquidation.UID, liquidation.BurstId, liquidation.PosId, liquidation.BurstTime, domain.MarginModeCross, -1, false)
// 							redisCli.Client().SRem(context.Background(), burstWorkingReCheckUsersSet, uid)
// 							redisCli.Client().SRem(context.Background(), burstWorkingUsersSet, uid)
// 						}
// 					}
// 				}
// 				redisCli.Client().HDel(context.Background(), userBurstStatusKey, uid, contractCode)
// 			} else {
// 				_, _ = updateBurstWorkStatus(marginMode, contractCode, 0, uid, domain.BurstReady, isTrialPos)
// 				lPushBurstTask(marginMode, contractCode, 0, uid, string(liquidationData))
// 			}

// 		default:

// 		}

// 	default:

// 	}
// }

// func sendUnlockTask(contractCode, uid, burstId, posId string, burstTime int64, marginMode domain.MarginMode, posSide int32, isTrialPos bool) {
// 	if marginMode == domain.MarginModeCross {
// 		lockKey := fmt.Sprintf("%s:unlock_task:%s:%s:%s:%d:lock", cache.Prefix, uid, burstId, posId, marginMode)
// 		lockStr, err := redisCli.GetString(lockKey)
// 		if err != nil {
// 			if err == redis.Nil { // 没有发送过解锁
// 				err = redisCli.SetString(lockKey, fmt.Sprintf("%d", time.Now().Unix()))
// 				if err != nil {
// 					logrus.Error(0, contractCode, "sendUnlockTask SetString", lockKey, "error:", err)
// 				}
// 				err = redisCli.SetExpire(lockKey, "5s")
// 				if err != nil {
// 					logrus.Error(0, contractCode, "sendUnlockTask SetExpire", lockKey, "error:", err)
// 				}
// 			} else {
// 				logrus.Error(0, contractCode, "sendUnlockTask GetString", lockKey, "error:", err)
// 			}
// 		} else {
// 			if len(lockStr) > 0 { // 发送过解锁
// 				unix, err := strconv.ParseInt(lockStr, 10, 64)
// 				if err != nil {
// 					logrus.Error(0, contractCode, "sendUnlockTask ParseInt", lockStr, "error:", err)
// 				} else {
// 					if time.Now().Sub(time.Unix(unix, 0)) > time.Second*5 { // 发送过解锁 时间已经超过5秒
// 						defer func() {
// 							defer func() {
// 								if err := recover(); err != nil {
// 									logrus.Error(0, contractCode, "sendUnlockTask DelKey", lockKey, "recover error:", err)
// 								}
// 							}()
// 							_, err = redisCli.DelKey(lockKey)
// 							if err != nil {
// 								logrus.Error(0, contractCode, "sendUnlockTask DelKey", lockKey, "error:", err)
// 							}
// 						}()
// 					} else { // 发送过解锁 时间未超过5秒
// 						return
// 					}
// 				}
// 			}
// 		}
// 	}
// 	task := map[string]interface{}{
// 		"burstId":      burstId,
// 		"uid":          uid,
// 		"posId":        posId,
// 		"marginMode":   marginMode,
// 		"contractCode": contractCode,
// 		"posSide":      posSide,
// 		"burstTime":    burstTime,
// 		"isTrialPos":   isTrialPos,
// 	}
// 	taskJsonBytes, _ := json.Marshal(task)
// 	err := redisCli.LPush(cachekey.GetBurstUnlockListRedisKey(), taskJsonBytes)
// 	if err != nil {
// 		logrus.Error(0, contractCode, "sendUnlockTask LPush", cachekey.GetBurstUnlockListRedisKey(), "error:", err, fmt.Sprintf("%+v", task))
// 	}
// 	// 清除短信锁
// 	redisCli.DelKey(cache.GetBurstSMSLockRedisKey(uid, contractCode, domain.MSReduce))
// 	redisCli.DelKey(cache.GetBurstSMSLockRedisKey(uid, contractCode, domain.MSForceReduceTarget))
// 	redisCli.DelKey(cache.GetBurstSMSLockRedisKey(uid, contractCode, domain.MSBurst))
// 	redisCli.DelKey(cache.GetBurstSMSLockRedisKey(uid, contractCode, domain.MSReduceWarn))
// 	redisCli.DelKey(cache.GetBurstSMSLockRedisKey(uid, contractCode, domain.MSBurstWarn))
// }

// func stopPrintDebug(data ...interface{}) {
// 	for _, d := range data {
// 		fmt.Println("=============================", reflect.TypeOf(data).String(), fmt.Sprintf("%+v", d))
// 	}
// 	os.Exit(3)
// }
