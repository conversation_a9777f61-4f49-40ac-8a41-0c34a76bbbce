package burst

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"io/ioutil"
// 	"log"
// 	"net/http"
// 	"reflect"
// 	"runtime"
// 	"runtime/debug"
// 	"sort"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/cache/sharedcache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/handler/monitor"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/db/swap"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/pkg/match"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/pkg/user"
// 	"futures-asset/util"

// 	"github.com/jinzhu/gorm"
// 	"github.com/redis/go-redis/v9"
// 	uuid "github.com/satori/go.uuid"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// // CronServiceStart 服务启动
// func CronServiceStart(_ctx context.Context, _wg *sync.WaitGroup, _slim, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "CronServiceStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second * 5)
// 			CronServiceStart(_ctx, _wg, _slim, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}

// 	// 拉取所有币对配置
// 	err := updateAllCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "CronServiceStart get setting all contract coin pair error:", err)
// 	}

// 	second30Ticker := time.NewTicker(time.Second * 30)
// Loop:
// 	for {
// 		// 判断是否停机维护
// 		status := maintain.MaintainState()
// 		if status.Id > 0 && (status.ModuleType == 1 || status.ModuleType == 3) {
// 			time.Sleep(time.Second)
// 		} else {
// 			select {
// 			case <-second30Ticker.C:
// 				// 拉取所有币对配置
// 				err := updateAllCoinPairInfo()
// 				if err != nil {
// 					logrus.Error(0, "CronServiceStart get setting all contract coin pair error:", err)
// 				}

// 				burstContracts := sharedcache.GetBurstServerContracts()
// 				if len(burstContracts) < 1 {
// 					log.Fatalf("burst contracts is empty")
// 				}
// 				sort.Strings(burstContracts)

// 				// 快照币对列表
// 				snapCronContracts.RLock()
// 				memSnapContracts := snapCronContracts.List
// 				snapCronContracts.RUnlock()
// 				sort.Strings(memSnapContracts)

// 				// 检查是否新增币对
// 				burstContractsStr := strings.Join(burstContracts, "|")
// 				snapContractsStr := strings.Join(memSnapContracts, "|")

// 				// 配置币对对比内存快照币对
// 				if burstContractsStr != snapContractsStr {
// 					// 配置币对map
// 					burstContractMap := map[string]string{}
// 					for _, symbol := range burstContracts {
// 						burstContractMap[symbol] = symbol
// 					}

// 					// 快照币对map
// 					memContractMap := map[string]string{}
// 					for _, symbol := range memSnapContracts {
// 						memContractMap[symbol] = symbol
// 					}

// 					// 确定移除币对
// 					var removeSymbolList []string
// 					for _, symbol := range memSnapContracts {
// 						if _, ok := burstContractMap[symbol]; !ok {
// 							removeSymbolList = append(removeSymbolList, symbol)
// 						}
// 					}

// 					// 确定添加币对
// 					var addSymbolList []string
// 					for _, symbol := range burstContracts {
// 						if _, ok := memContractMap[symbol]; !ok {
// 							addSymbolList = append(addSymbolList, symbol)
// 						}
// 					}

// 					// 移除币对
// 					cronStop(removeSymbolList)

// 					// 增加币对
// 					for _, symbol := range addSymbolList {
// 						hasContext := false
// 						snapCronContext.RLock()
// 						_, hasContext = snapCronContext.Map[symbol]
// 						snapCronContext.RUnlock()
// 						if !hasContext {
// 							if _slim {
// 								go _slimCronStart(_ctx, _wg, symbol, false)
// 							} else {
// 								go _cronStart(_ctx, _wg, symbol, false)
// 							}
// 						}
// 					}

// 					// 更新币对
// 					snapCronContracts.Lock()
// 					snapCronContracts.List = burstContracts
// 					snapCronContracts.Unlock()
// 				}

// 			case <-_ctx.Done():
// 				break Loop

// 			}
// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// func (bs *workingBurstService) updateServiceCoinPairInfo() error {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "updateCoinPairInfo error:", err)
// 		}
// 	}()
// 	// 更新合约配置
// 	err := setting.Service.UpdateAllPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "updateCoinPairInfo UpdatePairInfo error:", err)
// 		return err
// 	}
// 	coinPairSetting, err := setting.Service.GetPairSettingInfo(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "updateServiceCoinPairInfo GetPairSettingInfo error:", err)
// 		return err
// 	}
// 	bs.Lock()
// 	bs.coinPairInfo = *coinPairSetting
// 	bs.Unlock()

// 	_, err = initBurstLevels(bs.contractCode)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "updateCoinPairInfo initBurstLevels error:", err)
// 		return err
// 	}
// 	return nil
// }

// func (bs *workingBurstService) cron(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "cron recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			bs.cron(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}

// 	millisecond300Ticker := time.NewTicker(time.Millisecond * 300)

// 	millisecond500Ticker := time.NewTicker(time.Millisecond * 500)

// 	second1Ticker := time.NewTicker(time.Second)

// 	minute1Ticker := time.NewTicker(time.Minute)

// 	err := bs.updateFameexSpotLastPrice()
// 	if err != nil {
// 		logrus.Error(0, "fameex index price error:", err)
// 	}

// 	err = bs.updateBitmexIndexPrice()
// 	if err != nil {
// 		logrus.Error(0, "bitmex index price error:", err)
// 	}

// Loop:
// 	for {
// 		// 判断是否停机维护
// 		status := maintain.MaintainState()
// 		if status.Id > 0 && (status.ModuleType == 1 || status.ModuleType == 3) {
// 			time.Sleep(time.Second)
// 		} else {
// 			select {
// 			case <-_ctx.Done():
// 				break Loop

// 			case <-millisecond300Ticker.C:
// 				// go swapcache.MakeMarkPrice(bs.contractCode)

// 			case <-millisecond500Ticker.C:

// 			case <-second1Ticker.C:
// 				go swapcache.MakeMarkPrice(bs.contractCode)
// 				go bs.updateFameexSpotLastPrice()

// 			case t := <-minute1Ticker.C:
// 				bs.updateServiceCoinPairInfo()
// 				bs.updateBitmexIndexPrice()

// 				ContractSettings.RLock()
// 				contractSetting, ok := ContractSettings.Map[bs.contractCode]
// 				ContractSettings.RUnlock()
// 				if ok && (contractSetting.State == 1 || contractSetting.State == 3) {
// 					bestAskPrice, bestBidPrice := decimal.Zero, decimal.Zero
// 					if bestAskPrice, bestBidPrice, err = bs.futuresEngineRepo.GetBestPrice(bs.contractCode); err != nil {
// 						logrus.Error(0, "CronStart GetBestPrice error:", err)
// 					}

// 					basisListKey := fmt.Sprintf("%s%s:basis_list", cache.Prefix, bs.contractCode)
// 					basis := bs.MakeSwapBasis(bestAskPrice, bestBidPrice)

// 					err := redisCli.ZAdd(basisListKey, fmt.Sprintf("%s_%d", basis.String(), t.Unix()), float64(t.Unix()))
// 					if err != nil && err != redis.Nil {
// 						logrus.Error(0, "CronStart ZAdd error:", err)
// 					}
// 					listLen, err := redisCli.ZCard(basisListKey)
// 					if err != nil && err != redis.Nil {
// 						logrus.Error(0, "CronStart ZCard error:", err)
// 					}
// 					if err == nil && listLen > domain.MaxFundRateNum {
// 						go redisCli.ZRemRangeByRank(basisListKey, 0, listLen-domain.MaxFundRateNum)
// 					}
// 				}

// 			}
// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// // updateBitmexIndexPrice 更新bitmex指数价格
// func (bs *workingBurstService) updateBitmexIndexPrice() error {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "updateBitmexIndexPrice error:", err)
// 		}
// 	}()
// 	url := ""
// 	switch bs.contractCode {
// 	case "btc-usdt":
// 		url = fmt.Sprintf("https://www.bitmex.com/api/v1/trade?%s&count=1&columns=price&reverse=true", "symbol=.BXBT")
// 	}

// 	if len(url) > 0 {

// 		req, err := http.NewRequest(http.MethodGet, url, nil)
// 		if err != nil {
// 			return err
// 		}
// 		if req == nil {
// 			return errors.New("make request is nil")
// 		}
// 		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36")
// 		req.Header.Set("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9")
// 		req.Header.Set("authority", "www.bitmex.com")
// 		req.Header.Set("method", "GET")
// 		req.Header.Set("path", fmt.Sprintf("/api/v1/trade?%s&count=1&columns=price&reverse=true", "symbol=.BXBT"))
// 		req.Header.Set("scheme", "https")
// 		client := http.Client{}
// 		res, err := client.Do(req)
// 		if err != nil {
// 			logrus.Error(0, "http get bitmex index price error:", err)
// 			return errors.New(fmt.Sprint("http get bitmex index price error:", err))
// 		}
// 		bytes, err := ioutil.ReadAll(res.Body)
// 		if err != nil {
// 			logrus.Error(0, "bitmex index price ReadAll error:", err)
// 			return errors.New(fmt.Sprint("bitmex index price ReadAll error:", err))
// 		}
// 		// [{"symbol":".BXBT","timestamp":"2020-09-02T07:13:00.000Z","price":11765.11}]
// 		type bitmexIndex struct {
// 			Symbol    string          `json:"symbol"`
// 			Price     decimal.Decimal `json:"price"`
// 			Timestamp string          `json:"timestamp"`
// 		}
// 		indexInfo := make([]bitmexIndex, 0)
// 		json.Unmarshal(bytes, &indexInfo)

// 		if len(indexInfo) > 0 {
// 			indexKey := fmt.Sprintf("%sbitmex:index:%s", cache.Prefix, bs.contractCode)
// 			redisCli.SetString(indexKey, indexInfo[0].Price.String())
// 			redisCli.SetExpire(indexKey, "2m")
// 		}
// 	}
// 	return nil
// }

// // updateFameexSpotLastPrice 更新fameex现货最新成交价
// func (bs *workingBurstService) updateFameexSpotLastPrice() error {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "updateFameexSpotLastPrice error:", err)
// 		}
// 	}()
// 	lastPrice, err := match.Service.GetSpotLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, "fameex spot price read error:", err)
// 		return errors.New(fmt.Sprint("fameex spot price read error:", err))
// 	}
// 	if lastPrice.GreaterThan(decimal.Zero) {
// 		indexKey := fmt.Sprintf("%sfameex:spot:price", cache.Prefix)
// 		redisCli.HSet(indexKey, bs.contractCode, lastPrice.String())
// 	}
// 	return nil
// }

// // AsyncBurstLogStart 爆仓记录异步工作区
// func (bs *workingBurstService) AsyncBurstLogStart(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Errorln(fmt.Sprintf("%s AsyncBurstLogStart recover error: %s", bs.contractCode, err))
// 			logrus.Error(0, bs.contractCode, "AsyncBurstLogStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			bs.AsyncBurstLogStart(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}

// 	logrus.Info(0, bs.contractCode, "burst log async save start")
// 	defer logrus.Info(0, bs.contractCode, "burst log async save stopped")

// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			if _ctx.Err() != nil {
// 				logrus.Error(0, bs.contractCode, "burstService AsyncBurstLogStart context error:", _ctx.Err())
// 				break Loop
// 			}
// 			jsonStr, err := redisCli.RPop(cachekey.GetBurstLogListRedisKey(bs.contractCode))
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, "AsyncBurstLogStart RPop error:", err)
// 				}
// 				runtime.Gosched()
// 				time.Sleep(time.Second)
// 				continue
// 			}

// 			swapBurstTask := new(cache.SwapBurstTask)
// 			err = json.Unmarshal([]byte(jsonStr), swapBurstTask)
// 			if err != nil {
// 				logrus.Error(0, "AsyncBurstLogStart RPop", cachekey.GetBurstLogListRedisKey(bs.contractCode), "Unmarshal error:", err, jsonStr)
// 			} else {
// 				switch swapBurstTask.Type {
// 				case cache.TaskTypeNewBurst:
// 					bs.SaveBurstLog(_ctx, swapBurstTask.Data)

// 				case cache.TaskTypeDoBurst:
// 					bs.SendBurstTask(_ctx, swapBurstTask.Data)

// 				case cache.TaskTypeUpdateBurst:
// 					bs.UpdateBurstLog(swapBurstTask.Data, swapBurstTask.FuncName)

// 				case cache.TaskTypeBurstEvent:
// 					bs.DoBurstEvent(swapBurstTask.Data, swapBurstTask.FuncName)

// 				default:

// 				}
// 			}
// 		}
// 		runtime.Gosched()
// 		time.Sleep(time.Millisecond * 50)
// 	}

// 	_wg.Done()
// }

// // SaveBurstLog 保存爆仓记录
// func (bs *workingBurstService) SaveBurstLog(_ctx context.Context, _jsonBytes []byte) {
// 	logBurstSwap := new(swap.LogBurstSwap)
// 	err := json.Unmarshal(_jsonBytes, logBurstSwap)
// 	if err != nil {
// 		logrus.Error(0, "SaveBurstLog Unmarshal error:", err, string(_jsonBytes))
// 		return
// 	}

// 	if len(logBurstSwap.UID) < 1 {
// 		return
// 	}

// 	db, err := sqllib.Db()
// 	if err != nil {
// 		logrus.Error(0, "SaveBurstLog get db error:", err, fmt.Sprintf("%+v", logBurstSwap))
// 		return
// 	}
// 	tableName := logBurstSwap.TableName()
// 	if ok := util.TableIsExit(tableName); !ok {
// 		if !db.HasTable(tableName) {
// 			err := db.Table(tableName).CreateTable(logBurstSwap).Error
// 			if err != nil {
// 				return
// 			}
// 			util.SetNewTableName(tableName)
// 		}
// 	}
// 	counter := 0
// 	db.Table(logBurstSwap.TableName()).Where(
// 		"user_id=? AND burst_id=? AND level=? AND liquidation_type=? AND long_pos=? AND short_pos=?",
// 		logBurstSwap.UID, logBurstSwap.BurstId, logBurstSwap.Level, logBurstSwap.LiquidationType, logBurstSwap.LongPos, logBurstSwap.ShortPos,
// 	).Count(&counter)
// 	if counter > 0 {
// 		return
// 	}

// 	err = logBurstSwap.Insert(nil)
// 	if err != nil {
// 		logrus.Error(0, "SaveBurstLog Insert logBurstSwap error:", err, fmt.Sprintf("%+v", logBurstSwap))
// 	}

// 	if logBurstSwap.UserType == 0 {
// 		levelRate, err := user.Service.GetUserLevelRate(logBurstSwap.UID)
// 		if err != nil {
// 			logrus.Error(0, "SaveBurstLog GetUserLevelRate error:", err, logBurstSwap.UID)
// 		} else {
// 			logBurstSwap.UserType = levelRate.UserType
// 		}
// 	}

// 	// 获取用户资产信息
// 	_, assetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, logBurstSwap.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "burstService SaveBurstLog GetUserCacheData error:", err, logBurstSwap.UID)
// 		return
// 	}
// 	isMixMargin := 0
// 	if assetInfo.AssetMode == domain.AssetMode {
// 		isMixMargin = 1
// 	}

// 	createTime := time.Now().UnixNano()
// 	posAmount := logBurstSwap.LongPos
// 	posUnreal := logBurstSwap.LongPosUnreal
// 	posValue := logBurstSwap.LongPosValue
// 	openPrice := logBurstSwap.LongOpenPrice
// 	if logBurstSwap.ShortPos.GreaterThan(logBurstSwap.LongPos) {
// 		posAmount = logBurstSwap.ShortPos
// 		posUnreal = logBurstSwap.ShortPosUnreal
// 		posValue = logBurstSwap.ShortPosValue
// 		openPrice = logBurstSwap.ShortOpenPrice
// 	}
// 	dbBurstSwap := entity.BurstSwap{
// 		Id:                   fmt.Sprintf("%d_%s", createTime, uuid.NewV4().String()),
// 		PosId:                logBurstSwap.PosId,
// 		BurstId:              logBurstSwap.BurstId,
// 		UID:                  logBurstSwap.UID,
// 		UserType:             logBurstSwap.UserType,
// 		AccountType:          logBurstSwap.AccountType,
// 		Base:                 logBurstSwap.Base,
// 		Quote:                logBurstSwap.Quote,
// 		Currency:             logBurstSwap.Quote,
// 		OrderType:            domain.Limit,
// 		MarginMode:           logBurstSwap.MarginMode,
// 		MarginBalance:        logBurstSwap.MarginBalance,
// 		TotalMargin:          logBurstSwap.TotalMargin,
// 		PosType:              logBurstSwap.PosSide,
// 		PosAmount:            posAmount,
// 		PosUnreal:            posUnreal,
// 		PosMargin:            logBurstSwap.PosMargin,
// 		PosValue:             posValue,
// 		Leverage:             logBurstSwap.Leverage,
// 		Level:                logBurstSwap.Level,
// 		LiquidationPrice:     logBurstSwap.LiquidationPrice,
// 		LiquidationType:      logBurstSwap.LiquidationType,
// 		LiquidationFee:       decimal.Decimal{},
// 		LiquidationDealValue: decimal.Decimal{},
// 		IsOverflow:           domain.NotOverflow,
// 		OverflowInfo:         "",
// 		OverflowValue:        decimal.Decimal{},
// 		IsForceRival:         domain.NotForceRival,
// 		ForceRivalAmount:     decimal.Decimal{},
// 		OpenPrice:            openPrice,
// 		PNL:                  logBurstSwap.PNL,
// 		MarkPrice:            logBurstSwap.MarkPrice,
// 		BurstPrice:           logBurstSwap.BurstPrice,
// 		CollapsePrice:        logBurstSwap.CollapsePrice,
// 		CollapsePriceFormula: logBurstSwap.CollapsePriceFormula,
// 		HoldingMarginRate:    logBurstSwap.HoldingMarginRate,
// 		HoldingMargin:        logBurstSwap.HoldingMargin,
// 		OpenTime:             logBurstSwap.OpenTime,
// 		BurstTime:            logBurstSwap.CreateTime,
// 		Status:               domain.Bursting,
// 		IsMixMargin:          isMixMargin,
// 		IsTrialPos:           logBurstSwap.IsTrialPos,
// 		OverflowTime:         0,
// 		CreateTime:           createTime,
// 	}
// 	counter = 0
// 	db.Table(dbBurstSwap.TableName()).
// 		Where("user_id=? AND pos_type=? AND pos_amount=? AND burst_id=?",
// 			logBurstSwap.UID,
// 			logBurstSwap.PosSide,
// 			posAmount,
// 			logBurstSwap.BurstId).Count(&counter)
// 	if counter < 1 {
// 		if dbBurstSwap.LiquidationType != int(domain.LiquidationTypeDisableSymbol) {
// 			err = dbBurstSwap.Insert(nil)
// 		}
// 		if err != nil {
// 			logrus.Error(0, "SaveBurstLog Insert BurstSwap error:", err, fmt.Sprintf("%+v", dbBurstSwap))
// 		} else {
// 			if len(logBurstSwap.BurstPosData) > 0 {
// 				swapcache.SendSwapTask(
// 					strings.ToUpper(fmt.Sprintf("%s-%s", logBurstSwap.Base, logBurstSwap.Quote)),
// 					cache.SwapBurstTask{
// 						Type:     cache.TaskTypeDoBurst,
// 						Data:     []byte(logBurstSwap.BurstPosData),
// 						FuncName: "",
// 					})
// 			}
// 		}
// 	}
// 	return
// }

// // SendBurstTask 发送处理爆仓任务
// func (bs *workingBurstService) SendBurstTask(_ctx context.Context, _jsonBytes []byte) {
// 	burstInfoData := new(burstPosInfo)
// 	err := json.Unmarshal(_jsonBytes, burstInfoData)
// 	if err != nil {
// 		logrus.Error(0, "burstService SendBurstTask Unmarshal error:", err, "===", string(_jsonBytes))
// 		return
// 	}

// 	checkRecordKey := cachekey.GetBurstWorkingUsersRedisKey(burstInfoData.MarginMode)
// 	if burstInfoData.IsTrialPos {
// 		checkRecordKey = cachekey.GetTrialBurstWorkingUsersRedisKey(burstInfoData.MarginMode)
// 	}
// 	err = redisCli.Client().SAdd(context.Background(), checkRecordKey, burstInfoData.UID).Err()
// 	if err != nil {
// 		logrus.Error(0, "burstService SendBurstTask SAdd error:", err, "---", checkRecordKey, "===", string(_jsonBytes))
// 	}
// }

// // UpdateBurstLog 更新爆仓记录
// func (bs *workingBurstService) UpdateBurstLog(_jsonBytes []byte, _funcName string) {
// 	burstSwap := new(entity.BurstSwap)
// 	err := json.Unmarshal(_jsonBytes, burstSwap)
// 	if err != nil {
// 		logrus.Error(0, "UpdateBurstLog Unmarshal error:", err, string(_jsonBytes))
// 		return
// 	}
// 	if len(burstSwap.UID) < 1 || len(burstSwap.BurstId) < 1 || burstSwap.BurstTime < 1 {
// 		return
// 	}
// 	refObj := reflect.ValueOf(burstSwap)
// 	values := refObj.MethodByName(_funcName).Call([]reflect.Value{
// 		reflect.ValueOf((*gorm.DB)(nil)),
// 	})
// 	for _, v := range values {
// 		switch v.Type().String() {
// 		case "*errors.errorString":
// 			tempErr := v.Interface().(error)
// 			logrus.Error(0, "UpdateBurstLog error:", tempErr.Error(), "param jsonBytes", string(_jsonBytes), "funcName", _funcName)
// 		default:
// 		}
// 	}
// }

// // DoBurstEvent 爆仓事件操作
// func (bs *workingBurstService) DoBurstEvent(_jsonBytes []byte, _funcName string) {
// 	burstEvent := new(monitor.BurstEvent)
// 	err := json.Unmarshal(_jsonBytes, burstEvent)
// 	if err != nil {
// 		logrus.Error(0, "DoBurstEvent Unmarshal error:", err, string(_jsonBytes))
// 		return
// 	}
// 	if len(burstEvent.UID) < 1 || len(burstEvent.BurstId) < 1 || burstEvent.BurstTime < 1 {
// 		return
// 	}
// 	refObj := reflect.ValueOf(burstEvent)
// 	values := refObj.MethodByName(_funcName).Call([]reflect.Value{})
// 	for _, v := range values {
// 		switch v.Type().String() {
// 		case "*errors.errorString":
// 			tempErr := v.Interface().(error)
// 			logrus.Error(0, "DoBurstEvent error:", tempErr.Error(), "param jsonBytes", string(_jsonBytes), "funcName", _funcName)

// 		default:

// 		}
// 	}
// }

// // MakeSwapBasis 基差  合约中间价 - 现货指数价格
// func (bs *workingBurstService) MakeSwapBasis(_bestAsk, _bestBid decimal.Decimal) decimal.Decimal {
// 	indexPrice := sharedcache.IndexPrice(bs.contractCode)
// 	if indexPrice.IsZero() {
// 		return decimal.Zero
// 	}
// 	// 没深度 卖一、买一都是零
// 	if _bestAsk.IsZero() && _bestBid.IsZero() {
// 		priceStr, err := redisCli.HGet(cache.GetContractLastPriceRedisKey(), bs.contractCode)
// 		if err != nil && err != redis.Nil {
// 			logrus.Error(0, "MakeSwapBasis HGet", cache.GetContractLastPriceRedisKey(), "error:", err)
// 			return decimal.Zero
// 		}
// 		price, _ := decimal.NewFromString(priceStr)
// 		if price.IsZero() {
// 			price, err = match.Service.GetSpotLastPrice(bs.base, bs.quote)
// 			if err != nil {
// 				logrus.Error(0, "MakeSwapBasis GetSpotLastPrice", bs.contractCode, "error:", err)
// 				return decimal.Zero
// 			}
// 		}
// 		return price.Sub(indexPrice)
// 	}
// 	var err error = nil
// 	// 卖没深度 卖一是零
// 	if _bestAsk.IsZero() {
// 		_bestAsk, err = sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 		if err != nil && err != redis.Nil {
// 			logrus.Error(0, "MakeSwapBasis GetContractLastPrice error:", err)
// 			return decimal.Zero
// 		}
// 	}
// 	// 买没深度 买一是零
// 	if _bestBid.IsZero() {
// 		_bestBid, err = sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 		if err != nil && err != redis.Nil {
// 			logrus.Error(0, "MakeSwapBasis GetContractLastPrice error:", err)
// 			return decimal.Zero
// 		}
// 	}
// 	// fmt.Println(bs.contractCode, "bestAsk:", _bestAsk, "bestBid:", _bestBid)
// 	swapBasis := _bestAsk.Add(_bestBid).Div(decimal.NewFromFloat(2)).Sub(indexPrice)
// 	return swapBasis
// }

// func _cronStart(_ctx context.Context, _wg *sync.WaitGroup, _symbol string, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Error(strings.ToUpper(_symbol), "cronStart recover error:", err)
// 			logrus.Error(0, strings.ToUpper(_symbol), "cronStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second * 5)
// 			_cronStart(_ctx, _wg, _symbol, true)
// 		}
// 	}()

// 	var burstWorker *workingBurstService = nil
// 	if !_isRecover {
// 		burstWorker = buildService(_symbol)
// 	} else {
// 		RunningServices.RLock()
// 		burstWorker = RunningServices.Map[strings.ToUpper(_symbol)]
// 		RunningServices.RUnlock()
// 	}
// 	if burstWorker == nil {
// 		return
// 	}

// 	err := burstWorker.updateServiceCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, strings.ToUpper(_symbol), "cronStart updateServiceCoinPairInfo error:", err)
// 	}

// 	wg := new(sync.WaitGroup)

// 	// 启动计划任务
// 	cronCtx, cronCtxCancel := context.WithCancel(_ctx)
// 	go burstWorker.cron(cronCtx, wg, false)

// 	// 爆仓记录异步工作区
// 	burstLogCtx, burstLogCtxCancel := context.WithCancel(_ctx)
// 	go burstWorker.AsyncBurstLogStart(burstLogCtx, wg, false)

// 	// 追加内存快照
// 	RunningServices.Lock()
// 	RunningServices.Map[strings.ToUpper(_symbol)] = burstWorker
// 	RunningServices.Unlock()
// 	if !_isRecover {
// 		_wg.Add(1)
// 		snapCronContracts.Lock()
// 		snapCronContracts.List = append(snapCronContracts.List, _symbol)
// 		snapCronContracts.Unlock()
// 	}
// 	snapCronContext.Lock()
// 	if _, ok := snapCronContext.Map[_symbol]; ok {
// 		snapCronContext.Map[_symbol] = append(snapCronContext.Map[_symbol], cronCtxCancel, burstLogCtxCancel)
// 	} else {
// 		snapCronContext.Map[_symbol] = []context.CancelFunc{cronCtxCancel, burstLogCtxCancel}
// 	}
// 	snapCronContext.Unlock()

// 	// 启动最新成交价监听
// 	burstWorker.tickerSubStop = false
// 	go burstWorker.TickerListener()

// 	logrus.Info(0, _symbol, "contract cron started")

// 	<-cronCtx.Done()
// 	<-burstLogCtx.Done()
// 	wg.Wait()
// 	burstWorker = nil
// 	logrus.Info(0, _symbol, "contract cron stopped")
// 	_wg.Done()
// }

// func _slimCronStart(_ctx context.Context, _wg *sync.WaitGroup, _symbol string, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Errorln("slimCronStart recover error:", err)
// 			logrus.Error(0, "slimCronStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			_slimCronStart(_ctx, _wg, _symbol, true)
// 		}
// 	}()

// 	// 获取或新建工作协程
// 	var burstWorker *workingBurstService = nil
// 	if !_isRecover {
// 		burstWorker = buildService(_symbol)
// 	} else {
// 		RunningServices.RLock()
// 		burstWorker = RunningServices.Map[strings.ToUpper(_symbol)]
// 		RunningServices.RUnlock()
// 	}
// 	if burstWorker == nil {
// 		return
// 	}

// 	second30Ticker := time.NewTicker(time.Second * 30)

// 	err := burstWorker.updateServiceCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "get setting contract info error:", err)
// 		logrus.Error(0, err)
// 		return
// 	}

// 	err = updateAllCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "get setting all contract info error:", err)
// 		logrus.Error(0, err)
// 		return
// 	}
// 	cronCtx, cronCtxCancel := context.WithCancel(_ctx)

// 	// 追加内存快照
// 	RunningServices.Lock()
// 	RunningServices.Map[strings.ToUpper(_symbol)] = burstWorker
// 	RunningServices.Unlock()
// 	if !_isRecover {
// 		_wg.Add(1)
// 		snapCronContracts.Lock()
// 		snapCronContracts.List = append(snapCronContracts.List, _symbol)
// 		snapCronContracts.Unlock()
// 	}
// 	snapCronContext.Lock()
// 	if _, ok := snapCronContext.Map[_symbol]; ok {
// 		snapCronContext.Map[_symbol] = append(snapCronContext.Map[_symbol], cronCtxCancel)
// 	} else {
// 		snapCronContext.Map[_symbol] = []context.CancelFunc{cronCtxCancel}
// 	}
// 	snapCronContext.Unlock()
// 	logrus.Info(0, _symbol, "contract slim cron started")

// Loop:
// 	for {
// 		// 判断是否停机维护
// 		status := maintain.MaintainState()
// 		if status.Id > 0 && (status.ModuleType == 1 || status.ModuleType == 3) {
// 			time.Sleep(time.Second)
// 		} else {
// 			select {
// 			case <-second30Ticker.C:
// 				go burstWorker.updateServiceCoinPairInfo()
// 				go updateAllCoinPairInfo()

// 			case <-cronCtx.Done():
// 				break Loop

// 			}
// 		}
// 		runtime.Gosched()
// 	}

// 	burstWorker = nil
// 	logrus.Info(0, _symbol, "contract slim cron stopped")
// 	_wg.Done()
// }

// func cronStop(_symbolList []string) {
// 	for _, symbol := range _symbolList {
// 		snapCronContext.RLock()
// 		ctxCancelList, ok := snapCronContext.Map[symbol]
// 		snapCronContext.RUnlock()
// 		if ok {
// 			for _, ctxCancel := range ctxCancelList {
// 				ctxCancel()
// 			}
// 			snapCronContext.Lock()
// 			delete(snapCronContext.Map, symbol)
// 			snapCronContext.Unlock()
// 		}

// 		RunningServices.RLock()
// 		_, hasBurstWorker := RunningServices.Map[strings.ToUpper(symbol)]
// 		RunningServices.RUnlock()

// 		if hasBurstWorker {
// 			RunningServices.Lock()
// 			delete(RunningServices.Map, symbol)
// 			RunningServices.Unlock()
// 		}
// 	}
// 	return
// }

// // TickerListener 监听最新成交价
// func (bs *workingBurstService) TickerListener() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "TickerListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			if bs.tickerSubStop {
// 				return
// 			}
// 			time.Sleep(time.Second)
// 			bs.TickerListener()
// 		}
// 	}()
// 	lastPriceChannel := cache.GetContractLastPriceRedisChannel(match.AccountTypeSwap, strings.ToLower(bs.base), strings.ToLower(bs.quote))
// 	bs.tickerSubCli = redisCli.Subscribe(lastPriceChannel)
// 	logrus.Info(0, bs.contractCode, "scanner subscribe", lastPriceChannel)
// 	for {
// 		revMsg, err := bs.tickerSubCli.ReceiveMessage(context.Background())
// 		if err != nil {
// 			logrus.Error(0, fmt.Sprintln(bs.contractCode, "TickerListener ReceiveMessage error:", err))
// 			continue
// 		}
// 		// fmt.Println("Payload", revMsg.Payload)
// 		bs.lastTicker.Lock()
// 		bs.lastTicker.LastPrice, _ = decimal.NewFromString(revMsg.Payload)
// 		bs.lastTicker.Unlock()

// 		err = redisCli.HSet(cache.GetContractLastPriceRedisKey(), bs.contractCode, revMsg.Payload)
// 		if err != nil {
// 			logrus.Error(0, fmt.Sprintln(bs.contractCode, "TickerListener HSet error:", err))
// 			continue
// 		}

// 		runtime.Gosched()
// 	}
// }
