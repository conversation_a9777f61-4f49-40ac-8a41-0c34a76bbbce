package liquidation

import (
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
)

type IReduce interface {
	LiquidationData(trade payload.Trade, req repository.TradeCommon, pos repository.PosSwap)
}

// NewOperator 根据强平类型获取操作
//
//	仅在平仓时触发
func NewOperator(liquidationType domain.LiquidationType) IReduce {
	switch liquidationType {
	case domain.LiquidationTypeBurst:
		return new(Burst)
	case domain.LiquidationTypeReduce:
		return new(Reduce)
	case domain.LiquidationTypeStopProfitReduce:
		return new(StopProfitReduce)
	case domain.LiquidationTypeTrialReduce:
		return new(TrialReduce)
	case domain.LiquidationTypeDisableSymbol:
		return new(DisableSymbolReduce)
	case domain.LiquidationTypeRobotSelfReduce:
		return new(RobotSelfReduce)
	case domain.LiquidationTypeBurstSelfReduce:
		return new(BurstSelfReduce)
	default:
		return new(NoneReduce)
	}
}

type NoneReduce struct{}

func (r *NoneReduce) LiquidationData(trade payload.Trade, req repository.TradeCommon, pos repository.PosSwap) {
	return
}
