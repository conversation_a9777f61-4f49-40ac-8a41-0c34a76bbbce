package liquidation

import (
	"encoding/json"

	"futures-asset/cache"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
)

// BurstSelfReduce 最后一档, 爆仓自成交减仓
type BurstSelfReduce struct{}

// LiquidationData 仅更新强平单-强平成交总额
func (r *BurstSelfReduce) LiquidationData(trade payload.Trade, req repository.TradeCommon, pos repository.PosSwap) {
	// 更新DB中数据
	// 	- 强平成交总额
	//	- 状态
	burstSwap := entity.BurstSwap{
		BurstId:              trade.BurstId,
		BurstTime:            trade.BurstTime,
		UID:                  pos.UID,
		Status:               domain.BurstFinished,
		PosType:              pos.PosSide,
		PosId:                pos.PosId,
		LiquidationType:      int(trade.LiquidationType),
		LiquidationDealValue: req.Amount.Mul(req.Price).Truncate(domain.CurrencyPrecision), // 强平单成交总额,
	}
	// 序列化发送更新数据到队列, burst_time服务中处理更新逻辑(使用反射的调用方式)
	jsonBytes, _ := json.Marshal(burstSwap)
	swapcache.SendSwapTask(pos.ContractCode, cache.SwapBurstTask{
		Type:     cache.TaskTypeUpdateBurst,
		Data:     jsonBytes,
		FuncName: "UpdateReduceFields",
	})
}
