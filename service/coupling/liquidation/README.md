# liquidation package introduction

> 订单成交后, 需要按照不同的合约强平类型生成或者更新强平单记录

需要生成或者更新的强平类型如下:

- LiquidationTypeBurst = 1 爆仓(撮合成交、直接成交(自己和他人对手方强制减仓))
- LiquidationTypeReduce = 2 减仓(直接成交(自己和自己)、撮合成交(自己和自己、自己和他人))
- LiquidationTypeStopProfitReduce = 3 止盈减仓(直接成交(自己和他人对手方强制减仓))
- LiquidationTypeTrialReduce = 4 体验金减仓
- LiquidationTypeDisableSymbol = 5 禁用币对减仓
- LiquidationTypeRobotSelfReduce = 6 机器人自减仓

## 1.LiquidationTypeBurst

> 进行更新强平单数据 (taker, maker 都需要操作)

爆仓更新项:

- 强平状态
- 爆仓强平费
- 穿仓补贴时间
- 穿仓补贴金额
- 补贴数量折合

## 2.LiquidationTypeReduce

> 进行更新强平单数据 (taker, maker 都需要操作)

成交更新项:

- 强平状态
- 强平成交总额
- 对手方减仓数量 (爆仓创建数据的时候已经更新上了, 当前不用更新)

## 3.LiquidationTypeStopProfitReduce

> 又插入也更新强平单数据 (仅操作 maker)

## 4.LiquidationTypeTrialReduce

> 只插入强平单数据

## 5.LiquidationTypeDisableSymbol

> 只插入强平单数据

## 6.LiquidationTypeRobotSelfReduce

> 只插入强平单数据
