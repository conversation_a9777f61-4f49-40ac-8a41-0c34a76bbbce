package binaryoption

import (
)

type OptionService struct{}

func newOption() *OptionService {
	return &OptionService{}
}

func InitOption(param *payload.UserParam) domain.Code {
	
}

func Asset(param *payload.CurrencyParam) (payload.OptionAssetReply, error) {
	
}

func (o *OptionService) Trade(req payload.OptionTradeParam) (domain.Code, []*payload.OptionTradeReply, error) {
	
}

func (o *OptionService) Exercise(req payload.ExerciseParam) (domain.Code, []*payload.OptionTradeReply, error) {
	
}
