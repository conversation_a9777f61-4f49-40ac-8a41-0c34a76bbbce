package binaryoption

import (
	"futures-asset/cache/optioncache"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/pkg/redislib"
	"github.com/shopspring/decimal"
)

type optionDemoService struct {
}

func newOptionDemo() *optionDemoService {
	return &optionDemoService{}
}

func (o *optionDemoService) Trade(_req payload.OptionTradeParam) (domain.Code, []*payload.OptionTradeReply, error) {	
}

func (o *optionDemoService) Exercise(_req payload.ExerciseParam) (domain.Code, []*payload.OptionTradeReply, error) {
}

func DemoAsset(uid string) (decimal.Decimal, error) {
}
