package binaryoption

import (
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
)

type IOption interface {
	Trade(_req payload.OptionTradeParam) (domain.Code, []*payload.OptionTradeReply, error)
	Exercise(_req payload.ExerciseParam) (domain.Code, []*payload.OptionTradeReply, error)
}

func GetTrader(optionType int) IOption {
	if optionType == domain.OptionTypeFirm {
		return newOption()
	}
	if optionType == domain.OptionTypeDemo {
		return newOptionDemo()
	}
	return nil
}
