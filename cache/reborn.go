package cache

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

type (
	RebornCard struct {
		Id               string     `json:"id"`               // id
		UID              string     `json:"uid"`              // 所属用户
		ContractCodeList []string   `json:"contractCodeList"` // 生效合约
		CreateTime       int64      `json:"createTime"`       // 领取时间
		StartTime        int64      `json:"startTime"`        // 生效时间
		ExpireTime       int64      `json:"expireTime"`       // 过期时间
		Period           int64      `json:"period"`           // 有效时长
		TriggerPosIds    []string   `json:"triggerPosId"`     // 触发仓位id
		TriggerTime      int64      `json:"triggerTime"`      // 触发时间
		Status           CardStatus `json:"status"`           // 状态 1 已过期 2 已触发
	}
	CardStatus int
)

const (
	rebornCardKey        = "burst:reborn:%s:info"
	syncCardList         = "burst:reborn:sync"
	activatingRebornCard = "burst:reborn:%s:activating"

	CardStatusNormal    CardStatus = 0
	CardStatusExpired   CardStatus = 1
	CardStatusTriggered CardStatus = 2
)

type expireRebornCardList []RebornCard

func (e expireRebornCardList) Len() int {
	return len(e)
}

func (e expireRebornCardList) Less(i, j int) bool {
	return e[i].ExpireTime < e[j].ExpireTime
}

func (e expireRebornCardList) Swap(i, j int) {
	e[i], e[j] = e[j], e[i]
}

var lock *sync.RWMutex

func init() {
	lock = new(sync.RWMutex)
}

func getRebornCardKey(uid string) string {
	return Prefix + fmt.Sprintf(rebornCardKey, uid)
}

func getRebornSyncKey() string {
	return Prefix + syncCardList
}

func getRebornActivatingKey(uid string) string {
	return Prefix + fmt.Sprintf(activatingRebornCard, uid)
}

func SaveUserRebornCard(uid string, card RebornCard) error {
	lock.Lock()
	defer lock.Unlock()
	if len(card.Id) < 1 {
		return errors.New("card no id")
	}
	if len(card.UID) < 1 {
		return errors.New("card no user id")
	}
	if card.ExpireTime < 1 {
		return errors.New("card no expire time")
	}
	if card.Period < 1 {
		return errors.New("card no period time")
	}
	if card.CreateTime < 1 {
		card.CreateTime = time.Now().Unix()
	}
	key := getRebornCardKey(uid)

	_, err := RedisCli.HGet(key, card.Id)
	if err != nil {
		if err != redis.Nil {
			return err
		}
	}
	if err == redis.Nil {
		return saveRebornCard(key, card)
	}
	return nil
}

func GetAvailableRebornCard(uid string, contractCode string) (RebornCard, error) {
	lock.RLock()
	defer lock.RUnlock()
	key := getRebornCardKey(uid)

	var card RebornCard

	allCardMap, err := RedisCli.HGetAll(key)
	if err != nil {
		if err != redis.Nil {
			return card, err
		}
	}
	if len(allCardMap) > 0 {
	Loop:
		for cardId, cardStr := range allCardMap {
			temp := RebornCard{}
			err = json.Unmarshal([]byte(cardStr), &temp)
			if err != nil {
				logrus.Error(0, "GetUserRebornCard", uid, "Unmarshal error:", err, cardStr)
				continue Loop
			}

			temp = checkCard(temp, contractCode)
			switch temp.Status {
			case CardStatusExpired, CardStatusTriggered:
				err = saveRebornCard(key, temp)
				if err != nil {
					logrus.Error(0, "GetUserRebornCard", uid, "saveRebornCard error:", err, fmt.Sprintf("%+v", temp))
					continue Loop
				}
				err = sendSyncTaskByCardId(cardId)
				if err != nil {
					logrus.Error(0, "GetUserRebornCard", uid, "sendSyncTask error:", err, fmt.Sprintf("%+v", temp))
					continue Loop
				}

			default:
				if len(card.Id) > 0 {
					card = temp
					break Loop
				}

			}
		}
	}
	return card, nil
}

func getActivatingRebornCard(uid string) (RebornCard, error) {
	card := RebornCard{}
	activatingKey := getRebornActivatingKey(uid)
	activatingStr, err := RedisCli.GetString(activatingKey)
	if err != nil {
		return card, err
	}
	if len(activatingStr) < 1 {
		return card, redis.Nil
	}
	err = json.Unmarshal([]byte(activatingStr), &card)
	if err != nil {
		logrus.Error(0, "getActivateRebornCard", uid, "Unmarshal error:", err, activatingStr)
		return card, err
	}
	return card, nil
}

func GetActivatingRebornCard(uid string) (RebornCard, error) {
	lock.RLock()
	defer lock.RUnlock()
	return getActivatingRebornCard(uid)
}

func ActivateRebornCard(posIds []string, card RebornCard) (bool, error) {
	lock.Lock()
	defer lock.Unlock()

	activatingCard, err := getActivatingRebornCard(card.UID)
	if err != nil && err != redis.Nil {
		return false, err
	}
	if err == redis.Nil || (len(activatingCard.Id) > 0 && time.Now().Unix()-activatingCard.TriggerTime > activatingCard.Period) {
		card.TriggerPosIds = posIds
		card.TriggerTime = time.Now().Unix()
		card.Status = CardStatusTriggered

		flushKey := getRebornCardKey(card.UID)
		err = saveRebornCard(flushKey, card)
		if err != nil {
			return false, err
		}

		activatingBytes, err := json.Marshal(card)
		if err != nil {
			return false, err
		}

		activatingKey := getRebornActivatingKey(card.UID)
		err = RedisCli.SetString(activatingKey, string(activatingBytes))
		if err != nil {
			return false, err
		}

		err = RedisCli.SetExpire(activatingKey, fmt.Sprintf("%ds", card.Period))
		if err != nil {
			return false, err
		}

		err = sendSyncTaskByCardId(card.Id)
		if err != nil {
			return false, err
		}
		return true, nil
	}
	return false, errors.New(fmt.Sprintln("has activating reborn card", fmt.Sprintf("%+v", activatingCard)))
}

func saveRebornCard(key string, card RebornCard) error {
	cardByes, err := json.Marshal(card)
	if err != nil {
		return errors.New(fmt.Sprintln("Marshal error:", err))
	}
	err = RedisCli.HSet(key, card.Id, string(cardByes))
	if err != nil {
		return err
	}
	return nil
}

func checkCard(card RebornCard, contractCode string) RebornCard {
	if card.ExpireTime < time.Now().Unix() {
		card.Status = CardStatusExpired
		return card
	} else if card.TriggerTime > 0 {
		card.Status = CardStatusTriggered
		return card
	}
	if len(card.ContractCodeList) > 0 {
		for _, symbol := range card.ContractCodeList {
			if strings.ToUpper(symbol) == strings.ToUpper(contractCode) {
				return card
			}
		}
	} else {
		return card
	}
	return RebornCard{}
}

func sendSyncTaskByCardId(cardId string) error {
	key := getRebornSyncKey()
	err := RedisCli.LPush(key, cardId)
	if err != nil {
		return errors.New(fmt.Sprintln("LPush error:", err))
	}
	return nil
}
