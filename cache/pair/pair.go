package pair

import (
	gocache "github.com/patrickmn/go-cache"
)

var c *gocache.Cache

func Init() {
	c = gocache.New(gocache.NoExpiration, gocache.NoExpiration)
}

// contract pair setting cache in memory
const prefix = "setting:"

func GetPair(key string) []byte {
	b, exist := c.Get(prefix + key)
	if !exist {
		return nil
	}
	return b.([]byte)
}

func SetPair(key string, data []byte) {
	c.Set(prefix+key, data, gocache.NoExpiration)
}
