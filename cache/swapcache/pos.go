package swapcache

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"futures-asset/cache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/db/swap"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/es"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/match"
	"futures-asset/util"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type PosCache struct {
	AssetCacheKey
	CacheParam
	Pair         string // 币对
	CrossList    map[string][]repository.PosSwap
	IsolatedList map[string][]repository.PosSwap
}

// NewPosCache 新建仓位缓存实体
func NewPosCache(req CacheParam, uid string) *PosCache {
	posCache := &PosCache{CacheParam: req}
	posCache.UID = uid
	posCache.GetRedisKey(uid, req.Base, req.Quote)

	return posCache
}

func (slf *PosCache) SetContractCode(contractCode string) *PosCache {
	slf.Pair = strings.ToUpper(contractCode)
	return slf
}

// LoadPos 加载所有仓位
func (slf *PosCache) LoadPos() *PosCache {
	var err error
	slf.CrossList, slf.IsolatedList, err = slf.CacheUserPos()
	if err != nil {
		logrus.Error(err)
	}

	return slf
}

// UserPos 获取用户全仓及逐仓仓位
func (slf *PosCache) UserPos(getAll bool, area string, markPrice decimal.Decimal) ([]repository.PosSwap, []repository.PosSwap, error) {
	crossPos, isolatedPos := make([]repository.PosSwap, 0), make([]repository.PosSwap, 0)

	for contractCode, posList := range slf.CrossList {
		if !getAll {
			if len(area) > 0 {
				if strings.Index(strings.ToUpper(contractCode), strings.ToUpper(area)) <= 0 {
					continue
				}
			} else {
				if !strings.Contains(contractCode, slf.ContractCode()) {
					continue
				}
			}
		}
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			v.PosValue = v.CalcPosValue(markPrice)
			v.OpenTime = v.OpenTime / 1e9
			crossPos = append(crossPos, v)
		}
	}
	for contractCode, posList := range slf.IsolatedList {
		if !getAll && !strings.Contains(contractCode, slf.ContractCode()) {
			continue
		}
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			v.PosValue = v.CalcPosValue(markPrice)
			v.OpenTime = v.OpenTime / 1e9
			isolatedPos = append(isolatedPos, v)
		}
	}

	return crossPos, isolatedPos, nil
}

// LockOrUnlock lock or unlock asset
func (slf *PosCache) LockOrUnlock(asset *repository.AssetSwap) error {
	frozen, _ := json.Marshal(asset.Frozen)
	balance, _ := json.Marshal(asset.Balance)
	fields := map[string]interface{}{
		slf.BalanceKey: balance,
		slf.FrozenKey:  frozen,
	}
	err := redislib.Redis().HMSet(slf.HashKey, fields)
	if err != nil {
		msg := fmt.Sprintf("lock or unlock hmset hash %s err: %v", slf.HashKey, err)
		return errors.New(msg)
	}

	return nil
}

// RevertAsset 交易回滚仓位和资产
func (slf *PosCache) RevertAsset(revert repository.AssetSwap) error {
	long, _ := json.Marshal(revert.LongPos)
	short, _ := json.Marshal(revert.ShortPos)
	both, _ := json.Marshal(revert.BothPos)
	balance, _ := json.Marshal(revert.Balance)
	frozen, _ := json.Marshal(revert.Frozen)
	fields := map[string]interface{}{
		slf.FrozenKey:   frozen,
		slf.BalanceKey:  balance,
		slf.LongPosKey:  long,
		slf.ShortPosKey: short,
		slf.BothPosKey:  both,
	}
	return redislib.Redis().HMSet(slf.HashKey, fields)
}

// SetPos set positions
func (slf *PosCache) SetPos(side int32, pos repository.PosSwap, trial bool) error {
	posStr, _ := json.Marshal(pos)
	var key string
	if side == domain.LongPos {
		if trial {
			key = slf.TrialLongPosKey
		} else {
			key = slf.LongPosKey
		}
	} else if side == domain.ShortPos {
		if trial {
			key = slf.TrialShortPosKey
		} else {
			key = slf.ShortPosKey
		}
	} else {
		if trial {
			key = slf.TrialBothPosKey
		} else {
			key = slf.BothPosKey
		}
	}

	err := redislib.Redis().HSet(slf.HashKey, key, string(posStr))
	if err != nil {
		return fmt.Errorf("hset user pos err: %v", err)
	}
	return nil
}

// SetLongPos set long positions
func (slf *PosCache) SetLongPos(pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := redislib.Redis().HSet(slf.HashKey, slf.LongPosKey, string(posStr))
	if err != nil {
		msg := fmt.Sprintf("hset user long pos err: %v", err)
		return errors.New(msg)
	}
	return nil
}

// SetShortPos init short positions
func (slf *PosCache) SetShortPos(pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := redislib.Redis().HSet(slf.HashKey, slf.ShortPosKey, string(posStr))
	if err != nil {
		msg := fmt.Sprintf("hset user short pos err: %v", err)
		return errors.New(msg)
	}
	return nil
}

// SetBothPos init both positions
func (slf *PosCache) SetBothPos(pos repository.PosSwap) error {
	posStr, _ := json.Marshal(pos)
	err := redislib.Redis().HSet(slf.HashKey, slf.BothPosKey, string(posStr))
	if err != nil {
		msg := fmt.Sprintf("hset user both pos err: %v", err)
		return errors.New(msg)
	}
	return nil
}

// SetLeverage init both positions
func (slf *PosCache) SetLeverage(leverage []*repository.Leverage) error {
	leverageStr, _ := json.Marshal(leverage)
	err := redislib.Redis().HSet(slf.HashKey, slf.LeverageKey, string(leverageStr))
	if err != nil {
		msg := fmt.Sprintf("hset user both pos err: %v", err)
		return errors.New(msg)
	}
	return nil
}

// UpdateAnyPos 更新任意仓位
func (slf *PosCache) UpdateAnyPos(pos repository.PosSwap) error {
	posKey := ""
	switch pos.PosSide {
	case domain.LongPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.LongPosSuffix)

	case domain.ShortPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.ShortPosSuffix)

	case domain.BothPos:
		posKey = fmt.Sprintf("%s%s", pos.ContractCode, cache.BothPosSuffix)

	default:
		return nil
	}

	posStr, _ := json.Marshal(pos)
	fields := map[string]interface{}{
		posKey: posStr,
	}

	return redislib.Redis().HMSet(slf.HashKey, fields)
}

func (slf *PosCache) UpdateAssetBalance(asset *repository.AssetSwap) error {
	balance, _ := json.Marshal(asset.Balance)
	fields := map[string]interface{}{
		slf.BalanceKey: balance,
	}

	return redislib.Redis().HMSet(slf.HashKey, fields)
}

// IncrOrDecr increase balance
func (slf *PosCache) IncrOrDecr(asset *repository.AssetSwap) error {
	balance, _ := json.Marshal(asset.Balance)
	// update asset cache
	fields := map[string]interface{}{
		slf.BalanceKey: balance,
	}
	err := redislib.Redis().HMSet(slf.HashKey, fields)
	if err != nil {
		msg := fmt.Sprintf("incr or decr hmset hash %s err: %v", slf.HashKey, err)
		return errors.New(msg)
	}

	return nil
}

func (slf *PosCache) IsolatedBalanceAdd(p payload.BalanceUpdate, posInfo repository.PosSwap, pCache *price.PCache) (payload.BalanceRes, repository.PosSwap) {
	res := payload.BalanceRes{
		AssetLogs:     make([]*repository.MqCmsAsset, 0),
		BillAssetLogs: make([]repository.BillAssetSync, 0),
	}
	if p.Amount.IsZero() {
		return res, posInfo
	}

	posInfo.IsolatedMargin = posInfo.IsolatedMargin.Add(p.Amount)
	slf.OnBalanceAdd(p, &res, p.Amount, p.Currency)

	return res, posInfo
}

// BalanceAdd 用户余额更新
// 由于手续费需要减 所以处理手续费的时候需要先取反
func (slf *PosCache) BalanceAdd(p payload.BalanceUpdate, asset *repository.AssetSwap, pCache *price.PCache, skipTrial bool) (res payload.BalanceRes) {
	if p.Amount.IsZero() {
		return
	}
	res = payload.BalanceRes{
		AssetLogs:      make([]*repository.MqCmsAsset, 0),
		BillAssetLogs:  make([]repository.BillAssetSync, 0),
		TrialAssetLogs: make([]*entity.TrialAsset, 0),
	}

	// Amount 大于0 (加钱)
	if p.Amount.Sign() > 0 {
		asset.AddBalance(p.Currency, p.Amount)
		slf.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// ----------------- Amount 小于0 (扣钱) -----------------

	// 单一保证金
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		// 不跳过体验金
		if !skipTrial {
			trialBalance := asset.TrialCBalance(p.Currency)
			if trialBalance.IsPositive() {
				if afterBalance := trialBalance.Add(p.Amount); afterBalance.LessThan(decimal.Zero) {
					// 体验金不够扣
					trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialBalance)
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					slf.OnTrialBalanceAdd(p, &res, trialBalance.Neg(), p.Currency)
					p.Amount = afterBalance
				} else {
					// 体验金够扣
					trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					slf.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
					return
				}
			}
		}
		asset.AddBalance(p.Currency, p.Amount)
		slf.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// 混合保证金 负数金额会走到这里

	// 不跳过体验金
	if !skipTrial {
		trialCBalance := asset.TrialCBalance(p.Currency)
		// 当前币种余额够扣减
		if trialCBalance.Add(p.Amount).IsPositive() {
			trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
			slf.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
			return
		}

		// 当前币种余额不够 有多少扣多少
		if trialCBalance.IsPositive() {
			trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialCBalance)
			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
			slf.OnTrialBalanceAdd(p, &res, trialCBalance.Neg(), p.Currency)
		}

		// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
		trialCBalance = decimal.Max(decimal.NewFromInt(0), trialCBalance)
		// amount 是负数
		p.Amount = p.Amount.Add(trialCBalance)
		for _, v := range domain.CurrencyList {
			if v == p.Currency {
				continue
			}
			rate := pCache.SpotRate(v, p.Currency)
			vFee, _ := util.RoundCeil(p.Amount.Div(rate), domain.CurrencyPrecision)
			vTrialBalance := asset.TrialCBalance(v)
			// 当前币种余额够扣减
			if vTrialBalance.GreaterThan(decimal.Zero) {
				if vTrialBalance.Add(vFee).IsPositive() {
					trialAssetList := asset.ConsumeTrialBalance(v, vFee.Neg())
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					slf.OnTrialBalanceAdd(p, &res, vFee, v)
					return
				} else {
					trialAssetList := asset.ConsumeTrialBalance(v, vTrialBalance)
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					slf.OnTrialBalanceAdd(p, &res, vTrialBalance.Neg(), v)
					p.Amount = p.Amount.Add(vTrialBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
				}
			}
		}
	}

	cBalance := asset.CBalance(p.Currency)
	// 当前币种余额够扣减
	if cBalance.Add(p.Amount).IsPositive() {
		asset.AddBalance(p.Currency, p.Amount)
		slf.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// 当前币种余额不够 有多少扣多少
	if cBalance.IsPositive() {
		asset.AddBalance(p.Currency, cBalance.Neg())
		slf.OnBalanceAdd(p, &res, cBalance.Neg(), p.Currency)
	}

	// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
	cBalance = decimal.Max(decimal.NewFromInt(0), cBalance)
	// amount 是负数
	p.Amount = p.Amount.Add(cBalance)
	for _, v := range domain.CurrencyList {
		if v == p.Currency {
			continue
		}
		rate := pCache.SpotRate(v, p.Currency)
		vFee, _ := util.RoundCeil(p.Amount.Div(rate), domain.CurrencyPrecision)
		vBalance := asset.CBalance(v)
		// 当前币种余额够扣减
		if vBalance.GreaterThan(decimal.Zero) {
			if vBalance.Add(vFee).IsPositive() {
				asset.AddBalance(v, vFee)
				slf.OnBalanceAdd(p, &res, vFee, v)
				return
			} else {
				asset.AddBalance(v, vBalance.Neg())
				slf.OnBalanceAdd(p, &res, vBalance.Neg(), v)
				p.Amount = p.Amount.Add(vBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
			}
		}
	}

	// 当前币种都已经看过了 发现还没有扣完需要继续扣 以便穿仓补贴
	if !p.Amount.IsZero() {
		asset.AddBalance(p.Currency, p.Amount)
		slf.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}
	return res
}

// OnBalanceAdd 余额更新之后产生的流水
// p:资产变更的基本信息 amount：资产变更的数量 currency:资产变更的币种 联合保证金时有可能跟p中不一致
func (slf *PosCache) OnBalanceAdd(p payload.BalanceUpdate, res *payload.BalanceRes, amount decimal.Decimal, currency string) {
	mqCmsAsset := &repository.MqCmsAsset{}

	util.Assign(&p, mqCmsAsset)
	mqCmsAsset.Currency = strings.ToUpper(currency)
	mqCmsAsset.Amount = amount
	res.AssetLogs = append(res.AssetLogs, mqCmsAsset)

	billSwap := &repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:          p.UID,
			OperateId:    p.OrderId,
			BillId:       util.GenerateId(),
			ContractCode: strings.ToUpper(p.ContractCode),
			Currency:     strings.ToUpper(currency),
			BillType:     p.OperateType,
			Amount:       amount,
			OperateTime:  time.Now().UnixNano(),
		},
	}
	res.BillAssetLogs = append(res.BillAssetLogs, *billSwap)
}

// OnTrialBalanceAdd 体验金余额更新之后产生的流水
// p:资产变更的基本信息 amount：资产变更的数量 currency:资产变更的币种 联合保证金时有可能跟p中不一致
func (slf *PosCache) OnTrialBalanceAdd(p payload.BalanceUpdate, res *payload.BalanceRes, amount decimal.Decimal, currency string) {
	switch p.OperateType {
	case domain.BillTypeFee:
		p.OperateType = domain.BillTypeFeeTrial

	case domain.BillTypeFunding:
		p.OperateType = domain.BillTypeFundingTrial

	case domain.BillTypePlatFee:
		p.OperateType = domain.BillTypePlatFeeTrial

	case domain.BillTypeReal:
		p.OperateType = domain.BillTypeRealTrial

	default:

	}

	mqCmsAsset := &repository.MqCmsAsset{}

	util.Assign(&p, mqCmsAsset)
	mqCmsAsset.Currency = strings.ToUpper(currency)
	mqCmsAsset.TAsset = repository.CmsTrialAsset{
		TAmount: amount,
		TDetail: res.TrialAssetLogs,
	}
	mqCmsAsset.Amount = amount
	res.AssetLogs = append(res.AssetLogs, mqCmsAsset)

	billSwap := &repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:          p.UID,
			OperateId:    p.OrderId,
			BillId:       util.GenerateId(),
			ContractCode: strings.ToUpper(p.ContractCode),
			Currency:     strings.ToUpper(currency),
			BillType:     p.OperateType,
			Amount:       amount,
			OperateTime:  time.Now().UnixNano(),
		},
	}
	res.BillAssetLogs = append(res.BillAssetLogs, *billSwap)
}

func (slf *PosCache) TradeBalanceParam(amount decimal.Decimal, billType int, trade payload.Trade) payload.BalanceUpdate {
	ps := payload.BalanceUpdate{
		ContractCode:     slf.ContractCode(),
		Currency:         strings.ToUpper(slf.Quote),
		OrderId:          slf.TradeId,
		UID:              trade.UID,
		UserType:         trade.UserType,
		Platform:         trade.Platform,
		ChannelCode:      trade.ChannelCode,
		AgentUserId:      trade.AgentUserId,
		AgentStatus:      trade.AgentStatus,
		AgentChannelCode: trade.AgentChannelCode,
		RegisterLanguage: trade.RegisterLanguage,
		StrategyType:     trade.StrategyType,
		DealAmount:       slf.Amount,
		DealPrice:        slf.Price,
		Amount:           amount,
		OperateType:      billType,
		OperateTime:      slf.OperateTime,
		AwardOpIds:       trade.AwardOpIds,
	}
	return ps
}

func (slf *PosCache) FundingBalanceParam(settleId, currency string, amount decimal.Decimal) payload.BalanceUpdate {
	return payload.BalanceUpdate{
		ContractCode: slf.ContractCode(),
		Currency:     currency,
		OrderId:      settleId,
		UID:          slf.UID,
		Amount:       amount,
		OperateTime:  time.Now().UnixNano(),
		OperateType:  domain.BillTypeFunding,
	}
}

func (slf *PosCache) PlatFeeBalanceParam(burstId string, amount decimal.Decimal) payload.BalanceUpdate {
	return payload.BalanceUpdate{
		ContractCode: slf.ContractCode(),
		Currency:     slf.Quote,
		OrderId:      burstId,
		UID:          slf.UID,
		Amount:       amount,
		OperateTime:  time.Now().UnixNano(),
	}
}

// NewLogPosSync 同步仓位变化日志
// @param profitReal 平仓已实现盈亏(仅记录平仓时的记录,用于错误单处理时使用 全局搜索 tradeError 方法)
func (slf *PosCache) NewLogPosSync(_pos repository.PosSwap, _operateTime int64, _tradeId, _orderId string,
	_side, posSide int32, _amount, profitReal decimal.Decimal,
) *repository.LogPosSync {
	logPosSync := repository.LogPosSync{
		Pos: repository.PosSwap{
			PosId: _pos.PosId,
			UID:   _pos.UID, ContractCode: _pos.ContractCode,
			Currency: _pos.Currency, PosAvailable: _pos.PosAvailable,
			PosSide: _pos.PosSide, Leverage: _pos.Leverage,
			Pos:            _pos.Pos,
			IsolatedMargin: _pos.IsolatedMargin,
			TrialMargin:    _pos.TrialMargin,
			MarginMode:     _pos.MarginMode,
			OpenPriceAvg:   _pos.OpenPriceAvg,
			OpenTime:       _pos.OpenTime,
			ProfitReal:     _pos.ProfitReal,
			AwardOpIds:     _pos.AwardOpIds,
		},
		PosSwap: entity.Position{
			Id:              _pos.PosId,
			UID:             _pos.UID,
			UserType:        int(_pos.UserType),
			ContractCode:    _pos.ContractCode,
			Currency:        _pos.Currency,
			AccountType:     _pos.AccountType,
			PosAvailable:    _pos.PosAvailable,
			PosSide:         _pos.PosSide,
			Leverage:        _pos.Leverage,
			Pos:             _pos.Pos,
			IsolatedMargin:  _pos.IsolatedMargin,
			TrialMargin:     _pos.TrialMargin,
			MarginMode:      _pos.MarginMode,
			OpenPriceAvg:    _pos.OpenPriceAvg,
			OpenTime:        _pos.OpenTime,
			ProfitReal:      _pos.ProfitReal,
			Subsidy:         _pos.Subsidy,
			PosStatus:       int32(_pos.PosStatus),
			LiquidationType: int32(_pos.LiquidationType),
			CreateTime:      _operateTime,
			UpdateTime:      _operateTime,
			AwardOpIds:      strings.Join(_pos.AwardOpIds, ","),
		},
		LogPos: swap.LogPos{
			UID:          _pos.UID,
			PosId:        _pos.PosId,
			TradeId:      _tradeId,
			OrderId:      _orderId,
			ContractCode: _pos.ContractCode,
			Side:         _side,
			Pos:          _amount,
			ProfitReal:   profitReal,
			PosSide:      posSide,
			OperateTime:  _operateTime,
		},
	}
	return &logPosSync
}

// OpenBothPos 单向持仓 开仓
func (slf *PosCache) OpenBothPos(asset *repository.AssetSwap, trade payload.Trade, pCache *price.PCache,
	amount, fee, unfrozenMargin decimal.Decimal,
) (payload.PosReply, error) {
	var (
		err   error
		reply = payload.PosReply{
			AssetLogs:     make([]*repository.MqCmsAsset, 0),
			BillAssetLogs: make([]repository.BillAssetSync, 0),
			TrialLogs:     make([]*entity.TrialAsset, 0),
		}
	)

	if asset.BothPos.PosStatus == domain.PosStatusNone || asset.BothPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		oldPosId := asset.BothPos.PosId
		asset.BothPos.NewPos(trade.UID, trade.UserType, trade.MarginMode, trade.Leverage)
		// 机器人持仓仓位ID不变化
		if trade.UserType == 6 && len(oldPosId) > 0 {
			asset.BothPos.PosId = oldPosId
		}
	}

	// calac pos related content
	// 机器人单向持仓自成交不变动仓位开仓均价(相当于自身多空抵消, 无仓位变化)
	if !trade.IsRobotSelfTrade {
		asset.BothPos.OpenPriceAvg = asset.BothPos.CalcOpenPriceAvg(amount, slf.Price)
		logrus.Info(0, fmt.Sprintf("333333 ordiusdt both pos calc open avg price: %s", asset.BothPos.OpenPriceAvg))
	}
	asset.BothPos.Pos = asset.BothPos.Pos.Add(amount)
	asset.BothPos.PosAvailable = asset.BothPos.PosAvailable.Add(amount)
	logrus.Info(0, "================ OpenBothPos [DecrFrozen]", trade.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "trade", fmt.Sprintf("%+v", trade), "unfrozenMargin", unfrozenMargin)
	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), unfrozenMargin)
	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), unfrozenMargin)
	logrus.Info(0, "================ OpenBothPos [DecrFrozen]", trade.UID, "unfrozenMargin", unfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	// 计算手续费
	bParam := slf.TradeBalanceParam(fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)

	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 计算已用
	posMargin := asset.BothPos.CalcPosMargin(amount.Abs(), slf.Price, fee) // 仓位保证金
	if asset.BothPos.Isolated() {
		asset.BothPos.IsolatedMargin = asset.BothPos.IsolatedMargin.Add(posMargin)
	}
	// 计算占用体验金
	trialBillList := make([]*entity.TrialAsset, 0)
	asset.BothPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.BothPos, pCache)
	reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

	err = slf.UpdateBothPos(asset)
	if err != nil {
		msg := fmt.Sprintf("update both pos err:%+v,userAsset:%+v", err, *asset)
		return reply, errors.New(msg)
	}

	haveTrial := 0
	if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}

	// 统计用户开仓数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
			UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.BothPos.OpenTime,
			HoldPosValue: slf.Price.Mul(amount.Abs()), LatestPosId: asset.BothPos.PosId,
		})
	}

	// 创建仓位变动日志
	reply.LogPos = slf.NewLogPosSync(asset.BothPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, amount, decimal.Zero)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.BothPos.PosId,
		Pos:          asset.BothPos.Pos,
		OpenPriceAvg: asset.BothPos.OpenPriceAvg,
		PosSide:      asset.BothPos.PosSide,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
		if err != nil {
			logrus.Errorf("OpenBothPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, fee.Neg(), balance)
	}()

	return reply, nil
}

// CloseBothPos 单向持仓 平仓
func (slf *PosCache) CloseBothPos(asset *repository.AssetSwap, trade payload.Trade, pCache *price.PCache, amount, fee, unfrozenMargin decimal.Decimal) (payload.PosReply, domain.Code, error) {
	var (
		reply = payload.PosReply{
			AssetLogs:     make([]*repository.MqCmsAsset, 0),
			BillAssetLogs: make([]repository.BillAssetSync, 0),
			TrialLogs:     make([]*entity.TrialAsset, 0),
		}
		compareFee, compareProfitReal = fee, decimal.Zero
		revokeSide                    = domain.Sell
	)

	if asset.BothPos.Pos.LessThan(decimal.Zero) {
		revokeSide = domain.Buy
	}

	if asset.BothPos.Leverage < 1 {
		logrus.Info(0, "CloseBothPos 251114 long pos is leverage < 1", fmt.Sprintf("%+v", asset.BothPos))
		return reply, domain.Code251114, errors.New("251114 long pos is leverage < 1")
	}

	haveTrial := 0
	if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
		// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
		if asset.BothPos.IsolatedMargin.LessThan(asset.BothPos.TrialMargin) {
			asset.BothPos.TrialMargin = asset.BothPos.IsolatedMargin
		}
	}

	profitReal := asset.BothPos.CalcProfitReal(slf.Price, amount.Abs())
	// 如果是机器人自成交, 不计算平仓盈亏
	if trade.IsRobotSelfTrade {
		profitReal = decimal.Zero
	}
	compareProfitReal = profitReal

	originPos := asset.BothPos.Pos
	originPosIsolateMargin := asset.BothPos.IsolatedMargin

	asset.BothPos.Pos = asset.BothPos.Pos.Add(amount)
	asset.BothPos.ProfitReal = asset.BothPos.ProfitReal.Add(profitReal)
	logrus.Info(0, "================ CloseBothPos [DecrFrozen]", trade.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "trade", fmt.Sprintf("%+v", trade), "unfrozenMargin", unfrozenMargin)
	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), unfrozenMargin)
	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), unfrozenMargin)
	logrus.Info(0, "================ CloseBothPos [DecrFrozen]", trade.UID, "unfrozenMargin", unfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	// TODO delete me
	if asset.BothPos.ContractCode == "ORDI-USDT" {
		logrus.Info(0, fmt.Sprintf("ordiusdt both pos calc --- ProfitReal asset.BothPos: %+v profitReal:%s",
			asset.BothPos, profitReal))
	}

	// 计算已实现盈亏
	billProfitReal := profitReal
	skipTrialDiscount := true
	// 亏钱先扣体检金
	if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
		skipTrialDiscount = false
		trialLoss := decimal.Zero
		if billProfitReal.Abs().GreaterThanOrEqual(asset.BothPos.TrialMargin) {
			billProfitReal = billProfitReal.Add(asset.BothPos.TrialMargin)
			trialLoss = asset.BothPos.TrialMargin.Neg()
			asset.BothPos.TrialMargin = decimal.Zero
		} else {
			asset.BothPos.TrialMargin = asset.BothPos.TrialMargin.Add(billProfitReal)
			trialLoss = billProfitReal
			billProfitReal = decimal.Zero
		}

		if asset.BothPos.Isolated() {
			asset.BothPos.IsolatedMargin = asset.BothPos.IsolatedMargin.Add(trialLoss)
		}

		// 开仓时已经消耗过体验金原始数据，所以只需要处理仓位上的体验金，生成账单
		tProfitParam := slf.TradeBalanceParam(trialLoss, domain.BillTypeRealTrial, trade)
		tProfitBillList := payload.BalanceRes{
			AssetLogs:      make([]*repository.MqCmsAsset, 0),
			BillAssetLogs:  make([]repository.BillAssetSync, 0),
			TrialAssetLogs: make([]*entity.TrialAsset, 0),
		}
		slf.OnTrialBalanceAdd(tProfitParam, &tProfitBillList, trialLoss, tProfitParam.Currency)
		reply.AssetLogs = append(reply.AssetLogs, tProfitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, tProfitBillList.BillAssetLogs...)
	}
	if !billProfitReal.IsZero() {
		bProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeReal, trade)
		profitBillList := slf.BalanceAdd(bProfitParam, asset, pCache, skipTrialDiscount)
		reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
	}

	// 计算手续费
	bFeeParam := slf.TradeBalanceParam(fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bFeeParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)
	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	if slf.TradeType == domain.TradeTypeDirect {
		if asset.BothPos.PosAvailable.Abs().LessThan(amount.Abs()) {
			log.Printf("dark deal close both pos pos available less than amount.Abs() uid:%s PosAvailable:%s amount.Abs():%s",
				slf.UID, asset.BothPos.PosAvailable.Abs(), amount.Abs())
			return reply, domain.Code251119, errors.New("dark deal close bith pos pos available less than amount.Abs()")
		}
		asset.BothPos.PosAvailable = asset.BothPos.PosAvailable.Add(amount)
	}

	asset.BothPos.LiquidationType = trade.LiquidationType
	closePosMargin := decimal.Zero // 本次平仓仓位保证金
	if asset.BothPos.Isolated() {
		// 逐仓扣减仓位保证金
		closePosMargin = amount.Abs().Div(originPos.Abs()).Mul(originPosIsolateMargin).Abs().Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(trade.Fee))
		if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
			asset.BothPos.TrialMargin = asset.BothPos.TrialMargin.Add(subDeltaAmount)
		}

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		asset.BothPos.IsolatedMargin = asset.BothPos.IsolatedMargin.Add(subDeltaAmount)

		if asset.BothPos.LiquidationType != domain.LiquidationTypeBurst {
			// 逐仓的 IsolatedMargin 包含体验金保证金，所以穿仓需要用体验金的保证金填补
			if asset.BothPos.TrialMargin.LessThan(decimal.Zero) {
				if asset.BothPos.IsolatedMargin.LessThan(decimal.Zero) {
					asset.BothPos.IsolatedMargin = decimal.Min(decimal.NewFromInt(0), asset.BothPos.IsolatedMargin.Add(asset.BothPos.TrialMargin.Neg()))
				}
				asset.BothPos.TrialMargin = decimal.Zero
			}

			// 平穿逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if asset.BothPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := asset.BothPos.IsolatedMargin.Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)

				// 逐仓保证金加穿仓补贴的费用
				asset.BothPos.IsolatedMargin = asset.BothPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(slf.Quote, bankruptAmount)

				asset.BothPos.Subsidy = asset.BothPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close isolated both pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, slf.UID)
			}
		}

	} else {
		closePosMargin = slf.Price.Mul(amount.Abs()).Div(decimal.NewFromInt(int64(asset.BothPos.Leverage))).Abs().Truncate(domain.CurrencyPrecision)

		// 只有盈亏为负数时候从保证金上扣
		if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
			subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
			asset.BothPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.BothPos.TrialMargin.Add(subDeltaAmount))
		}

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if asset.BothPos.LiquidationType != domain.LiquidationTypeBurst {
			totalBalance := asset.CBalance(slf.Quote)
			rate := decimal.NewFromInt(1)
			if asset.AssetMode == domain.AssetMode {
				var err error = nil
				totalBalance, err = asset.TotalJoinBalance(pCache)
				if err != nil {
					log.Println("CloseBothPos close", err)
					return reply, domain.Code252407, err
				}
				rate = pCache.SpotURate(slf.Quote)
				if rate.IsZero() {
					log.Println("CloseBothPos close rate is zero")
					return reply, domain.Code252407, err // todo Code252408 由于已经联调下次修改需要更新
				}
			}

			// crossBalance := totalBalance.Sub(slf.HoldCostTotal(pCache, asset.AssetMode, slf.Quote)) // todo 旧逻辑待评估

			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
				if totalBalance.Abs().LessThan(asset.BothPos.TrialMargin.Mul(rate)) {
					totalBalance = totalBalance.Add(totalBalance.Neg())
					asset.BothPos.TrialMargin = asset.BothPos.TrialMargin.Add(totalBalance.Div(rate))
				} else {
					totalBalance = totalBalance.Add(asset.BothPos.TrialMargin.Mul(rate))
					asset.BothPos.TrialMargin = decimal.Zero
				}
			}

			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(slf.Quote, bankruptAmount)
				asset.BothPos.Subsidy = asset.BothPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close both short pos the balance of user drop below zero, value:%+v uid:%s", bankruptAmount, slf.UID)
			}
		}
	}

	// 平仓后如果仓位为0,重新生成PosId
	if asset.BothPos.Pos.IsZero() {
		reply.ClearPos = asset.BothPos
		asset.BothPos.Clear() // 清空现有持仓
		if trade.IsRobotSelfTrade {
			asset.BothPos.OpenPriceAvg = reply.ClearPos.OpenPriceAvg
		}
	}

	err := slf.UpdateBothPos(asset)
	if err != nil {
		log.Printf("hmset hash %s err: %v", slf.HashKey, err)
		if amount.GreaterThan(decimal.Zero) {
			return reply, domain.Code251108, err
		}
		return reply, domain.Code251109, err
	}

	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
		if tempProfit.Sign() > 0 {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(amount), TotalCloseMargin: closePosMargin,
			})
		} else {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(amount), TotalCloseMargin: closePosMargin,
			})
		}
	}

	go func() {
		// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
		totalBalance := asset.CBalance(trade.FeeCurrency)
		if asset.AssetMode == domain.AssetMode {
			totalBalance, err = asset.TotalJoinBalance(pCache)
			if err != nil {
				log.Println("CloseBothPos revoke", err)
			}
		}

		if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
			match.Service.ConditionCancel(asset.BothPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
		}
		// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
		if asset.BothPos.Pos.IsZero() {
			RemoveRivalScore(asset.BothPos.UID, asset.BothPos.ContractCode, asset.BothPos.PosSide, false) // 删除对手方评分
			match.Service.ConditionCancel(asset.BothPos.UID, slf.Base, slf.Quote, domain.Close, revokeSide, asset.BothPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
		}
	}()
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
		if err != nil {
			logrus.Errorf("CloseBothPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(fee))
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, fee.Neg(), balance)
	}()

	reply.LogPos = slf.NewLogPosSync(asset.BothPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, amount, decimal.Zero)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.BothPos.PosId,
		Pos:          asset.BothPos.Pos,
		OpenPriceAvg: asset.BothPos.OpenPriceAvg,
		PosSide:      asset.BothPos.PosSide,
		ProfitReal:   profitReal,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	return reply, http.StatusOK, nil
}

// OpenLongPos 开多仓
//   - originPrice: 下单冻结时的价格
//   - trade: 需要区分 taker 还是 maker
func (slf *PosCache) OpenLongPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (payload.PosReply, error) {
	if asset.LongPos.PosStatus == domain.PosStatusNone || asset.LongPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		asset.LongPos.NewPos(trade.UID, trade.UserType, trade.MarginMode, trade.Leverage)
	}
	reply := payload.PosReply{
		AssetLogs:     make([]*repository.MqCmsAsset, 0),
		BillAssetLogs: make([]repository.BillAssetSync, 0),
		TrialLogs:     make([]*entity.TrialAsset, 0),
	}

	// calac pos related content
	asset.LongPos.OpenPriceAvg = asset.LongPos.CalcOpenPriceAvg(slf.Amount, slf.Price)
	asset.LongPos.Pos = asset.LongPos.Pos.Add(slf.Amount)
	asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Add(slf.Amount)
	logrus.Info(0, "================ OpenLongPos [DecrFrozen]", trade.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "trade", fmt.Sprintf("%+v", trade), "trade.UnfrozenMargin", trade.UnfrozenMargin)
	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), trade.UnfrozenMargin)
	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), trade.UnfrozenMargin)
	logrus.Info(0, "================ OpenLongPos [DecrFrozen]", trade.UID, "trade.UnfrozenMargin", trade.UnfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	// 计算手续费
	bParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
		if err != nil {
			logrus.Errorf("OpenLongPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	}()
	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)
	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 计算已用
	posMargin := asset.LongPos.CalcPosMargin(slf.Amount, slf.Price, trade.Fee) // 仓位保证金
	// 计算占用体验金
	trialBillList := make([]*entity.TrialAsset, 0)
	asset.LongPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.LongPos, pCache)
	reply.TrialLogs = append(reply.TrialLogs, trialBillList...)
	if asset.LongPos.Isolated() {
		asset.LongPos.IsolatedMargin = asset.LongPos.IsolatedMargin.Add(posMargin)
	}

	// update asset cache
	err := slf.UpdateLongPos(asset)
	if err != nil {
		msg := fmt.Sprintf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
		logrus.Errorf("revert hmset hash: %s with err: %v", slf.HashKey, err)
		return reply, errors.New(msg)
	}

	haveTrial := 0
	if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}

	// 统计用户开仓数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
			UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.LongPos.OpenTime,
			HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: asset.LongPos.PosId,
		})
	}

	reply.LogPos = slf.NewLogPosSync(asset.LongPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, slf.Amount, decimal.Zero)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.LongPos.PosId,
		Pos:          asset.LongPos.Pos,
		OpenPriceAvg: asset.LongPos.OpenPriceAvg,
		PosSide:      asset.LongPos.PosSide,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	return reply, nil
}

// CloseLongPos 平多仓
//   - trade: 区分 taker 还是 maker
func (slf *PosCache) CloseLongPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (payload.PosReply, domain.Code, error) {
	var (
		reply = payload.PosReply{
			AssetLogs:     make([]*repository.MqCmsAsset, 0),
			BillAssetLogs: make([]repository.BillAssetSync, 0),
			TrialLogs:     make([]*entity.TrialAsset, 0),
		}
		compareFee, compareProfitReal = trade.Fee, decimal.Zero
	)

	haveTrial := 0
	if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
		// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
		if asset.LongPos.IsolatedMargin.LessThan(asset.LongPos.TrialMargin) {
			asset.LongPos.TrialMargin = asset.LongPos.IsolatedMargin
		}
	}

	profitReal := asset.LongPos.CalcProfitReal(slf.Price, slf.Amount)
	compareProfitReal = profitReal

	originPosAmount := asset.LongPos.Pos
	originPosIsolateMargin := asset.LongPos.IsolatedMargin
	asset.LongPos.Pos = asset.LongPos.Pos.Sub(slf.Amount)
	if asset.LongPos.Pos.Sign() < 0 {
		logrus.Info(0, "251111 pos is negative after closing long")
		return reply, domain.Code251111, errors.New("pos is negative after closing long")
	}

	if asset.LongPos.Leverage < 1 {
		logrus.Info(0, "CloseLongPos 251114 long pos is leverage < 1", fmt.Sprintf("%+v", asset.LongPos))
		return reply, domain.Code251114, errors.New("251114 long pos is leverage < 1")
	}

	asset.LongPos.ProfitReal = asset.LongPos.ProfitReal.Add(profitReal)

	// 计算已实现盈亏
	billProfitReal := profitReal
	skipTrialDiscount := true
	// 亏钱先扣体检金
	if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
		skipTrialDiscount = false
		trialLoss := decimal.Zero
		if billProfitReal.Abs().GreaterThanOrEqual(asset.LongPos.TrialMargin) {
			billProfitReal = billProfitReal.Add(asset.LongPos.TrialMargin)
			trialLoss = asset.LongPos.TrialMargin.Neg()
			asset.LongPos.TrialMargin = decimal.Zero
		} else {
			asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(billProfitReal)
			trialLoss = billProfitReal
			billProfitReal = decimal.Zero
		}

		if asset.LongPos.Isolated() {
			asset.LongPos.IsolatedMargin = asset.LongPos.IsolatedMargin.Add(trialLoss)
		}

		// 开仓时已经消耗过体验金原始数据，所以只需要处理仓位上的体验金，生成账单
		tProfitParam := slf.TradeBalanceParam(trialLoss, domain.BillTypeRealTrial, trade)
		tProfitBillList := payload.BalanceRes{
			AssetLogs:      make([]*repository.MqCmsAsset, 0),
			BillAssetLogs:  make([]repository.BillAssetSync, 0),
			TrialAssetLogs: make([]*entity.TrialAsset, 0),
		}
		slf.OnTrialBalanceAdd(tProfitParam, &tProfitBillList, trialLoss, tProfitParam.Currency)
		reply.AssetLogs = append(reply.AssetLogs, tProfitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, tProfitBillList.BillAssetLogs...)
	}
	if !billProfitReal.IsZero() {
		bProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeReal, trade)
		profitBillList := slf.BalanceAdd(bProfitParam, asset, pCache, skipTrialDiscount)
		reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
	}

	// 计算手续费
	bFeeParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bFeeParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)
	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)
	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	if slf.TradeType == domain.TradeTypeDirect {
		if asset.LongPos.PosAvailable.LessThan(slf.Amount) {
			log.Printf("dark deal close long pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
				slf.UID, asset.LongPos.PosAvailable, slf.Amount)
			return reply, domain.Code251119, errors.New("dark deal close long pos pos available less than slf.amount")
		}
		asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Sub(slf.Amount)
	}

	asset.LongPos.LiquidationType = trade.LiquidationType
	closePosMargin := decimal.Zero // 本次平仓仓位保证金
	if asset.LongPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = slf.Amount.Div(originPosAmount).Mul(originPosIsolateMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(trade.Fee))
		if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
			asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(subDeltaAmount)
		}

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		asset.LongPos.IsolatedMargin = asset.LongPos.IsolatedMargin.Add(subDeltaAmount)

		if asset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			// 逐仓的 IsolatedMargin 包含体验金保证金，所以穿仓需要用体验金的保证金填补
			if asset.LongPos.TrialMargin.LessThan(decimal.Zero) {
				if asset.LongPos.IsolatedMargin.LessThan(decimal.Zero) {
					asset.LongPos.IsolatedMargin = decimal.Min(decimal.NewFromInt(0), asset.LongPos.IsolatedMargin.Add(asset.LongPos.TrialMargin.Neg()))
				}
				asset.LongPos.TrialMargin = decimal.Zero
			}

			// 平穿逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if asset.LongPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := asset.LongPos.IsolatedMargin.Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)

				// 逐仓保证金加穿仓补贴的费用
				asset.LongPos.IsolatedMargin = asset.LongPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(slf.Quote, bankruptAmount)

				asset.LongPos.Subsidy = asset.LongPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close isolated long pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, slf.UID)
			}
		}

	} else {
		closePosMargin = slf.Price.Mul(slf.Amount).Div(decimal.NewFromInt(int64(asset.LongPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 只有盈亏为负数时候从保证金上扣
		if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
			subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
			asset.LongPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.LongPos.TrialMargin.Add(subDeltaAmount))
		}

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if asset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			totalBalance := asset.CBalance(slf.Quote)
			rate := decimal.NewFromInt(1)
			if asset.AssetMode == domain.AssetMode {
				var err error = nil
				totalBalance, err = asset.TotalJoinBalance(pCache)
				if err != nil {
					log.Println("CloseLongPos close", err)
					return reply, domain.Code252407, err
				}
				rate = pCache.SpotURate(slf.Quote)
				if rate.IsZero() {
					log.Println("CloseLongPos close rate is zero")
					return reply, domain.Code252407, err // todo Code252408 由于已经联调下次修改需要更新
				}
			}

			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
				if totalBalance.Abs().LessThanOrEqual(asset.LongPos.TrialMargin.Mul(rate)) {
					totalBalance = totalBalance.Add(totalBalance.Neg())
					asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(totalBalance.Div(rate))
				} else {
					totalBalance = totalBalance.Add(asset.LongPos.TrialMargin.Mul(rate))
					asset.LongPos.TrialMargin = decimal.Zero
				}
			}

			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(slf.Quote, bankruptAmount)
				asset.LongPos.Subsidy = asset.LongPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close cross long pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, slf.UID)
			}
		}
	}

	// 平仓后如果仓位归0, 生成历史持仓记录
	if asset.LongPos.Pos.Sign() <= 0 {
		reply.ClearPos = asset.LongPos
		asset.LongPos.Clear() // 清空现有持仓
	}
	err := slf.UpdateLongPos(asset)
	if err != nil {
		log.Printf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
		return reply, domain.Code251109, err
	}
	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
		if tempProfit.Sign() > 0 {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		} else {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		}
	}

	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
		if tempProfit.Sign() > 0 {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		} else {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		}
	}

	go func() {
		// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
		totalBalance := asset.CBalance(trade.FeeCurrency)
		if asset.AssetMode == domain.AssetMode {
			totalBalance, err = asset.TotalJoinBalance(pCache)
			if err != nil {
				log.Println("CloseLongPos revoke", err)
			}
		}
		if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
			match.Service.ConditionCancel(asset.LongPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
		}
		// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
		if asset.LongPos.Pos.Sign() <= 0 {
			RemoveRivalScore(asset.LongPos.UID, asset.LongPos.ContractCode, asset.LongPos.PosSide, false) // 删除对手方评分
			match.Service.ConditionCancel(asset.LongPos.UID, slf.Base, slf.Quote, domain.Close, domain.Sell, asset.LongPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
		}
	}()
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
		if err != nil {
			logrus.Errorf("CloseLongPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(trade.Fee))
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	}()

	reply.LogPos = slf.NewLogPosSync(asset.LongPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, slf.Amount.Neg(), profitReal)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.LongPos.PosId,
		Pos:          asset.LongPos.Pos,
		OpenPriceAvg: asset.LongPos.OpenPriceAvg,
		PosSide:      asset.LongPos.PosSide,
		ProfitReal:   profitReal,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	return reply, http.StatusOK, nil
}

// OpenShortPos 开空仓
//   - originPrice: 下单冻结时的价格
//   - trade: 需要区分 taker 还是 maker
func (slf *PosCache) OpenShortPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (payload.PosReply, error) {
	if asset.ShortPos.PosStatus == domain.PosStatusNone || asset.ShortPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		asset.ShortPos.NewPos(trade.UID, trade.UserType, trade.MarginMode, trade.Leverage)
	}
	reply := payload.PosReply{
		AssetLogs:     make([]*repository.MqCmsAsset, 0),
		BillAssetLogs: make([]repository.BillAssetSync, 0),
		TrialLogs:     make([]*entity.TrialAsset, 0),
	}

	// calac pos related content
	asset.ShortPos.OpenPriceAvg = asset.ShortPos.CalcOpenPriceAvg(slf.Amount, slf.Price)
	asset.ShortPos.Pos = asset.ShortPos.Pos.Add(slf.Amount)
	asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Add(slf.Amount)
	logrus.Info(0, "================ OpenShortPos [DecrFrozen]", trade.UID, fmt.Sprintf("asset.Frozen %+v", asset.Frozen), "trade", fmt.Sprintf("%+v", trade), "trade.UnfrozenMargin", trade.UnfrozenMargin)
	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), trade.UnfrozenMargin)
	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), trade.UnfrozenMargin)
	logrus.Info(0, "================ OpenShortPos [DecrFrozen]", trade.UID, "trade.UnfrozenMargin", trade.UnfrozenMargin, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	// 计算手续费
	bParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
		if err != nil {
			logrus.Errorf("OpenShortPos GetCurrencyTotalBalance uid: %s userType:%d currency: %s error: %v", trade.UID, trade.UserType, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	}()

	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)
	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 计算已用
	posMargin := asset.ShortPos.CalcPosMargin(slf.Amount, slf.Price, trade.Fee) // 仓位保证金
	// 计算占用体验金
	trialBillList := make([]*entity.TrialAsset, 0)
	asset.ShortPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.ShortPos, pCache)
	reply.TrialLogs = append(reply.TrialLogs, trialBillList...)
	if asset.ShortPos.Isolated() {
		asset.ShortPos.IsolatedMargin = asset.ShortPos.IsolatedMargin.Add(posMargin)
	}

	// update asset cache
	err := slf.UpdateShortPos(asset)
	if err != nil {
		msg := fmt.Sprintf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
		return reply, errors.New(msg)
	}

	haveTrial := 0
	if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
	}

	// 统计用户开仓数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
			UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.ShortPos.OpenTime,
			HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: asset.ShortPos.PosId,
		})
	}

	reply.LogPos = slf.NewLogPosSync(asset.ShortPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, slf.Amount, decimal.Zero)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.ShortPos.PosId,
		Pos:          asset.ShortPos.Pos,
		OpenPriceAvg: asset.ShortPos.OpenPriceAvg,
		PosSide:      asset.ShortPos.PosSide,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	return reply, nil
}

// CloseShortPos 平空仓
//   - trade: 区分 taker 还是 maker
func (slf *PosCache) CloseShortPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (payload.PosReply, domain.Code, error) {
	var (
		reply = payload.PosReply{
			AssetLogs:     make([]*repository.MqCmsAsset, 0),
			BillAssetLogs: make([]repository.BillAssetSync, 0),
			TrialLogs:     make([]*entity.TrialAsset, 0),
		}
		compareFee, compareProfitReal = trade.Fee, decimal.Zero
	)

	haveTrial := 0
	if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
		haveTrial = 1
		// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
		if asset.ShortPos.IsolatedMargin.LessThan(asset.ShortPos.TrialMargin) {
			asset.ShortPos.TrialMargin = asset.ShortPos.IsolatedMargin
		}
	}

	profitReal := asset.ShortPos.CalcProfitReal(slf.Price, slf.Amount)
	compareProfitReal = profitReal

	// 记录仓位的累计平仓盈亏
	originPosAmount := asset.ShortPos.Pos
	originPosIsolateMargin := asset.ShortPos.IsolatedMargin
	asset.ShortPos.Pos = asset.ShortPos.Pos.Sub(slf.Amount)
	if asset.ShortPos.Pos.Sign() < 0 {
		logrus.Info(0, "251110 pos is negative after closing short")
		return reply, domain.Code251110, errors.New("251110 pos is negative after closing short")
	}

	if asset.ShortPos.Leverage < 1 {
		logrus.Info(0, "CloseShortPos 251114 short pos is leverage < 1", fmt.Sprintf("%+v", asset.ShortPos))
		return reply, domain.Code251114, errors.New("251114 short pos is leverage < 1")
	}
	asset.ShortPos.ProfitReal = asset.ShortPos.ProfitReal.Add(profitReal)

	// 计算已实现盈亏
	billProfitReal := profitReal
	skipTrialDiscount := true
	// 亏钱先扣体检金
	if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
		skipTrialDiscount = false
		trialLoss := decimal.Zero
		if billProfitReal.Abs().GreaterThanOrEqual(asset.ShortPos.TrialMargin) {
			billProfitReal = billProfitReal.Add(asset.ShortPos.TrialMargin)
			trialLoss = asset.ShortPos.TrialMargin.Neg()
			asset.ShortPos.TrialMargin = decimal.Zero
		} else {
			asset.ShortPos.TrialMargin = asset.ShortPos.TrialMargin.Add(billProfitReal)
			trialLoss = billProfitReal
			billProfitReal = decimal.Zero
		}

		if asset.ShortPos.Isolated() {
			asset.ShortPos.IsolatedMargin = asset.ShortPos.IsolatedMargin.Add(trialLoss)
		}

		// 开仓时已经消耗过体验金原始数据，所以只需要处理仓位上的体验金，生成账单
		tProfitParam := slf.TradeBalanceParam(trialLoss, domain.BillTypeRealTrial, trade)
		tProfitBillList := payload.BalanceRes{
			AssetLogs:      make([]*repository.MqCmsAsset, 0),
			BillAssetLogs:  make([]repository.BillAssetSync, 0),
			TrialAssetLogs: make([]*entity.TrialAsset, 0),
		}
		slf.OnTrialBalanceAdd(tProfitParam, &tProfitBillList, trialLoss, tProfitParam.Currency)
		reply.AssetLogs = append(reply.AssetLogs, tProfitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, tProfitBillList.BillAssetLogs...)
	}
	if !billProfitReal.IsZero() {
		bProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeReal, trade)
		profitBillList := slf.BalanceAdd(bProfitParam, asset, pCache, skipTrialDiscount)
		reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
		reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
	}

	// 计算手续费
	bFeeParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
	feeBillList := slf.BalanceAdd(bFeeParam, asset, pCache, false)
	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)
	// 手续费明细
	feeDetails := make([]payload.FeeDetail, 0)
	for _, detail := range feeBillList.AssetLogs {
		feeDetails = append(feeDetails, payload.FeeDetail{
			Currency: detail.Currency,
			Amount:   detail.Amount,
			Price:    pCache.SpotURate(detail.Currency),
		})
	}

	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	if slf.TradeType == domain.TradeTypeDirect {
		if asset.ShortPos.PosAvailable.LessThan(slf.Amount) {
			log.Printf("dark deal close pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
				slf.UID, asset.ShortPos.PosAvailable, slf.Amount)
			return reply, domain.Code251119, errors.New("dark deal close pos pos available less than req.amount")
		}
		asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Sub(slf.Amount)
	}

	asset.ShortPos.LiquidationType = trade.LiquidationType
	closePosMargin := decimal.Zero // 本次平仓仓位保证金
	if asset.ShortPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = slf.Amount.Div(originPosAmount).Mul(originPosIsolateMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(trade.Fee))
		if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
			asset.ShortPos.TrialMargin = asset.ShortPos.TrialMargin.Add(subDeltaAmount)
		}

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		asset.ShortPos.IsolatedMargin = asset.ShortPos.IsolatedMargin.Add(subDeltaAmount)

		if asset.ShortPos.LiquidationType != domain.LiquidationTypeBurst {
			// 逐仓的 IsolatedMargin 包含体验金保证金，所以穿仓需要用体验金的保证金填补
			if asset.ShortPos.TrialMargin.LessThan(decimal.Zero) {
				if asset.ShortPos.IsolatedMargin.LessThan(decimal.Zero) {
					asset.ShortPos.IsolatedMargin = decimal.Min(decimal.NewFromInt(0), asset.ShortPos.IsolatedMargin.Add(asset.ShortPos.TrialMargin.Neg()))
				}
				asset.ShortPos.TrialMargin = decimal.Zero
			}

			// 平穿逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if asset.ShortPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := asset.ShortPos.IsolatedMargin.Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)

				// 逐仓保证金加穿仓补贴的费用
				asset.ShortPos.IsolatedMargin = asset.ShortPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(slf.Quote, bankruptAmount)

				asset.ShortPos.Subsidy = asset.ShortPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close isolated short pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, slf.UID)
			}
		}

	} else {
		closePosMargin = slf.Price.Mul(slf.Amount).Div(decimal.NewFromInt(int64(asset.ShortPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 只有盈亏为负数时候从保证金上扣
		if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
			subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
			asset.ShortPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.ShortPos.TrialMargin.Add(subDeltaAmount))
		}

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if asset.ShortPos.LiquidationType != domain.LiquidationTypeBurst {
			totalBalance := asset.CBalance(slf.Quote)
			rate := decimal.NewFromInt(1)
			if asset.AssetMode == domain.AssetMode {
				var err error = nil
				totalBalance, err = asset.TotalJoinBalance(pCache)
				if err != nil {
					log.Println("CloseShortPos close", err)
					return reply, domain.Code252407, err
				}
				rate = pCache.SpotURate(slf.Quote)
				if rate.IsZero() {
					log.Println("CloseShortPos close rate is zero")
					return reply, domain.Code252407, err // todo Code252408 由于已经联调下次修改需要更新
				}
			}

			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
				if totalBalance.Abs().LessThanOrEqual(asset.ShortPos.TrialMargin.Mul(rate)) {
					totalBalance = totalBalance.Add(totalBalance.Neg())
					asset.ShortPos.TrialMargin = asset.ShortPos.TrialMargin.Add(totalBalance.Div(rate))
				} else {
					totalBalance = totalBalance.Add(asset.ShortPos.TrialMargin.Mul(rate))
					asset.ShortPos.TrialMargin = decimal.Zero
				}
			}

			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs)
				// 账户余额需要加上穿仓补贴的费用
				asset.AddBalance(trade.FeeCurrency, bankruptAmount)
				asset.ShortPos.Subsidy = asset.ShortPos.Subsidy.Add(bankruptAmount) // 记录仓位的穿仓补贴
				log.Printf("close cross short pos the balance of user drop below zero, value:%s uid:%s", bankruptAmount, slf.UID)
			}
		}
	}

	// 平仓后如果仓位平空,重新生成PosId
	if asset.ShortPos.Pos.Sign() <= 0 {
		reply.ClearPos = asset.ShortPos
		asset.ShortPos.Clear() // 清空现有持仓
	}
	err := slf.UpdateShortPos(asset)
	if err != nil {
		log.Printf("hmset hash %s err: %v", slf.HashKey, err)
		return reply, domain.Code251108, err
	}

	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	if !domain.RobotUsers.HasKey(slf.UID) {
		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
		if tempProfit.Sign() > 0 {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		} else {
			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
			})
		}
	}

	go func() {
		// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
		totalBalance := asset.CBalance(trade.FeeCurrency)
		if asset.AssetMode == domain.AssetMode {
			totalBalance, err = asset.TotalJoinBalance(pCache)
			if err != nil {
				log.Println("CloseShortPos revoke", err)
			}
		}
		if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
			match.Service.ConditionCancel(asset.ShortPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
		}
		// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
		if asset.ShortPos.Pos.Sign() <= 0 {
			RemoveRivalScore(asset.ShortPos.UID, asset.ShortPos.ContractCode, asset.ShortPos.PosSide, false) // 删除对手方评分
			match.Service.ConditionCancel(asset.ShortPos.UID, slf.Base, slf.Quote, domain.Close, domain.Buy, asset.ShortPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
		}
	}()
	// 账本记录
	go func() {
		if trade.UserType == domain.UserTypePlatformRobot {
			return
		}
		balance, err := GetCurrencyTotalBalance(trade.UID, bFeeParam.Currency)
		if err != nil {
			logrus.Errorf("CloseShortPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
			return
		}
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(trade.Fee))
		es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	}()

	reply.LogPos = slf.NewLogPosSync(asset.ShortPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
		trade.PosSide, slf.Amount, profitReal)
	reply.Reply = payload.Reply{
		UID:          asset.UID,
		PosId:        asset.ShortPos.PosId,
		Pos:          asset.ShortPos.Pos,
		OpenPriceAvg: asset.ShortPos.OpenPriceAvg,
		PosSide:      asset.ShortPos.PosSide,
		ProfitReal:   profitReal,
		FeeDetail:    feeDetails,
		HaveTrial:    haveTrial,
	}
	return reply, http.StatusOK, nil
}

// bankruptSubsidy 平仓穿仓补贴
func (slf *PosCache) bankruptSubsidy(amount decimal.Decimal, assetLogs *[]*repository.MqCmsAsset, _billAsset *[]repository.BillAssetSync, isTrials ...bool) {
	bankrupt := &repository.MqCmsAsset{
		ContractCode: slf.ContractCode(),
		Currency:     slf.Quote,
		OrderId:      slf.TradeId,
		UID:          slf.UID,
		Amount:       amount,
		OperateType:  domain.BillTypeCloseSubsidy,
		OperateTime:  time.Now().UnixNano(),
	}
	if len(isTrials) > 0 && isTrials[0] {
		bankrupt.OperateType = domain.BillTypeCloseSubsidyTrial
	}
	*assetLogs = append(*assetLogs, bankrupt)

	billAsset := repository.NewBillAssetSync(bankrupt, domain.BillTypeCloseSubsidy, bankrupt.Amount)
	*_billAsset = append(*_billAsset, *billAsset)
}
