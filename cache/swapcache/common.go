package swapcache

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"futures-asset/cache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// TriggerBurstScan 触发爆仓检查
func TriggerBurstScan(_base, _quote string) {
	markPriceChannel := cache.GetContractMarkPriceRedisChannel(_base, _quote)
	_ = cache.RedisCli.Publish(
		markPriceChannel,
		"1",
	)
}

func GetUserCacheData(base, quote, uid string) (*PosCache, *repository.AssetSwap, error) {
	// 更新最新仓位信息
	userCache := NewPosCache(CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  base,
			Quote: quote,
		},
	}, uid)
	assetInfo, err := userCache.Load()
	if err != nil {
		logrus.Errorln(fmt.Sprintf("GetUserCacheData userCache.userCache.Load error: %s", err))
		return nil, nil, err
	}

	return userCache, assetInfo, nil
}

func GetTotalTrialPosAmount(base, quote string) (decimal.Decimal, decimal.Decimal, error) {
	posHashKey := domain.UserTrialPosBase.Key(fmt.Sprintf("%s-%s", base, quote))
	posTotalData, err := cache.RedisCli.HGetAll(posHashKey)
	if err != nil && !errors.Is(err, redis.Nil) {
		return decimal.Zero, decimal.Zero, err
	}
	if errors.Is(err, redis.Nil) {
		return decimal.Zero, decimal.Zero, nil
	}
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero
	for _, posStr := range posTotalData {
		temp := new(repository.UserHoldPos)
		err := json.Unmarshal([]byte(posStr), temp)
		if err != nil {
			logrus.Errorln("GetTotalPosAmount Unmarshal error:", err, posStr)
			continue
		}
		totalLongPos = totalLongPos.Add(temp.LongPos)
		totalShortPos = totalShortPos.Add(temp.ShortPos)
		if temp.BothPos.IsPositive() {
			totalLongPos = totalLongPos.Add(temp.BothPos)
		} else {
			totalShortPos = totalShortPos.Add(temp.BothPos.Abs())
		}
	}

	return totalLongPos, totalShortPos, nil
}

func GetTotalPosAmount(base, quote string) (decimal.Decimal, decimal.Decimal, error) {
	posHashKey := domain.UserPosBase.Key(strings.ToUpper(fmt.Sprintf("%s-%s", base, quote)))
	posTotalData, err := cache.RedisCli.HGetAll(posHashKey)
	if err != nil && err != redis.Nil {
		return decimal.Zero, decimal.Zero, err
	}
	if err == redis.Nil {
		return decimal.Zero, decimal.Zero, nil
	}
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero
	for _, posStr := range posTotalData {
		temp := new(repository.UserHoldPos)
		err := json.Unmarshal([]byte(posStr), temp)
		if err != nil {
			logrus.Errorln("GetTotalPosAmount Unmarshal error:", err, posStr)
			continue
		}
		totalLongPos = totalLongPos.Add(temp.LongPos)
		totalShortPos = totalShortPos.Add(temp.ShortPos)
		if temp.BothPos.IsPositive() {
			totalLongPos = totalLongPos.Add(temp.BothPos)
		} else {
			totalShortPos = totalShortPos.Add(temp.BothPos.Abs())
		}
	}
	return totalLongPos, totalShortPos, nil
}

func SendSwapTask(contractCode string, task cache.SwapBurstTask) {
	jsonBytes, err := json.Marshal(task)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("SendSwapTask Marshal error: %s", err), fmt.Sprintf("%+v", task))
		return
	}
	key := cachekey.GetBurstLogListRedisKey(contractCode)
	err = cache.RedisCli.LPush(key, jsonBytes)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("SendSwapTask LPush error: %s", err), key, string(jsonBytes))
		return
	}
}

type CurrencyTotalBalanceRes struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data *TotalBalance `json:"data"`
}

type TotalBalance struct {
	UID      string          `json:"uid"`
	Currency string          `json:"currency"`
	Balance  decimal.Decimal `bson:"balance"`
}

func GetCurrencyTotalBalance(uid, currency string) (decimal.Decimal, error) {
	// 现货、杠杆账户
	resObj := new(CurrencyTotalBalanceRes)
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["wallet"], "/currency-total-balance")
	params := map[string]interface{}{"uid": uid, "currency": currency}
	paramsBytes, _ := json.Marshal(params)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("getCurrencyTotalBalance Post error:", err))
		return decimal.Zero, err
	}
	resBytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("getCurrencyTotalBalance ReadAll error:", err))
		return decimal.Zero, err
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("getCurrencyTotalBalance Unmarshal error:", err), string(resBytes))
		return decimal.Zero, err
	}
	if resObj.Code != 200 {
		logrus.Error(fmt.Sprintf("getCurrencyTotalBalance resObj.code.resObj:%+v", resObj))
		return decimal.Zero, err
	}
	// 衍生品账户
	userCache := NewPosCache(CacheParam{}, uid)
	asset, err := userCache.Load()
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return decimal.Zero, err
	}

	return resObj.Data.Balance.Add(asset.CBalance(currency)), nil
}
