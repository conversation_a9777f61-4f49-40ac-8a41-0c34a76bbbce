package cachelock

import (
	"encoding/json"
	"errors"
	"fmt"
	"futures-asset/cache"
	"futures-asset/cache/cachekey"
	"futures-asset/internal/domain"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

type BurstLockParams struct {
	ContractCode string
	Liquidation  cache.LiquidationInfo
}

// LockBurstUser 爆仓锁定用户  逐仓需要提供 contract code
func LockBurstUser(lockParams BurstLockParams) error {
	lockKey := lockParams.Liquidation.UID
	err := redislib.Redis().HSet(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode), lockKey, fmt.Sprintf("%d_%s", lockParams.Liquidation.BurstTime, lockParams.Liquidation.BurstId))
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("LockBurstUser %s HSet error: %s", lockParams.Liquidation.UID, err))
		return err
	}
	return nil
}

// GetBurstUserLock 爆仓锁定用户  逐仓需要提供 contract code
func GetBurstUserLock(lockParams BurstLockParams) (string, error) {
	lockKey := lockParams.Liquidation.UID
	info, err := redislib.Redis().HGet(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode), lockKey)
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("LockBurstUser %s HSet error: %s", lockParams.Liquidation.UID, err))
		return "", err
	}
	return info, nil
}

// LockBurstSymbol 爆仓锁定用户  逐仓需要提供 contract code
func LockBurstSymbol(lockParams BurstLockParams) error {
	lockKey := ""
	switch lockParams.Liquidation.MarginMode {
	case domain.MarginModeCross:
		lockKey = lockParams.ContractCode

	case domain.MarginModeIsolated:
		lockKey = fmt.Sprintf("%s_%d", lockParams.ContractCode, lockParams.Liquidation.PosSide)

	default:
		errMsg := fmt.Sprintf("%s LockBurstSymbol %s no marginMode error: %+v", lockParams.ContractCode, lockParams.Liquidation.UID, lockParams)
		logrus.Errorln(errMsg)

		return errors.New(errMsg)
	}

	liquidationData, err := json.Marshal(lockParams.Liquidation)
	if err != nil {
		logrus.Errorln("LockBurstSymbol Marshal error:", err, fmt.Sprintf("%+v", lockParams))
		return err
	}

	lockBurst := cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode)
	if lockParams.Liquidation.IsTrialPos {
		lockBurst = cachekey.GetTrialBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode)
	}
	err = redislib.Redis().HSet(lockBurst, lockKey, string(liquidationData))
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("%s LockBurstSymbol %s HSet error: %s", lockParams.ContractCode, lockParams.Liquidation.UID, err))
		return err
	}

	return nil
}

// GetUserBurstAllSymbolLock 获取用户爆仓所有锁
func GetUserBurstAllSymbolLock(lockParams BurstLockParams) ([]string, error) {
	locks := make([]string, 0)
	if len(lockParams.Liquidation.UID) < 1 {
		return locks, nil
	}
	allLocks := make(map[string]string)
	var err error = nil
	keys := make([]string, 0)
	if lockParams.Liquidation.MarginMode > 0 {
		if len(lockParams.ContractCode) > 0 {
			if lockParams.Liquidation.PosSide > 0 {
				keys = append(keys, fmt.Sprintf("%s_%d", lockParams.ContractCode, lockParams.Liquidation.PosSide))
			} else {
				if lockParams.Liquidation.MarginMode == domain.MarginModeCross {
					keys = append(keys, lockParams.ContractCode)
				} else {
					keys = append(keys, fmt.Sprintf("%s_%d", lockParams.ContractCode, domain.LongPos))
					keys = append(keys, fmt.Sprintf("%s_%d", lockParams.ContractCode, domain.ShortPos))
					keys = append(keys, fmt.Sprintf("%s_%d", lockParams.ContractCode, domain.BothPos))
				}
			}
			allLocks, err = redislib.Redis().HMGet(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode), keys...)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HMGet error: %s  %+v", lockParams.ContractCode, err, lockParams))
			}
		} else {
			allLocks, err = redislib.Redis().HGetAll(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, lockParams.Liquidation.MarginMode))
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HMGet error: %s  %+v", lockParams.ContractCode, err, lockParams))
			}
		}
		for k := range allLocks {
			locks = append(locks, k)
		}
	} else {
		allCrossLocks, err := redislib.Redis().HGetAll(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, domain.MarginModeCross))
		if err != nil && err != redis.Nil {
			logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HGetAll error: %s  %+v", lockParams.ContractCode, err, lockParams))
		}
		for k := range allCrossLocks {
			locks = append(locks, k)
		}
		allIsolatedLocks, err := redislib.Redis().HGetAll(cachekey.GetBurstLockRedisKey(lockParams.Liquidation.UID, domain.MarginModeIsolated))
		if err != nil && err != redis.Nil {
			logrus.Errorln(fmt.Sprintf("%s GetBurstLockRedisKey HGetAll error: %s  %+v", lockParams.ContractCode, err, lockParams))
		}
		for k := range allIsolatedLocks {
			locks = append(locks, k)
		}
	}
	return locks, nil
}

// UnlockBurstUser 爆仓解锁
func UnlockBurstUser(_lockParams BurstLockParams, clear bool) error {
	if len(_lockParams.Liquidation.UID) < 1 {
		return errors.New("UnlockBurstUser UID is empty")
	}
	if _lockParams.Liquidation.MarginMode < 1 {
		return errors.New("UnlockBurstUser MarginMode is empty")
	}
	lockHashKey := cachekey.GetBurstLockRedisKey(_lockParams.Liquidation.UID, _lockParams.Liquidation.MarginMode)
	recoverBurstListKey := cachekey.GetRecoverBurstListRedisKey(_lockParams.ContractCode, _lockParams.Liquidation.MarginMode)
	if _lockParams.Liquidation.IsTrialPos {
		lockHashKey = cachekey.GetTrialBurstLockRedisKey(_lockParams.Liquidation.UID, _lockParams.Liquidation.MarginMode)
	}
	logrus.Infoln(fmt.Sprintf("UnlockBurstUser %v %v %s", _lockParams, clear, lockHashKey))
	if len(_lockParams.ContractCode) > 0 {
		if _lockParams.Liquidation.PosSide > 0 {
			lockKey := fmt.Sprintf("%s_%d", _lockParams.ContractCode, _lockParams.Liquidation.PosSide)
			err := redislib.Redis().HDel(lockHashKey, lockKey)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HGet error: %s", lockKey, err))
				return err
			}
			err = redislib.Redis().HDel(recoverBurstListKey, _lockParams.Liquidation.UID)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser del recover data error: %s", lockKey, err))
				return err
			}
		} else {
			err := redislib.Redis().HDel(lockHashKey, _lockParams.ContractCode)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", _lockParams.ContractCode, err))
				return err
			}
			err = redislib.Redis().HDel(lockHashKey, fmt.Sprintf("%s_%d", _lockParams.ContractCode, domain.LongPos))
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", _lockParams.ContractCode, err))
				return err
			}
			err = redislib.Redis().HDel(lockHashKey, fmt.Sprintf("%s_%d", _lockParams.ContractCode, domain.ShortPos))
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HDel error: %s", _lockParams.ContractCode, err))
				return err
			}
			err = redislib.Redis().HDel(recoverBurstListKey, _lockParams.Liquidation.UID)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser del recover data error: %s", _lockParams.ContractCode, err))
				return err
			}
		}
	} else {
		if clear {
			_, err := redislib.Redis().DelKey(lockHashKey)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser DelKey error: %s", lockHashKey, err))
				return err
			}
		} else {
			lockKey := _lockParams.Liquidation.UID
			err := redislib.Redis().HDel(lockHashKey, lockKey)
			if err != nil && err != redis.Nil {
				logrus.Errorln(fmt.Sprintf("%s UnlockBurstUser HGet error: %s", lockKey, err))
				return err
			}
		}

	}
	return nil
}

// GetBurstLockInfo 获取用户爆仓锁信息
func GetBurstLockInfo(_lockParams BurstLockParams) (string, string, error) {
	lockKey := _lockParams.ContractCode
	if _lockParams.Liquidation.PosSide > 0 {
		lockKey = fmt.Sprintf("%s_%d", _lockParams.ContractCode, _lockParams.Liquidation.PosSide)
	}
	if len(lockKey) < 1 {
		lockKey = _lockParams.Liquidation.UID
	}
	userLockKey := cachekey.GetBurstLockRedisKey(_lockParams.Liquidation.UID, _lockParams.Liquidation.MarginMode)
	// 是否体验金仓位
	if _lockParams.Liquidation.IsTrialPos {
		userLockKey = cachekey.GetTrialBurstLockRedisKey(_lockParams.Liquidation.UID, _lockParams.Liquidation.MarginMode)
	}
	lockInfo, err := redislib.Redis().HGet(userLockKey, lockKey)
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintf("%s GetBurstLockInfo %s HGet error: %s", _lockParams.ContractCode, _lockParams.Liquidation.UID, err))
		return "", "", err
	}
	if len(lockInfo) > 0 {
		lockInfoList := strings.Split(lockInfo, "_")
		if len(lockInfoList) == 2 {
			return lockInfoList[0], lockInfoList[1], nil
		}
	}
	return "", "", nil
}

// UserScanTryLock 用户扫描加锁 （1 Minute）
func UserScanTryLock(_userId string, _marginMode domain.MarginMode, _contractCode string) (bool, error) {
	lockKey := cachekey.GetBurstScanLockRedisKey(_userId, _marginMode, _contractCode)
	return cache.RedisCli.SetNx(lockKey, fmt.Sprintf("%d", time.Now().UnixNano()), time.Minute)
}

// UserScanUnlock 用户扫描解锁
func UserScanUnlock(_userId string, _marginMode domain.MarginMode, _contractCode string) {
	lockKey := cachekey.GetBurstScanLockRedisKey(_userId, _marginMode, _contractCode)
	_, err := cache.RedisCli.DelKey(lockKey)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("%s UserScanUnlock DelKey error: %s", _contractCode, err))
	}
	return
}

// UserTrialScanTryLock 用户体验金仓位扫描加锁 （1 Minute）
func UserTrialScanTryLock(_userId string, _marginMode domain.MarginMode, _contractCode string) (bool, error) {
	lockKey := cachekey.GetTrialBurstScanLockRedisKey(_userId, _marginMode, _contractCode)
	return cache.RedisCli.SetNx(lockKey, fmt.Sprintf("%d", time.Now().UnixNano()), time.Minute)
}

// UserScanUnlock 用户体验金仓位扫描解锁
func UserTrialScanUnlock(_userId string, _marginMode domain.MarginMode, _contractCode string) {
	lockKey := cachekey.GetTrialBurstScanLockRedisKey(_userId, _marginMode, _contractCode)
	_, err := cache.RedisCli.DelKey(lockKey)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("%s UserScanUnlock DelKey error: %s", _contractCode, err))
	}
	return
}
