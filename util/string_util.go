package util

import (
	"fmt"
	"strconv"
	"strings"
)

const SplitFlag = "-"

func BaseQuote(contractCode string) (string, string) {
	array := strings.Split(contractCode, SplitFlag)
	if len(array) >= 2 {
		return strings.ToUpper(array[0]), strings.ToUpper(array[1])
	}
	if len(array) >= 1 {
		return strings.ToUpper(array[0]), ""
	}
	return "", ""
}

func FormatSymbol(base string, quote string) string {
	return strings.ToLower(fmt.Sprintf("%s%s", base, quote))
}

func BaseQuoteByFlag(contractCode, flag string) (string, string) {
	array := strings.Split(contractCode, flag)
	if len(array) >= 2 {
		return strings.ToUpper(array[0]), strings.ToUpper(array[1])
	}
	if len(array) >= 1 {
		return strings.ToUpper(array[0]), ""
	}
	return "", ""
}

func ContractCode(base, quote string) string {
	return strings.ToUpper(base + SplitFlag + quote)
}

// AccountType 获取账户类型
func AccountType(account string) int {
	switch strings.ToLower(account) {
	case "spot":
		return 0
	case "margin":
		return 1
	case "swap":
		return 2
	}
	return 2
}

func AssetId(uid, currency string) string {
	return uid + "_" + strings.ToUpper(currency)
}

func RateId(uid, accountType, code string) string {
	return uid + "_" + strings.ToLower(accountType) + "_" + code
}

func StatisticsId(uid, accountType string) string {
	return uid + "_" + strings.ToLower(accountType)
}

// ProfitLossBassKey 获取利润管理 redis hash 表中的key，使用时还需要拼上userId
func ProfitLossBassKey(_currency string) string {
	return "_" + _currency
}

// GetTotalProfitRedisKey 获取累计盈亏的redis key
func GetTotalProfitRedisKey(_userId, _currency string) string {
	return _userId + "_" + _currency
}

// OptionSubKey 获取期权子Key
func OptionSubKey(base, quote, optionId string) string {
	return fmt.Sprintf("%s%s", ContractCode(base, quote), optionId)
}

func FrozenKey(contractCode string, side int) string {
	return fmt.Sprintf("%s%s%d", contractCode, SplitFlag, side)
}

func TrialFrozenKey(contractCode string, side int) string {
	return "trial" + SplitFlag + FrozenKey(contractCode, side)
}

func FrozenInfo(frozenKey string) (string, string, int) {
	array := strings.Split(frozenKey, SplitFlag)
	if len(array) == 3 {
		side, _ := strconv.Atoi(array[2])
		return strings.ToUpper(array[0]), strings.ToUpper(array[1]), side
	}
	if len(array) == 2 {
		return strings.ToUpper(array[0]), strings.ToUpper(array[1]), 0
	}
	if len(array) == 1 {
		return strings.ToUpper(array[0]), "", 0
	}
	return "", "", 0
}

// 支持体验金模式
func FrozenInfo2(frozenKey string) (isTrial bool, base, quote string, side int) {
	array := strings.Split(frozenKey, SplitFlag)
	if len(array) == 4 {
		isTrial = true
		base, quote = array[1], array[2]
		side, _ = strconv.Atoi(array[3])
	}
	base, quote, side = FrozenInfo(frozenKey)
	return
}
