package match

import (
	"encoding/json"
	"fmt"
	"strings"

	"futures-asset/conf"
	"futures-asset/internal/domain/entity"
	"futures-asset/pkg/httplib"

	"github.com/sirupsen/logrus"
)

type matchData struct{}

func NewMatchData() *matchData {
	return &matchData{}
}

type (
	SwapOrder     struct{}
	SwapOrderPage struct {
		PageNum  int             `json:"pageNum"`
		PageSize int             `json:"pageSize"`
		Total    int64           `json:"total"`
		Orders   []*entity.Order `json:"orders"`
	}
	SwapOrdersReply struct {
		Code int           `json:"code"`
		Msg  string        `json:"msg"`
		Data SwapOrderPage `json:"data"`
	}
	DepthInfo struct {
		Time int        `json:"time"`
		Bids [][]string `json:"bids"`
		Asks [][]string `json:"asks"`
	}
	DepthReply struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data DepthInfo `json:"data"`
	}
)

func (slf *matchData) SwapOrders(uid, contractCode string) SwapOrderPage {
	params := map[string]interface{}{}
	params["uid"] = uid
	if len(contractCode) > 0 {
		params["contractCode"] = strings.ToUpper(contractCode)
	}
	params["accountType"] = AccountTypeSwap
	params["state"] = 101
	params["pageNum"] = 1
	params["pageSize"] = 500

	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractdata"], urlMatchOrders)
	reply, err := httplib.Post(url, params)
	if err != nil {
		logrus.Error("get swap orders err:" + err.Error())
		return SwapOrderPage{}
	}

	orders := SwapOrdersReply{}
	if err := json.Unmarshal(reply, &orders); err != nil {
		logrus.Error("get swap orders unmarshal err:" + err.Error())
		return SwapOrderPage{}
	}

	return orders.Data
}

func (slf *matchData) SwapDepth(contractCode string) DepthInfo {
	params := map[string]interface{}{}
	params["contractCode"] = strings.ToUpper(contractCode)
	params["accountType"] = AccountTypeSwap
	params["step"] = "step0"

	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractdata"], urlContractDepth)
	reply, err := httplib.Post(url, params)
	if err != nil {
		logrus.Error("get swap orders err:" + err.Error())
		return DepthInfo{}
	}

	depth := DepthReply{}
	if err := json.Unmarshal(reply, &depth); err != nil {
		logrus.Error("get swap orders unmarshal err:" + err.Error())
		return DepthInfo{}
	}

	return depth.Data
}
