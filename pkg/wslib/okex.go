package wslib

import (
	"bytes"
	"compress/flate"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/sharedcache"
	"futures-asset/pkg/redislib"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

type okexWs struct {
	ws
	lock             *sync.RWMutex
	supportContracts []string
}

var OkexWs *okexWs

type okexIndexPriceData struct {
	Arg struct {
		Channel string `json:"channel"`
		InstID  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstID  string `json:"instId"`
		IdxPx   string `json:"idxPx"`
		Open24H string `json:"open24h"`
		High24H string `json:"high24h"`
		Low24H  string `json:"low24h"`
		SodUtc0 string `json:"sodUtc0"`
		SodUtc8 string `json:"sodUtc8"`
		Ts      string `json:"ts"`
	} `json:"data"`
}

func InitOkexWs(_host string) {
	OkexWs = new(okexWs)
	OkexWs.newWs(_host)
	return
}

func (ow *okexWs) newWs(_host string) {
	ow.host = _host
	ow.lock = new(sync.RWMutex)
	ow.supportContracts = sharedcache.GetBurstServerContracts()
	u := url.URL{Scheme: "wss", Host: _host, Path: "ws/v5/public"}
	logrus.Infoln("connecting to ", u.String())
	var err error
	ow.conn, _, err = websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		logrus.Errorln("dial ", u.String(), " error: ", err)
	}
	time.Sleep(time.Second)
	go ow.heartbeat()
	go ow.processMessage()
	return
}

func (ow *okexWs) heartbeat() {
	defer func() {
		if err := recover(); err != nil {
			// logrus.Errorln("okexWs heartbeat recover error exit: ", err)
			logrus.Error(0, "okexWs heartbeat recover error exit: ", err)
			logrus.Error(0, string(debug.Stack()))
		}
	}()
	ticker := time.NewTicker(time.Second * 15)
	for {
		select {
		case <-ticker.C:
			ow.conn.WriteMessage(websocket.TextMessage, []byte("ping"))
		}
	}
}

func (ow *okexWs) processMessage() {
	defer func() {
		if err := recover(); err != nil {
			// logrus.Errorln("okexWs processMessage recover error exit: ", err)
			logrus.Error(0, "okexWs processMessage recover error exit: ", err)
			logrus.Error(0, string(debug.Stack()))
			time.Sleep(time.Second * 15)
			ow.newWs(ow.host)
		}
	}()
	for {
		msgType, msgBytes, err := ow.conn.ReadMessage()
		if err != nil {
			logrus.Errorln("okexWs processMessage ReadMessage error: ", err)
			continue
		}
		switch msgType {
		case websocket.TextMessage:
			ow.parseMsg(msgBytes)
		case websocket.BinaryMessage:
			msgBytes, err = ow._msgDecode(msgBytes)
			if err != nil {
				logrus.Errorln("okexWs processMessage msgDecode error: ", err)
				continue
			}

			ow.parseMsg(msgBytes)
		}
	}
}

func (ow *okexWs) _msgDecode(in []byte) ([]byte, error) {
	reader := flate.NewReader(bytes.NewReader(in))
	defer reader.Close()

	return io.ReadAll(reader)
}

func (ow *okexWs) parseMsg(_msgBytes []byte) {
	indexPriceData := okexIndexPriceData{}
	_ = json.Unmarshal(_msgBytes, &indexPriceData)
	switch indexPriceData.Arg.Channel {
	case "index-tickers":
		for _, data := range indexPriceData.Data {
			ow._updateSyncIndexPriceData(data.InstID, data.IdxPx)
		}
		return
	}
	if string(_msgBytes) == "pong" {
		return
	}
	logrus.Infoln(string(_msgBytes))
}

func (ow *okexWs) _updateSyncIndexPriceData(_contractCode string, _indexPrice string) {
	ow.lock.RLock()
	burstContracts := ow.supportContracts
	ow.lock.RUnlock()
	if len(burstContracts) < 1 {
		logrus.Errorln(fmt.Sprintf("okexWs parseMsg syncCoinPair is empty"))
		return
	}
	indexKey := fmt.Sprintf("%sokex:index:%s", cache.Prefix, strings.ToUpper(_contractCode))
	redislib.Redis().SetString(indexKey, _indexPrice)
	redislib.Redis().SetExpire(indexKey, "2m")
	return
}

func (ow *okexWs) Subscribe(_channel map[string]string) {
	subMsgBytes, err := json.Marshal(map[string]interface{}{
		"op":   "subscribe",
		"args": []interface{}{_channel},
	})
	if err != nil {
		logrus.Errorln("okexWs Subscribe Marshal error: ", err)
		return
	}
	ow.conn.WriteMessage(websocket.TextMessage, subMsgBytes)
	return
}
