package wslib

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/sharedcache"
	"futures-asset/pkg/redislib"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

type huobiWs struct {
	ws
	lock             *sync.RWMutex
	supportContracts []string
}

var HuobiWs *huobiWs

type huobiIndexPriceData struct {
	Ch   string `json:"ch"`
	Tick struct {
		Close string `json:"close"`
	}
}

func InitHuobiWs(_host string) {
	HuobiWs = new(huobiWs)
	HuobiWs.newWs(_host)
	return
}

func (hw *huobiWs) newWs(_host string) {
	hw.host = _host
	hw.lock = new(sync.RWMutex)
	hw.supportContracts = sharedcache.GetBurstServerContracts()
	u := url.URL{Scheme: "wss", Host: _host, Path: "/ws_index"}
	logrus.Infoln("connecting to ", u.String())
	var err error
	hw.conn, _, err = websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		logrus.Errorln("dial ", u.String(), " error: ", err)
	}
	time.Sleep(time.Second)
	go hw.processMessage()
	return
}

func (hw *huobiWs) heartbeat(_msgStr string) {
	defer func() {
		if err := recover(); err != nil {
			// logrus.Errorln("huobiWs heartbeat recover error exit: ", err)
			logrus.Error(0, "huobiWs heartbeat recover error exit: ", err)
			logrus.Error(0, string(debug.Stack()))
		}
	}()
	hw.conn.WriteMessage(websocket.TextMessage, []byte(strings.Replace(_msgStr, "ping", "pong", -1)))
	return
}

func _gzipDecompress(input []byte) (string, error) {
	buf := bytes.NewBuffer(input)
	reader, gzipErr := gzip.NewReader(buf)
	if gzipErr != nil {
		return "", gzipErr
	}
	defer reader.Close()

	result, readErr := io.ReadAll(reader)
	if readErr != nil {
		return "", readErr
	}

	return string(result), nil
}

func (hw *huobiWs) processMessage() {
	defer func() {
		if err := recover(); err != nil {
			// logrus.Errorln("huobiWs processMessage recover error exit: ", err)
			logrus.Error(0, "huobiWs processMessage recover error exit: ", err)
			logrus.Error(0, string(debug.Stack()))
			time.Sleep(time.Second * 15)
			hw.newWs(hw.host)
		}
	}()
	for {
		msgType, msgBytes, err := hw.conn.ReadMessage()
		if err != nil {
			logrus.Errorln("huobiWs processMessage ReadMessage error: ", err)
			continue
		}

		msgStr := ""
		if msgType == websocket.BinaryMessage {
			msgStr, err = _gzipDecompress(msgBytes)
			if err != nil {
				logrus.Errorln("huobiWs processMessage gzipDecompress error: ", err)
				continue
			}
		}
		pingMsg := struct {
			Ping int64
		}{}
		json.Unmarshal([]byte(msgStr), &pingMsg)
		if pingMsg.Ping > 0 {
			go hw.heartbeat(msgStr)
			continue
		}

		hw.parseMsg(msgStr)
	}
}

func (hw *huobiWs) Subscribe(_channel string) {
	subMsgBytes, err := json.Marshal(map[string]interface{}{
		"sub": _channel,
	})
	if err != nil {
		logrus.Errorln("huobiWs Subscribe Marshal error: ", err)
		return
	}
	hw.conn.WriteMessage(websocket.TextMessage, subMsgBytes)
	return
}

func (hw *huobiWs) parseMsg(_msgStr string) {
	indexPriceData := huobiIndexPriceData{}
	_ = json.Unmarshal([]byte(_msgStr), &indexPriceData)
	chList := strings.Split(indexPriceData.Ch, ".")
	if len(chList) > 2 {
		if chList[0] == "market" && chList[2] == "index" {
			hw._updateSyncIndexPriceData(chList[1], indexPriceData.Tick.Close)
			return
		}
	}
	fmt.Println(_msgStr)
}

func (hw *huobiWs) _updateSyncIndexPriceData(_contractCode string, _indexPrice string) {
	hw.lock.RLock()
	burstContracts := hw.supportContracts
	hw.lock.RUnlock()
	if len(burstContracts) < 1 {
		logrus.Errorln(fmt.Sprintf("binanceWs parseMsg syncCoinPair is empty"))
		return
	}
	indexKey := fmt.Sprintf("%shuobi:index:%s", cache.Prefix, strings.ToUpper(_contractCode))
	redislib.Redis().SetString(indexKey, _indexPrice)
	redislib.Redis().SetExpire(indexKey, "2m")
	return
}
