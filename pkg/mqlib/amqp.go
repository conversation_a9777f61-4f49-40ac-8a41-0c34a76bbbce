package mqlib

import (
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"futures-asset/conf"
)

const (
	reconnectDelay = 10 * time.Second // 连接断开后多久重连
)

var _connection *amqp.Connection
var CommonAmqp *Amqp
var connMutx sync.Mutex

type Amqp struct {
	conn    *amqp.Connection
	channel *amqp.Channel
}

func Init() (err error) {
	CommonAmqp, err = New()
	if err != nil {
		return err
	}
	go monitor()
	return
}

// newConnection 建立新的连接
func newConnection() (err error) {
	connMutx.Lock()
	defer connMutx.Unlock()

	uri := fmt.Sprintf("amqp://%s:%s@%s:%s/",
		conf.Conf.Mid.RabbitMQ.Contract.User, conf.Conf.Mid.RabbitMQ.Contract.Pwd,
		conf.Conf.Mid.RabbitMQ.Contract.Host, conf.Conf.Mid.RabbitMQ.Contract.Port,
	)
	if _connection, err = amqp.Dial(uri); err != nil {
		return
	}
	return nil
}

func getConnection() (*amqp.Connection, error) {
	for cnt := 0; _connection == nil || _connection.IsClosed(); cnt++ {
		err := newConnection()
		if err != nil {
			if cnt == 10 {
				return nil, errors.New("failed to connect to mq after 6 retries. err: " + err.Error())
			}
			time.Sleep(reconnectDelay)
		}
	}

	return _connection, nil
}

func monitor() {
	for {
		if _connection == nil || _connection.IsClosed() ||
			CommonAmqp.channel == nil || CommonAmqp.channel.IsClosed() {
			newAmqp, err := New()
			if err != nil {
				time.Sleep(reconnectDelay)
				continue
			}

			CommonAmqp.conn = newAmqp.conn
			CommonAmqp.channel = newAmqp.channel
		}

		time.Sleep(reconnectDelay)
	}
}

func New() (*Amqp, error) {
	conn, err := getConnection()
	if err != nil {
		return nil, err
	}
	channel, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	entity := &Amqp{
		conn:    conn,
		channel: channel,
	}
	return entity, nil
}

func (slf *Amqp) Close() error {
	if slf.channel != nil {
		err := slf.channel.Close()
		if err != nil {
			return err
		}
	}

	return nil
}

func (slf *Amqp) Send(exchange, key string, data []byte) error {
	if slf.channel == nil || slf.channel.IsClosed() {
		if slf.conn == nil || slf.conn.IsClosed() {
			slf.conn, _ = getConnection()
		}
		slf.channel, _ = slf.conn.Channel()
	}
	return slf.channel.Publish(exchange, key, false, false, amqp.Publishing{
		ContentType: "text/plain",
		Body:        data,
	})
}

// DeclareExchange 创建交换机
func (slf *Amqp) DeclareExchange(_exchange, _exchangeKind string) error {
	return slf.channel.ExchangeDeclare(
		_exchange,     // name
		_exchangeKind, // kind
		true,          // durable
		false,         // autoDelete
		false,         // internal
		false,         // noWait
		nil,           // args
	)
}

// DeclareQueue 创建队列
func (slf *Amqp) DeclareQueue(queue string) error {
	_, err := slf.channel.QueueDeclare(
		queue, // name
		true,  // durable
		false, // autoDelete
		false, // exclusive
		false, // noWait
		nil,   // args
	)
	return err
}

// BindQueue 绑定队列
func (slf *Amqp) BindQueue(_queueName, _key, _exchange string) error {
	return slf.channel.QueueBind(
		_queueName, // name
		_key,       // key
		_exchange,  // exchange
		false,      // noWait
		nil,        // args
	)
}
