package mqlib

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"futures-asset/conf"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const (
	topicMarkPrice  = "contract/wallet/mark_price/%s/%s"
	topicIndexPrice = "contract/wallet/index_price/%s/%s"
)

var (
	opt    *mqtt.ClientOptions
	client mqtt.Client
)

func InitMQTT(server string) error {
	opt = mqtt.NewClientOptions()
	opt.SetClientID(fmt.Sprintf("contract-%s-%s", server, conf.Conf.Biz.Node))
	opt.AddBroker(fmt.Sprintf("tcp://%s", conf.Conf.Biz.MQTT))
	client = mqtt.NewClient(opt)
	return connect()
}

func connect() error {
	token := client.Connect()
	if token.Wait() && token.Error() != nil {
		log.Println("MQTT connect error", token.Error())
		return token.Error()
	}
	return nil
}

func publish(topic string, data interface{}) error {
	token := client.Publish(topic, byte(0), false, data)
	if token.Wait() && token.Error() != nil {
		log.Println("MQTT publish error", token.Error())
		return token.Error()
	}
	return nil
}

func PublishMark(base, quote string, data interface{}) {
	base = strings.ToLower(base)
	quote = strings.ToLower(quote)
	topic := fmt.Sprintf(topicMarkPrice, base, quote)

	dataBytes, err := json.Marshal(data)
	if err != nil {
		log.Println("PublishMark Marshal error:", err)
	}

	publishBaseQuotePush(topic, dataBytes)
}

func PublishIndex(base, quote string, data interface{}) {
	base = strings.ToLower(base)
	quote = strings.ToLower(quote)
	topic := fmt.Sprintf(topicIndexPrice, base, quote)

	dataBytes, err := json.Marshal(data)
	if err != nil {
		log.Println("PublishIndex Marshal error:", err)
	}

	publishBaseQuotePush(topic, dataBytes)
}

func publishBaseQuotePush(topic string, data interface{}) {
	if client.IsConnected() {
		err := publish(topic, data)
		if err != nil {
			log.Println("publishBaseQuotePush publish", topic, "error:", err)
		}
	} else {
		err := connect()
		if err != nil {
			log.Println("publishBaseQuotePush connect", topic, "error:", err)
		}
	}
}
