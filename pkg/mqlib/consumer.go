package mqlib

import (
	"fmt"
	"log"
	"runtime"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
)

// DeliveryHandler mq message handler
type DeliveryHandler func(d amqp.Delivery) error

// Consumer mq consumer
type Consumer struct {
	conn      *amqp.Connection
	channel   *amqp.Channel
	queueName string
	tag       string
	handler   DeliveryHandler
	autoAck   bool
	exclusive bool
	noLocal   bool
	noWait    bool
}

func NewConsumer(queueName, tag string, handler DeliveryHandler) (*Consumer, error) {
	c := &Consumer{
		conn:      nil,
		channel:   nil,
		tag:       tag,
		queueName: queueName,
		handler:   handler,
	}

	return c, nil
}

func (c *Consumer) SetAutoAck(autoAck bool) *Consumer {
	c.autoAck = autoAck
	return c
}

func (c *Consumer) SetExclusive(exclusive bool) *Consumer {
	c.exclusive = exclusive
	return c
}

func (c *Consumer) SetNoLocal(noLocal bool) *Consumer {
	c.noLocal = noLocal
	return c
}

func (c *Consumer) SetNoWait(noWait bool) *Consumer {
	c.noWait = noWait
	return c
}

func (c *Consumer) Consume() (err error) {
	c.conn, err = getConnection()
	if err != nil {
		return
	}

	c.channel, err = c.conn.Channel()
	if err != nil {
		return fmt.Errorf("channel: %s", err)
	}

	deliveries, err := c.channel.Consume(
		c.queueName, // name
		c.tag,       // consumerTag,
		c.autoAck,   // noAck
		c.exclusive, // exclusive
		c.noLocal,   // noLocal
		c.noWait,    // noWait
		nil,         // arguments
	)
	if err != nil {
		return fmt.Errorf("queue consume: %s", err)
	}

	go func() {
		for {
			d, ok := <-deliveries
			if !ok {
				log.Printf("connection closed")
				log.Printf("%d", runtime.NumGoroutine())
				_ = c.Consume()
				time.Sleep(5 * time.Second)
				return
			}
			if err := c.handler(d); err != nil {
				log.Printf("could not consume message: %v with error: %v", d, err)
			}
		}
	}()
	return nil
}
