package user

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"futures-asset/conf"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

type user struct {
}

var Service *user

type (
	UserLevelData struct {
		BbLevel  int    `json:"bbLevel"`
		DictUser int    `json:"dictUser"`
		UID      string `json:"uid"`
	}
	GetUserLevelRet struct {
		Code int           `json:"code"`
		Msg  string        `json:"msg"`
		Data UserLevelData `json:"data"`
	}
)

func (*user) GetUserLevel(_userId string) (*GetUserLevelRet, error) {
	resObj := &GetUserLevelRet{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["user"], urlUserLevel)
	params := map[string]interface{}{"uid": _userId}
	paramsBytes, _ := json.Marshal(params)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevel Post error:", err))
		return resObj, err
	}
	resBytes, err := io.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevel ReadAll error:", err))
		return resObj, err
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevel Unmarshal error:", err))
		return resObj, err
	}
	if resObj.Code != 200 {
		return resObj, errors.New(fmt.Sprintf("response code error: %d", resObj.Code))
	}
	return resObj, nil
}

type (
	BasicResult struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data UserBasic `json:"data"`
	}

	/*用户详情表*/
	UserBasic struct {
		UID               string `json:"uid"`               // 用户ID
		RealName          string `json:"realName"`          // 用户真名
		SensitiveRealName string `json:"sensitiveRealName"` // 脱敏的用户真名
		IdCard            string `json:"idCard"`            // 身份证号
		SensitiveIdCard   string `json:"sensitiveIdCard"`   // 脱敏的身份证号
		Sex               int    `json:"sex"`               // 1 男 2 女
		PhonePrefix       string `json:"phonePrefix"`       // 手机号前缀 (中国: 86)
		Phone             string `json:"phone"`             // 手机号
		SensitivePhone    string `json:"sensitivePhone"`    // 脱敏的手机号
		MailAddr          string `json:"mail"`              // 邮箱
		SensitiveMailAddr string `json:"sensitiveMailAddr"` // 脱敏的邮箱
	}
)

func (*user) GetUserBasic(_userId string) UserBasic {
	resObj := &BasicResult{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["user"], urlUserBasic)
	params := map[string]interface{}{"uid": _userId}
	paramsBytes, _ := json.Marshal(params)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Post error:", err))
		return resObj.Data
	}
	resBytes, err := io.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic ReadAll error:", err))
		return resObj.Data
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Unmarshal error:", err))
		return resObj.Data
	}
	if resObj.Code != 200 {
		logrus.Error(fmt.Sprintf("resObj.code.resObj:%+v", resObj))
		return resObj.Data
	}
	return resObj.Data
}

type (
	AgreeRes struct {
		Code int          `json:"code"`
		Msg  string       `json:"msg"`
		Data HasAgreeResp `json:"data"`
	}
	HasAgreeResp struct {
		HasAgree int `json:"hasAgree"`
	}
)

func (*user) HasAgree(_userId string) HasAgreeResp {
	resObj := &AgreeRes{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["user"], urlUserHasAgree)
	params := map[string]interface{}{"uid": _userId}
	paramsBytes, _ := json.Marshal(params)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Post error:", err))
		return resObj.Data
	}
	resBytes, err := io.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic ReadAll error:", err))
		return resObj.Data
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Unmarshal error:", err))
		return resObj.Data
	}
	if resObj.Code != 200 {
		logrus.Error(fmt.Sprintf("resObj.code.resObj:%+v", resObj))
		return resObj.Data
	}
	return resObj.Data
}

type (
	ResRobotList struct {
		Code int        `json:"code"`
		Msg  string     `json:"msg"`
		Data []UserData `json:"data"`
	}
	UserData struct {
		UID string `json:"uid"`
	}
)

func (*user) RobotList() []UserData {
	resObj := &ResRobotList{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["user"], urlUserRobotList)
	response, err := http.Post(url, "application/json", bytes.NewBuffer([]byte{}))
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Post error:", err))
		return resObj.Data
	}
	resBytes, err := io.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic ReadAll error:", err))
		return resObj.Data
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserBasic Unmarshal error:", err))
		return resObj.Data
	}
	if resObj.Code != 200 {
		logrus.Error(fmt.Sprintf("resObj.code.resObj:%+v", resObj))
		return resObj.Data
	}
	return resObj.Data
}

type (
	LevelRateResult struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data LevelRate `json:"data"`
	}

	/*用户等级及费率*/
	LevelRate struct {
		UID              string          `json:"uid"`
		VIP              int             `json:"vip"`      // 现货等级
		LevelS           int             `json:"levelS"`   // 期货等级
		UserType         int             `json:"userType"` // 用户类型
		UserTypeStatus   int             `json:"userTypeStatus"`
		BbMaker          decimal.Decimal `json:"bbMaker"`          // 币币maker费率
		BbTaker          decimal.Decimal `json:"bbTaker"`          // 币币taker费率
		ContractMaker    decimal.Decimal `json:"contractMaker"`    // 合约maker费率
		ContractTaker    decimal.Decimal `json:"contractTaker"`    // 合约taker费率
		IsRobotFee       int             `json:"isRobotFee"`       // 是否收手续费(机器人)-1:是,2:否'
		ChannelCode      string          `json:"channelCode"`      // 渠道码
		AgentChannelCode string          `json:"agentChannelCode"` // 代理人渠道码
		AgentUserId      string          `json:"agentUserId"`
		RegisterLanguage string          `json:"registerLanguage"`
		OptionLevel      int             `json:"optionLevel"`
		OptionTaker      decimal.Decimal `json:"optionTaker"`
		LastRequestTime  int64           // 最近一次请求时间戳
	}
)

func (*user) GetUserLevelRate(_userId string) (LevelRate, error) {
	resObj := new(LevelRateResult)
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["user"], urlUserLevelRate)
	params := map[string]interface{}{"uid": _userId}
	paramsBytes, _ := json.Marshal(params)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevelRate Post error:", err))
		return resObj.Data, err
	}
	resBytes, err := io.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevelRate ReadAll error:", err))
		return resObj.Data, err
	}
	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("GetUserLevelRate Unmarshal error:", err), string(resBytes))
		return resObj.Data, err
	}
	if resObj.Code != 200 {
		logrus.Error(fmt.Sprintf("GetUserLevelRate resObj.code.resObj:%+v", resObj))
		return resObj.Data, err
	}
	return resObj.Data, nil
}
