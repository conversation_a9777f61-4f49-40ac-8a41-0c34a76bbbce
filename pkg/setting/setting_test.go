package setting

import (
	"fmt"
	"testing"
)

func TestGetMarginRate(t *testing.T) {
	url := fmt.Sprintf("http://%s%s", "10.32.1.2:1103", urlMarginRate)
	fmt.Println(url)
}

func TestGetCoinPair(t *testing.T) {
	url := fmt.Sprintf("http://%s%s", "10.32.1.2:1103", urlCoinPair)
	fmt.Println(url)
}

func Test_setting_GetCoinWhiteListConfigMap(t *testing.T) {
	s := setting{}
	configMap, err := s.GetCoinWhiteListConfigMap()
	t.Log(configMap, err)
}
