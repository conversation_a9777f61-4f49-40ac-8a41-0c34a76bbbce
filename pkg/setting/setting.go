package setting

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strings"

	"futures-asset/cache/pair"
	"futures-asset/conf"
	"futures-asset/internal/domain"
	"futures-asset/pkg/httplib"
	"futures-asset/pkg/redislib"
	"futures-asset/util"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// 获取合约配置的redis key
func getContractSettingRedisKey() string {
	return domain.ContractSetting
}

// 获取合约配置等级的redis key
func getContractSettingLevelRedisKey() string {
	return fmt.Sprintf("%s:level", domain.ContractSetting)
}

// GetProfitDateKey 获取盈亏记录 redis中的数据的key
func GetProfitDateKey() string {
	return domain.ProfitLossDate
}

// GetProfitDataKeyByTime 获取盈亏记录 redis key
func GetProfitDataKeyByTime(_timeStr string) string {
	return domain.ProfitLossData + _timeStr
}

func GetOptionProfitDateKey() string {
	return domain.OptionProfitLossDate
}

func GetOptionProfitDataKeyByTime(_timeStr string) string {
	return domain.OptionProfitLossData + _timeStr
}

func getProfitKey() string {
	return domain.ProfitKey
}

type setting struct {
}

var Service *setting

type LevelFilter struct {
	Level             int
	Lever             int
	HighLimit         decimal.Decimal
	LowLimit          decimal.Decimal
	InitMarginRate    decimal.Decimal
	WarnMarginRate    decimal.Decimal
	HoldingMarginRate decimal.Decimal
}

// GetMarginRateLevel 从redis获取维持保证金率等级
func (s *setting) GetMarginRateLevel(base, quote string) (LevelHoldSortList, error) {
	quoteList := strings.Split(quote, "_")
	if len(quoteList) < 1 {
		return nil, errors.New("GetMarginRateLevel Split quote error")
	}
	quote = quoteList[0]
	var levelFilters LevelHoldSortList
	contractCode := strings.ToUpper(fmt.Sprintf("%s-%s", base, quote))
	settingListStr, err := redislib.Redis().HGet(getContractSettingLevelRedisKey(), contractCode)
	if err != nil && err != redis.Nil {
		logrus.Errorln(fmt.Sprintln(contractCode, "GetMarginRateLevel HGet contractSettingLevelRedisKey", getContractSettingLevelRedisKey()))
		logrus.Errorln(fmt.Sprintln(contractCode, "GetMarginRateLevel HGet redis error:", err))
		return levelFilters, err
	}
	if len(settingListStr) < 1 {
		logrus.Errorln(contractCode, "no margin rate level data")
		return levelFilters, errors.New("no margin rate level data")
	}

	err = json.Unmarshal([]byte(settingListStr), &levelFilters)
	if err != nil {
		return levelFilters, err
	}
	return levelFilters, nil
}

// GetPairSettingInfo 从redis获取币对配置
func (s *setting) GetPairSettingInfo(_base string, _quote string) (*ContractPair, error) {
	coinPairSetting := new(ContractPair)
	jsonStr, err := redislib.Redis().HGet(getContractSettingRedisKey(), strings.ToUpper(fmt.Sprintf("%s-%s", _base, _quote)))
	if err != nil {
		if err != redis.Nil {
			logrus.Error("GetPairSettingInfo redis HGet error:", err)
			return nil, err
		}
	} else {
		err = json.Unmarshal([]byte(jsonStr), &coinPairSetting)
		if err != nil {
			logrus.Error(fmt.Sprintln("GetPairSettingInfo Unmarshal error:", err, jsonStr))
			return nil, err
		}
	}
	return coinPairSetting, nil
}

type CurrencyWhiteListConfig struct {
	Currency       string               `json:"currency"`
	CurrencyDetail map[string]WhiteList `json:"currencyDetail"`
}
type WhiteList struct {
	UserWhiteState bool     `json:"userWhiteState"`
	UserWhiteList  []string `json:"userWhiteList"`
}

func (s *setting) GetCoinWhiteListConfigMap() (map[string]CurrencyWhiteListConfig, error) {
	cacheHash, err := redislib.Redis().HGetAll("Wallet_Coin")
	if err != nil {
		logrus.Error(fmt.Sprintln("GetCoinWhiteListConfigMap from redis error:", err))
		return nil, err
	}
	configMap := make(map[string]CurrencyWhiteListConfig)
	for currency, currencyConfig := range cacheHash {
		r := CurrencyWhiteListConfig{}
		err := json.Unmarshal([]byte(currencyConfig), &r)
		if err != nil {
			logrus.Error(fmt.Sprintln("GetCoinWhiteListConfigMap Unmarshal error:", err))
			continue
		}
		configMap[strings.ToUpper(currency)] = r
	}
	return configMap, nil
}

// GetCachePair 从内存缓存获取币对配置
func (s *setting) GetCachePair(base string, quote string) (*ContractPair, error) {
	pairInfo := new(ContractPair)
	b := pair.GetPair(util.ContractCode(base, quote))
	if b != nil {
		err := json.Unmarshal(b, &pairInfo)
		if err != nil {
			return pairInfo, err
		}
		return pairInfo, nil
	}
	return s.GetPairSettingInfo(base, quote)
}

// UpdateAllPairInfo 更新所有setting和level配置，保存到redis
func (*setting) UpdateAllPairInfo() error {
	resObj := new(CoinPairsReply)
	params := map[string]interface{}{
		"viewType": 1,
	}
	paramsBytes, _ := json.Marshal(params)
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["setting"], urlCoinPair)
	response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	if err != nil {
		logrus.Error(fmt.Sprintln("UpdatePairInfo Post error:", err))
		return err
	}
	resBytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		logrus.Error(fmt.Sprintln("UpdatePairInfo ReadAll error:", err))
		return err
	}

	err = json.Unmarshal(resBytes, resObj)
	if err != nil {
		logrus.Error(fmt.Sprintln("UpdatePairInfo Unmarshal error:", err, string(resBytes)))
		return err
	}
	if resObj.Code != 200 {
		return fmt.Errorf("response code error: %d", resObj.Code)
	}
	if len(resObj.Data) > 0 {
		for _, pairInfo := range resObj.Data {
			// 保存setting数据
			contractCode := strings.ToUpper(fmt.Sprintf("%s-%s", pairInfo.Base, pairInfo.Quote))
			pairInfoBytes, _ := json.Marshal(pairInfo)
			err := redislib.Redis().HSet(getContractSettingRedisKey(), contractCode, string(pairInfoBytes))
			if err != nil {
				logrus.Error("UpdatePairInfo redis HSet error:", err, string(pairInfoBytes))
				continue
			}
			// 保存level数据
			var levelList LevelHoldSortList
			for _, marginRateInfo := range pairInfo.MarginRateGear {
				levelList = append(levelList, LevelFilter{
					Level:             marginRateInfo.Gear,
					Lever:             marginRateInfo.LeverMultiple,
					HighLimit:         marginRateInfo.End.Truncate(domain.CurrencyPrecision),
					LowLimit:          marginRateInfo.Start.Truncate(domain.CurrencyPrecision),
					InitMarginRate:    marginRateInfo.InitRate.Truncate(domain.RatePrecision),
					WarnMarginRate:    marginRateInfo.WarnRate.Truncate(domain.RatePrecision),
					HoldingMarginRate: marginRateInfo.MaintenanceRate.Truncate(domain.RatePrecision),
				})
			}

			if len(levelList) > 0 {
				sort.Sort(sort.Reverse(levelList))
				levelBytes, _ := json.Marshal(levelList)
				err := redislib.Redis().HSet(getContractSettingLevelRedisKey(), contractCode, string(levelBytes))
				if err != nil {
					logrus.Error("UpdateAllPairInfo redis error:", err)
					continue
				}
			}
		}
	}
	return nil
}

// GetAllPairSettingInfo 从redis获取所有币对配置
func (s *setting) GetAllPairSettingInfo() (map[string]ContractPair, error) {
	coinPairSettings := make(map[string]ContractPair)
	coinPairSettingMap, err := redislib.Redis().HGetAll(getContractSettingRedisKey())
	if err != nil {
		if err != redis.Nil {
			logrus.Error("GetAllPairSettingInfo redis HGet error:", err)
			return coinPairSettings, err
		}
	}
	if len(coinPairSettingMap) > 0 {
		for coinPairName, coinPairSettingStr := range coinPairSettingMap {
			coinPairSetting := new(ContractPair)
			err := json.Unmarshal([]byte(coinPairSettingStr), &coinPairSetting)
			if err != nil {
				logrus.Errorln(fmt.Sprintf("setting GetAllPairInfo error: %s", err.Error()))
				continue
			}
			coinPairSettings[coinPairName] = *coinPairSetting
		}
	}
	return coinPairSettings, err
}

func (s *setting) AllContractPair() (AllContractPairReply, error) {
	pairs := AllContractPairReply{}
	params := map[string]interface{}{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["setting"], urlAllContractPair)
	reply, err := httplib.Post(url, params)
	if err != nil {
		return pairs, err
	}
	if err := json.Unmarshal(reply, &pairs); err != nil {
		return pairs, err
	}

	return pairs, nil
}

func (s *setting) GetUserLevelsRate() (UserLevelsData, error) {
	userLevelsReply := UserLevelsReply{}
	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["setting"], urlLevelConfig)
	params := map[string]interface{}{}
	reply, err := httplib.Post(url, params)
	if err != nil {
		return userLevelsReply.Data, err
	}
	if err := json.Unmarshal(reply, &userLevelsReply); err != nil {
		return userLevelsReply.Data, err
	}
	return userLevelsReply.Data, nil
}

// FetchMarginLevel 获取保证金等級
//
// returns:
//
// 0: LevelFilter 当前等级配置
//
// 1: bool 是否是最后一级
//
// 3: error 错误信息
func FetchMarginLevel(_base, _quote string, _posTotalValue decimal.Decimal) (LevelFilter, bool, error) {
	retryTimes := 0
	lowLimitSortList := LevelLowSortList{}
Retry:
	tempHoldSortList, err := Service.GetMarginRateLevel(_base, _quote)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("%s-%s", _base, _quote), "FetchMarginLevel GetMarginRateLevel error:", err)

		err = Service.UpdateAllPairInfo()
		if err != nil {
			return LevelFilter{}, false, errors.New(fmt.Sprintln(fmt.Sprintf("%s-%s", _base, _quote), "FetchMarginLevel UpdateAllPairInfo error:", err))
		}
		retryTimes += 1
		if retryTimes > 3 {
			return LevelFilter{}, false, errors.New(fmt.Sprintln("FetchMarginLevel retry times >= 3"))
		}
		goto Retry
	}

	lowLimitSortList = LevelLowSortList(tempHoldSortList)

	sort.Sort(sort.Reverse(lowLimitSortList))

	tempLevel := LevelFilter{}
	tempIsLast := false
	for k, lData := range lowLimitSortList {
		// 如果是最后一个直接跳出
		if k == lowLimitSortList.Len()-1 {
			tempLevel, tempIsLast = lData, true
			break
		}
		// 在等级范围内的就是当前等级
		if _posTotalValue.GreaterThanOrEqual(lData.LowLimit) && (lData.HighLimit.Equal(decimal.NewFromInt(-1)) || _posTotalValue.LessThanOrEqual(lData.HighLimit)) {
			tempLevel = lData
			break
		}
	}

	if tempLevel.Lever == 0 {
		logrus.Errorln("HoldingMarginRate leverage is zero")
		return LevelFilter{}, false, errors.New("HoldingMarginRate leverage is zero")
	}

	return tempLevel, tempIsLast, nil
}

// NextMarginLevel 获取下一个级维持保证金率信息
//
//	Params:
//	  base int: 交易币
//	  quote int: 计价币
//	  level int: 当前仓位等级
//
//	Return:
//	  0 LevelFilter: 仓位等级信息
//	  1 error: 错误信息
func NextMarginLevel(base, quote string, level int) (LevelFilter, error) {
	tempLevel := LevelFilter{}
	contractSettings, err := Service.GetMarginRateLevel(base, quote)
	if err != nil {
		logrus.Errorln("NextMarginLevel GetMarginRateLevel error:", err)
		return tempLevel, err
	}
	tempLowSortList := LevelLowSortList(contractSettings)
	sort.Sort(sort.Reverse(tempLowSortList))

	nextBreak := false

	for _, filterInfo := range tempLowSortList {
		if level == filterInfo.Level {
			nextBreak = true
			continue
		}
		if nextBreak {
			tempLevel = filterInfo
			break
		}
	}

	if !nextBreak {
		return tempLevel, errors.New(fmt.Sprintln(util.ContractCode(base, quote), "no match level", level, "next"))
	}

	return tempLevel, nil
}

type Profit struct {
	InitialFund decimal.Decimal
	CurrentFund decimal.Decimal
	ProfitRate  float64
}

func GetProfitFromRedis(uid string) (*Profit, error) {
	data, err := redislib.Redis().HGet(getProfitKey(), uid)
	if err != nil {
		return nil, err
	}
	profit := new(Profit)
	err = json.Unmarshal([]byte(data), profit)
	if err != nil {
		return nil, err
	}
	return profit, nil
}

func (slf *Profit) Flush(uid string) error {
	data, err := json.Marshal(slf)
	if err != nil {
		return err
	}
	err = redislib.Redis().HSet(getProfitKey(), uid, string(data))
	if err != nil {
		return err
	}
	return nil
}

func (slf *Profit) Calculate() error {
	if slf.InitialFund.IsZero() {
		return fmt.Errorf("InitialFund is zero")
	}
	profit := slf.CurrentFund.Sub(slf.InitialFund)
	if profit.IsNegative() {
		slf.ProfitRate = 0
		logrus.Infof("no profit, current fund: %s, initial fund: %s", slf.CurrentFund.String(), slf.InitialFund.String())
		return nil
	}
	profitRate := profit.Div(slf.InitialFund)
	slf.ProfitRate = profitRate.InexactFloat64()
	return nil
}
