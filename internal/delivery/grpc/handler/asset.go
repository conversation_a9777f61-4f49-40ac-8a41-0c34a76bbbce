package handler

import (
	"context"
	"errors"

	"futures-asset/internal/delivery/grpc/response"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/usecase"
	"futures-asset/service/isolation"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	"yt.com/backend/common.git/transport/grpcinit"
)

type AssetHandler struct {
	assetUseCase usecase.AssetUseCase
	burstRepo    repository.BurstRepository

	futuresassetpb.FuturesAssetServiceServer
}

type NewAssetHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
	BurstRepo    repository.BurstRepository
}

func NewAssetHandler(param NewAssetHandlerParam) *AssetHandler {
	return &AssetHandler{
		assetUseCase: param.AssetUseCase,
		burstRepo:    param.BurstRepo,
	}
}

func (h *AssetHandler) LockAsset(ctx context.Context, req *futuresassetpb.LockAssetRequest) (*futuresassetpb.LockAssetResponse, error) {
	amount, err := decimal.NewFromString(req.GetAmount())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	param := &payload.LockParam{
		UID:        req.Uid,
		UserType:   req.UserType,
		Currency:   req.Currency,
		Amount:     amount,
		OrderID:    req.OrderId,
		OrderTime:  req.OrderTime,
		MarginMode: req.MarginMode,
		AwardOpIds: req.AwardOpIds,
		TrialIsEnd: req.TrialIsEnd,
	}
	if len(param.OrderID) <= 0 || param.Amount.Sign() <= 0 || param.MarginMode <= 0 {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("param err")))
	}
	if param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_CROSS && param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("margin mode err")))
	}

	// 如果用户在爆仓中, 不能进行挂单
	isBursting, _ := h.burstRepo.IsBursting(ctx, repository.CheckBurstParam{
		UID:        param.UID,
		MarginMode: param.MarginMode,
		IsTrialPos: len(param.AwardOpIds) > 0,
	})
	if isBursting && param.IsInnerCall != 1 {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("user bursting")))
	}

	operator, err := isolation.GetOperater(param)
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("get user asset error")))
	}
	if operator == nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("param side or offset err")))
	}

	reply, err := operator.Lock()
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.LockAssetResponse{
		Amount:         reply.Amount.String(),
		FrozenMargin:   reply.FrozenMargin.String(),
		HaveTrial:      int32(reply.HaveTrial),
		PositionMode:   reply.PositionMode,
		JoinMarginRate: reply.JoinMarginRate.String(),
		OrderId:        reply.OrderId,
	}, nil
}

func (h *AssetHandler) UnLockAsset(ctx context.Context, req *futuresassetpb.UnLockAssetRequest) (*futuresassetpb.UnLockAssetResponse, error) {
	if len(req.Uid) == 0 ||
		len(req.OrderId) <= 0 {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("param err")))
	}

	amount, err := decimal.NewFromString(req.GetAmount())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	operater, err := isolation.GetOperater(param)
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("get user asset error")))
	}
	if operater == nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(errors.New("param side or offset err")))
	}

	_, err = operater.UnLock()
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.UnLockAssetResponse{
		OrderId: req.OrderId,
	}, nil
}
