package delivery

import (
	"context"
	"fmt"

	"futures-asset/configs"
	"futures-asset/internal/delivery/event"
	"futures-asset/internal/delivery/grpc"
	"futures-asset/internal/delivery/http"
	"futures-asset/internal/libs/container"
	"futures-asset/internal/libs/elk"
	"futures-asset/internal/libs/kafka"
	"futures-asset/internal/libs/mongo"
	"futures-asset/internal/libs/mysql"
	"futures-asset/internal/libs/otel"
	"futures-asset/internal/libs/redis"

	"github.com/go-redsync/redsync/v4"
	"github.com/olivere/elastic/v7"
	goredis "github.com/redis/go-redis/v9"
	gomongodb "go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"

	cfg "yt.com/backend/common.git/config"

	"yt.com/backend/common.git/shutdown"
	"yt.com/backend/common.git/transport/http/health"
)

type API struct {
	conf            *cfg.Config[configs.Config]
	shutdownHandler *shutdown.Shutdown
	httpServer      *http.Server
	//nolint:unused
	grpcServer    *grpc.Server
	messageServer *event.Server
	container     *dig.Container
}

func NewAPI(conf *cfg.Config[configs.Config], shutdownHandler *shutdown.Shutdown) *API {
	return &API{
		conf:            conf,
		shutdownHandler: shutdownHandler,
	}
}

func (a *API) Start(ctx context.Context) error {
	if a.conf.Observability.Otel.Enable {
		otelShutdown, err := otel.New(ctx, a.conf)
		if err != nil {
			return fmt.Errorf("initialize otel error: %w", err)
		}

		a.shutdownHandler.Add("otel tracing", otelShutdown)
	}

	healths, kdb, err := a.registerContainer(ctx)
	if err != nil {
		return fmt.Errorf("registerContainer fail : %w", err)
	}

	if err := a.registerUseCaseAndRepo(); err != nil {
		return fmt.Errorf("registerUseCaseAndRepo fail : %w", err)
	}

	if err := a.registerHTTPServ(ctx, healths); err != nil {
		return fmt.Errorf("register HTTP server error: %w", err)
	}

	if err := a.registerEventServ(ctx, kdb); err != nil {
		return fmt.Errorf("register message server error: %w", err)
	}

	return nil
}

func (a *API) registerContainer(ctx context.Context) ([]health.CheckConfig, *kafka.Cluster, error) {
	a.container = container.New()

	if err := a.container.Provide(func() *cfg.Config[configs.Config] {
		return a.conf
	}, dig.Name("config")); err != nil {
		return nil, nil, fmt.Errorf("new gorm failed : %w", err)
	}

	db, err := mysql.New(a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new mysql failed : %w", err)
	}

	mdb, err := mongo.New(a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new mongodb failed : %w", err)
	}

	rdb, err := redis.New(ctx, a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new redis failed : %w", err)
	}

	rs, err := redis.NewMutex(ctx, a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new redis failed : %w", err)
	}

	kdb, err := kafka.NewCluster(a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new kafka cluster failed : %w", err)
	}

	esdb, err := elk.New(ctx, a.conf)
	if err != nil {
		return nil, nil, fmt.Errorf("new elk failed : %w", err)
	}

	if err := a.container.Provide(func() *kafka.Cluster {
		return kdb
	}, dig.Name("kafka")); err != nil {
		return nil, nil, fmt.Errorf("new kafka cluster failed : %w", err)
	}

	if err := a.container.Provide(func() *gorm.DB {
		return db
	}, dig.Name("gorm")); err != nil {
		return nil, nil, fmt.Errorf("new gorm failed : %w", err)
	}

	if err := a.container.Provide(func() *gomongodb.Database {
		return mdb
	}, dig.Name("mongodb")); err != nil {
		return nil, nil, fmt.Errorf("new mongodb failed : %w", err)
	}

	if err := a.container.Provide(func() *goredis.ClusterClient {
		return rdb
	}, dig.Name("redis-cluster")); err != nil {
		return nil, nil, fmt.Errorf("new redis cluster failed : %w", err)
	}

	if err := a.container.Provide(func() *redsync.Redsync {
		return rs
	}, dig.Name("rs")); err != nil {
		return nil, nil, fmt.Errorf("new redsync mutx failed : %w", err)
	}

	if err := a.container.Provide(func() *elastic.Client {
		return esdb
	}, dig.Name("elk")); err != nil {
		return nil, nil, fmt.Errorf("new elk failed : %w", err)
	}

	healths := []health.CheckConfig{
		{
			Name: "mongodb",
			CheckFn: func(ctx context.Context) error {
				if err := mdb.Client().Ping(ctx, nil); err != nil {
					return fmt.Errorf("ping mongo error: %w", err)
				}

				return nil
			},
		},
		{
			Name: "redis",
			CheckFn: func(ctx context.Context) error {
				return rdb.Ping(ctx).Err()
			},
		},
	}

	return healths, kdb, nil
}

func (a *API) registerHTTPServ(ctx context.Context, healths []health.CheckConfig) error {
	a.httpServer = http.NewServer(a.conf, a.container)

	if err := a.httpServer.Start(ctx, healths); err != nil {
		return fmt.Errorf("http server start error: %w", err)
	}

	a.shutdownHandler.Add("http server", a.httpServer.Shutdown)

	return nil
}

//nolint:unused
func (a *API) registerGRPCServ(ctx context.Context) error {
	a.grpcServer = grpc.NewServer(a.conf, a.container)

	if err := a.grpcServer.Start(ctx); err != nil {
		return fmt.Errorf("grpc server start error: %w", err)
	}

	a.shutdownHandler.Add("grpc server", a.grpcServer.Shutdown)

	return nil
}

func (a *API) registerEventServ(ctx context.Context, kdb *kafka.Cluster) error {
	a.messageServer = event.NewServer(a.conf, a.container, kdb)

	if err := a.messageServer.Start(ctx); err != nil {
		return fmt.Errorf("message server start error: %w", err)
	}

	a.shutdownHandler.Add("message server", a.messageServer.Shutdown)

	return nil
}
