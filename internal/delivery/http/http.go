package http

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"futures-asset/configs"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"go.uber.org/dig"

	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/transport/http/health"
)

type Server struct {
	container  *dig.Container
	conf       *cfg.Config[configs.Config]
	httpServer *http.Server
	httpRouter *gin.Engine
}

func NewServer(conf *cfg.Config[configs.Config], container *dig.Container) *Server {
	return &Server{
		conf:      conf,
		container: container,
	}
}

func (s *Server) Start(ctx context.Context, healthChecks []health.CheckConfig) error {
	if err := s.registerHTTPRouter(ctx, healthChecks); err != nil {
		return fmt.Errorf("server.registerHTTPRouter error: %w", err)
	}

	s.httpServer = &http.Server{
		Addr:              fmt.Sprintf(":%d", s.conf.HTTP.Port),
		ReadTimeout:       s.conf.HTTP.Timeouts.ReadTimeout,
		ReadHeaderTimeout: s.conf.HTTP.Timeouts.ReadHeaderTimeout,
		WriteTimeout:      s.conf.HTTP.Timeouts.WriteTimeout,
		IdleTimeout:       s.conf.HTTP.Timeouts.IdleTimeout,
		Handler:           s.httpRouter,
	}

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.WithField("err", err).Error("http server failed to listen and serve")
		}
	}()

	return nil
}

func (s *Server) Shutdown(ctx context.Context) error {
	if err := s.httpServer.Shutdown(ctx); err != nil {
		return fmt.Errorf("http server shutdown with err: %w", err)
	}

	return nil
}
