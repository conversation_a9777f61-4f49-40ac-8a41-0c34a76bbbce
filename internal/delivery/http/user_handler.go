package http

import (
	"errors"
	"fmt"
	"net/http"

	"futures-asset/cache"
	"futures-asset/cache/cachelock"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/usecase"
	"futures-asset/pkg/match"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type UserHandlerParam struct {
	dig.In

	UserUseCase usecase.UserUseCase
	PriceRepo   repository.PriceRepository
	AssetRepo   repository.UserRepository
}

type userHandler struct {
	userUseCase usecase.UserUseCase
	priceRepo   repository.PriceRepository
	assetRepo   repository.UserRepository
}

func newUserHandler(param UserHandlerParam) *userHandler {
	return &userHandler{
		userUseCase: param.UserUseCase,
		priceRepo:   param.PriceRepo,
		assetRepo:   param.AssetRepo,
	}
}

// InitSwap 合约钱包初始化
func (handler *userHandler) InitSwap(c *gin.Context) {
	var req payload.SwapInit
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	code := handler.userUseCase.SwapInit(c.Request.Context(), req.ToUsecase())
	if code != http.StatusOK {
		logrus.Error(fmt.Sprintf("InitSpotPrice Swap err.code:%+v, param:%+v", code, req))
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(req))
}

// UserLeverage 获取用户杠杆倍数
func (handler *userHandler) UserLeverage(c *gin.Context) {
	var req payload.CommonParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	leverage, err := handler.userUseCase.UserLeverage(c.Request.Context(), req.ToUsecase())
	if err != nil {
		logrus.Info(0, fmt.Sprintf("UserLeverage %+v", leverage))
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251114, errors.New(err.Error())))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(leverage))
}

// LeverageMarginAdjust 调整杠杠倍数和仓位模式
func (handler *userHandler) LeverageMarginAdjust(c *gin.Context) {
	var req payload.LeverageMarginAdAdjust
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 || len(req.ContractCode) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("userid or base, quote err: %+v", req))))
		return
	}
	if req.MarginMode != domain.MarginModeIsolated && req.MarginMode != domain.MarginModeCross {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", req))))
		return
	}

	if req.Leverage <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("leverage less than 0")))
		return
	}

	// 爆仓中不能调整杠杆倍数 和仓位模式
	checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: req.UID}}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	// 不能有挂单(包含止赢止损单)
	if match.NewMatchData().SwapOrders(req.UID, req.ContractCode).Total > 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251020, errors.New(domain.ErrOrderOrPos.Error())))
		return
	}

	pair, err := setting.Service.GetPairSettingInfo(util.BaseQuote(req.ContractCode))
	if err != nil || pair == nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250002, err))
		return
	}
	// 验证杠杆倍数取值范围
	if req.Leverage > pair.MaxLeverage {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251007, errors.New(fmt.Sprintf("leverage %+v error, max:%+v", req.Leverage, pair.MaxLeverage))))
		return
	}

	code, err := handler.userUseCase.AdjustLeverageMargin(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(req))
}

// LeverageAdjust 调整杠杠倍数
func (handler *userHandler) LeverageAdjust(c *gin.Context) {
	var req payload.LeverageAdjust
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	// 爆仓中不能调整杠杆倍数
	checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: req.UID}}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}
	if len(req.UID) <= 0 || len(req.ContractCode) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("userid or base, quote err: %+v", req))))
		return
	}
	// 不能有挂单(包含止赢止损单)
	orders := match.NewMatchData().SwapOrders(req.UID, req.ContractCode).Orders
	logrus.Infof("SwapOrders userid:%s,len:%v", req.UID, len(orders))
	// ctx.Error(domain.Code251020, domain.ErrOrderOrPos.Error())
	for i := range orders {
		flag := checkAdjustLeverage(orders[i], req.ToUsecase())
		if !flag {
			logrus.Errorf("update both pos orders[i]:%+v,req:%v", orders[i], req)
			c.JSON(http.StatusBadRequest, response.NewError(domain.Code251001, errors.New(domain.ErrOrderOrPos.Error())))
			return
		}
	}

	if req.Leverage < 0 || req.LLeverage < 0 || req.SLeverage < 0 || req.BLeverage < 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("leverage less than 0")))
		return
	}
	pair, err := setting.Service.GetCachePair(util.BaseQuote(req.ContractCode))
	if err != nil || pair == nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250002, err))
		return
	}
	// 验证杠杆倍数取值范围
	if req.Leverage > pair.MaxLeverage || req.LLeverage > pair.MaxLeverage || req.SLeverage > pair.MaxLeverage || req.BLeverage > pair.MaxLeverage {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251007, errors.New(fmt.Sprintf("leverage %+v error, max:%+v", req.Leverage, pair.MaxLeverage))))

		return
	}

	code, err := handler.userUseCase.AdjustLeverage(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(req))
}

// MarginAdjust 调整仓位模式
func (handler *userHandler) MarginAdjust(c *gin.Context) {
	var req payload.MarginAdjust
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 || len(req.ContractCode) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("userid or base, quote err: %+v", req))))
		return
	}
	if req.MarginMode != domain.MarginModeIsolated && req.MarginMode != domain.MarginModeCross {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", req))))
		return
	}

	// 混合保证金不允许切换成逐仓
	asset, err := handler.assetRepo.LoadJoinMargin(c.Request.Context(), &repository.CommonParam{
		UID:          req.UID,
		ContractCode: req.ContractCode,
	})
	if err != nil {
		logrus.Errorf("query user join margin err: %s", err.Error())
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252404, err))
		return
	}
	if asset.AssetMode == int(futuresassetpb.AssetMode_ASSET_MODE_MULTI) && req.MarginMode == domain.MarginMode(futuresassetpb.AssetMode_ASSET_MODE_SINGLE) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251023, errors.New("join margin not support isolated")))
		return
	}

	// 爆仓中不能调整杠杆倍数 和仓位模式
	checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: req.UID}}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	pair, err := setting.Service.GetPairSettingInfo(util.BaseQuote(req.ContractCode))
	if err != nil || pair == nil {
		logrus.Errorf("get contract pair err: %+v", err)
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250002, err))
		return
	}

	code, err := handler.userUseCase.AdjustMarginCfg(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

// AdjustMargin 调整逐仓仓位保证金
func (handler *userHandler) AdjustMargin(c *gin.Context) {
	var param payload.MarginParam
	if err := request.ShouldBindJSON(c, &param); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(param.UID) <= 0 || len(param.ContractCode) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param err: %+v", param))))
		return
	}
	if param.MarginMode != domain.MarginModeIsolated {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", param))))
		return
	}
	if param.PosSide != domain.LongPos && param.PosSide != domain.ShortPos && param.PosSide != domain.BothPos {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250007, errors.New(fmt.Sprintf("Pos Side err: %+v", param))))
		return
	}

	// 全仓爆仓中不能调整逐仓仓位保证金
	checkBurst := cachelock.BurstLockParams{Liquidation: cache.LiquidationInfo{UID: param.UID, MarginMode: domain.MarginModeCross}}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}
	// 逐仓当前仓位爆仓中不能调整逐仓仓位保证金
	checkBurst = cachelock.BurstLockParams{
		ContractCode: param.ContractCode,
		Liquidation: cache.LiquidationInfo{
			UID:        param.UID,
			MarginMode: domain.MarginModeIsolated,
			PosSide:    param.PosSide,
		},
	}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	code, err := handler.userUseCase.AdjustMargin(c.Request.Context(), param.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) UserHoldMode(c *gin.Context) {
	var param payload.CommonParam
	if err := request.ShouldBindJSON(c, &param); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(param.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("uid err: %+v", param))))
		return
	}

	positionMode, err := handler.userUseCase.UserHoldMode(c.Request.Context(), param.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.UserHoldMoldErr, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(positionMode))
}

func (handler *userHandler) AdjustHoldMode(c *gin.Context) {
	var req payload.HoldModeParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}
	if req.PositionMode != domain.HoldModeHedge && req.PositionMode != domain.HoldModeBoth {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("hold mode err: %+v", req))))
		return
	}

	code, err := handler.userUseCase.AdjustHoldMode(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) GetJoinMargin(c *gin.Context) {
	var req payload.CommonParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}

	assetMode, err := handler.userUseCase.LoadJoinMargin(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.JoinMarginErr, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(assetMode))
}

func (handler *userHandler) ChangeJoinMargin(c *gin.Context) {
	var req payload.ChangeJoinMarginParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}
	if req.AssetMode != futuresassetpb.AssetMode_ASSET_MODE_SINGLE && req.AssetMode != futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("join margin err: %+v", req))))
		return
	}

	code, err := handler.userUseCase.ChangeJoinMargin(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) AdjustCross(c *gin.Context) {
	var req payload.AdjustCrossParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}
	res, err := handler.userUseCase.AdjustCross(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251030, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(res))
}

func (handler *userHandler) GetOrderConfirm(c *gin.Context) {
	var req payload.CommonParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}

	assetMode, err := handler.userUseCase.GetOrderConfirm(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.OrderConfirmErr, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(assetMode))
}

func (handler *userHandler) ChangeOrderConfirm(c *gin.Context) {
	var req payload.ChangeOrderConfirmParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}
	code, err := handler.userUseCase.ChangeOrderConfirm(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}
	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) GetOrderConfig(c *gin.Context) {
	var req payload.CommonParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}

	orderConfig, err := handler.userUseCase.GetOrderConfig(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.OrderConfigErr, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(orderConfig))
}

func (handler *userHandler) ChangeOrderConfig(c *gin.Context) {
	var req payload.ChangeOrderConfigParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
		return
	}
	code, err := handler.userUseCase.ChangeOrderConfig(c.Request.Context(), req.ToUsecase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

// MarkPrice 获取全部标记价格
// TODO 验证接口返回数据
func (handler *userHandler) MarkPrice(c *gin.Context) {
	data := handler.priceRepo.GetAllMarkPrice(c.Request.Context())

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func checkAdjustLeverage(order *entity.Order, p *repository.LeverageAdjust) bool {
	switch order.PositionMode {
	case domain.HoldModeBoth:
		if p.BLeverage > 0 {
			return false
		}

	case domain.HoldModeHedge:
		switch domain.MarginMode(order.MarginMode) {
		case domain.MarginModeIsolated:
			if order.PosSide == domain.Long && p.LLeverage > 0 {
				return false
			}
			// 卖出开空 看跌
			if order.PosSide == domain.Short && p.SLeverage > 0 {
				return false
			}

		case domain.MarginModeCross:
			if p.Leverage > 0 {
				return false
			}
		}
	}

	return true
}
