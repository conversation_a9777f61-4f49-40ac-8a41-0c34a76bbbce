package payload

import (
	"futures-asset/internal/domain"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"

	"github.com/shopspring/decimal"
)

type (
	// LockParam contains binded and validated data.
	LockParam struct {
		UID             string                    `json:"uid" binding:"required"`    // 用户ID
		UserType        int32                     `json:"user_type"`                 // 用户类型
		OrderID         string                    `json:"order_id"`                  // 委托单ID
		OrderType       futuresassetpb.OrderType  `json:"order_type"`                // 委托类型 (1:限价 2:市价 3-限价止盈 4-市价止盈 5-限价止损 6-市价止损)
		Currency        string                    `json:"currency"`                  // 货币对
		Amount          decimal.Decimal           `json:"amount" binding:"required"` // 数量
		AccountType     string                    `json:"account_type"`              // 账户类型
		OrderTime       int64                     `json:"order_time"`                // 委托创建时间
		Leverage        int                       `json:"leverage"`                  // 杠杆倍数
		MarginMode      futuresassetpb.MarginMode `json:"margin_mode"`               // 仓位模式 (1:全仓 2:逐仓)
		UnfrozenMargin  decimal.Decimal           `json:"unfrozen_margin"`           // 解锁冻结
		IsInnerCall     int                       `json:"is_inner_call"`             // 是否为服务内部调用下单(爆仓) 1:内部服务下单
		IsErr           bool                      `json:"is_err"`                    // 是否错误单解锁
		PositionMode    int32                     `json:"hold_mode"`                 // 持仓模式 1.双向 2.单向 委托接口需要加参数
		IsLimitOrder    int                       `json:"is_limit_order"`            // 是否是限价 1是 2不是
		LiquidationType int                       `json:"liquidation_type"`          // 强平类型
		AwardOpIds      []string                  `json:"award_op_ids"`              // 体验金券ID
		TrialIsEnd      bool                      `json:"trial_is_end"`              // 体验金是否成交
	}
	LockReply struct {
		OrderId      string          `json:"order_id"`      // 委托单ID
		Amount       decimal.Decimal `json:"amount"`        // 数量
		FrozenMargin decimal.Decimal `json:"frozen_margin"` // 冻结数量
	}
)

// func (p LockParam) ContractCode() string {
// 	return util.ContractCode(p.Base, p.Quote)
// }

// // GetLeverage 获取对应的杠杆配置
// func (p LockParam) GetLeverage(l repository.Leverage) int {
// 	return l.GetLeverage(p.PositionMode, p.MarginMode, p.PosSide, p.Side)
// }

// // Value 获取价值
// func (p LockParam) Value() decimal.Decimal {
// 	return p.Price.Mul(p.Amount)
// }

// LockRes lock reply data.
type LockRes struct {
	Code           domain.Code                 `json:"code"`
	Msg            string                      `json:"msg"`
	Amount         decimal.Decimal             `json:"amount"`
	FrozenMargin   decimal.Decimal             `json:"frozen_margin"`
	PositionMode   futuresassetpb.PositionMode `json:"hold_mode"`
	OrderId        string                      `json:"order_id"`
	JoinMarginRate decimal.Decimal             `json:"rate"`
	HaveTrial      int                         `json:"have_trial"`
}
