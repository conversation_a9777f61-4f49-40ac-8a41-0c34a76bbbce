package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type (
	SwapInit struct {
		UID string `json:"uid" binding:"required"` // 用户ID
	}
	MarginParam struct {
		CommonParam
		Amount     decimal.Decimal   `json:"amount"`
		MarginMode domain.MarginMode `json:"margin_mode"`
		PosSide    int32             `json:"pos_side"`
		AwardOpIds []string          `json:"award_op_ids"`
	}
	HoldModeParam struct {
		CommonParam
		PositionMode int `json:"hold_mode"` // 保证金模式 1.双向持仓 2.单向持仓
	}
	LeverageMarginAdAdjust struct {
		CommonParam
		MarginMode domain.MarginMode `json:"margin_mode"`
		Leverage   int               `json:"leverage"`
		LLeverage  int               `json:"l_leverage"` // 逐仓 多仓杠杆倍数
		SLeverage  int               `json:"s_leverage"` // 逐仓 空仓杠杆倍数
		BLeverage  int               `json:"b_leverage"` // 单向持仓 逐仓杠杆倍数
	}
	HoldModeRes struct {
		UID          string `json:"uid"`       // 用户ID
		PositionMode int    `json:"hold_mode"` // positionMode
	}
	JoinMarginRes struct {
		UID       string `json:"uid"`         // 用户ID
		AssetMode int    `json:"join_margin"` // 保证金模式 1.单币保证金 2.联合保证金
	}
	ChangeJoinMarginParam struct {
		CommonParam
		AssetMode futuresassetpb.AssetMode `json:"join_margin"`
	}
	OrderConfirm struct {
		LimitConfirm    int `json:"limit_confirm"`     // 限价二次确认
		MarketConfirm   int `json:"market_confirm"`    // 市价二次确认
		TriggerConfirm  int `json:"trigger_confirm"`   // 止盈止损二次确认
		PostOnlyConfirm int `json:"post_only_confirm"` // 只做Maker二次确认
		BackhandConfirm int `json:"backhand_confirm"`  // 反手二次确认
	}
	OrderConfirmRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfirm
	}
	ChangeOrderConfirmParam struct {
		CommonParam
		OrderConfirm
	}
	OrderConfig struct {
		RebornCard int `json:"reborn_card"` // 复活卡
		Trial      int `json:"trial"`       // 体验金
	}
	OrderConfigRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfig
	}
	ChangeOrderConfigParam struct {
		CommonParam
		OrderConfig
	}
	AdjustCrossParam struct {
		CommonParam
	}
	MarginAdjust struct {
		CommonParam
		MarginMode domain.MarginMode `json:"margin_mode"`
	}
	LeverageAdjust struct {
		CommonParam
		Leverage  int `json:"leverage"`
		LLeverage int `json:"l_leverage"` // 逐仓 多仓杠杆倍数
		SLeverage int `json:"s_leverage"` // 逐仓 空仓杠杆倍数
		BLeverage int `json:"b_leverage"` // 单向持仓 逐仓杠杆倍数
	}
)

func (p *SwapInit) ToUsecase() *repository.SwapInit {
	return &repository.SwapInit{
		UID: p.UID,
	}
}

type CommonParam struct {
	UID          string `json:"uid" binding:"required"` // 用户ID
	ContractCode string `json:"contract_code"`          // 合约代码
}

func (p *CommonParam) ToUsecase() *repository.CommonParam {
	return &repository.CommonParam{
		UID:          p.UID,
		ContractCode: p.ContractCode,
	}
}

func (p *ChangeJoinMarginParam) ToUsecase() *repository.ChangeJoinMarginParam {
	return &repository.ChangeJoinMarginParam{
		CommonParam: *p.CommonParam.ToUsecase(),
		AssetMode:   p.AssetMode,
	}
}

func (p *HoldModeParam) ToUsecase() *repository.HoldModeParam {
	return &repository.HoldModeParam{
		CommonParam:  *p.CommonParam.ToUsecase(),
		PositionMode: p.PositionMode,
	}
}

func (p *MarginParam) ToUsecase() *repository.MarginParam {
	return &repository.MarginParam{
		CommonParam: *p.CommonParam.ToUsecase(),
		Amount:      p.Amount,
		MarginMode:  p.MarginMode,
		PosSide:     p.PosSide,
		AwardOpIds:  p.AwardOpIds,
	}
}

func (p *MarginAdjust) ToUsecase() *repository.MarginAdjust {
	return &repository.MarginAdjust{
		CommonParam: *p.CommonParam.ToUsecase(),
		MarginMode:  p.MarginMode,
	}
}

func (p *AdjustCrossParam) ToUsecase() *repository.AdjustCrossParam {
	return &repository.AdjustCrossParam{
		CommonParam: *p.CommonParam.ToUsecase(),
	}
}

func (p *LeverageMarginAdAdjust) ToUsecase() *repository.LeverageMarginAdAdjust {
	return &repository.LeverageMarginAdAdjust{
		CommonParam: *p.CommonParam.ToUsecase(),
		MarginMode:  p.MarginMode,
		Leverage:    p.Leverage,
		LLeverage:   p.LLeverage,
		SLeverage:   p.SLeverage,
		BLeverage:   p.BLeverage,
	}
}

func (p *LeverageAdjust) ToUsecase() *repository.LeverageAdjust {
	return &repository.LeverageAdjust{
		CommonParam: *p.CommonParam.ToUsecase(),
		Leverage:    p.Leverage,
		LLeverage:   p.LLeverage,
		SLeverage:   p.SLeverage,
		BLeverage:   p.BLeverage,
	}
}

func (p *ChangeOrderConfirmParam) ToUsecase() *repository.ChangeOrderConfirmParam {
	return &repository.ChangeOrderConfirmParam{
		CommonParam: *p.CommonParam.ToUsecase(),
		OrderConfirm: repository.OrderConfirm{
			LimitConfirm:    p.LimitConfirm,
			MarketConfirm:   p.MarketConfirm,
			TriggerConfirm:  p.TriggerConfirm,
			PostOnlyConfirm: p.PostOnlyConfirm,
			BackhandConfirm: p.BackhandConfirm,
		},
	}
}

func (p *ChangeOrderConfigParam) ToUsecase() *repository.ChangeOrderConfigParam {
	return &repository.ChangeOrderConfigParam{
		CommonParam: *p.CommonParam.ToUsecase(),
		OrderConfig: repository.OrderConfig{
			RebornCard: p.RebornCard,
			Trial:      p.Trial,
		},
	}
}
