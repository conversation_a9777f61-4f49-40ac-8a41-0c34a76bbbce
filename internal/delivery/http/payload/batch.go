package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
)

type AssetParam struct {
	UserIds  []string `json:"userIds" binding:"required"`
	Currency string   `json:"currency" binding:"required"`
}

func (p *AssetParam) ToUseCase() usecase.AssetParam {
	return usecase.AssetParam{
		UserIds:  p.UserIds,
		Currency: p.Currency,
	}
}

// 批量冻结参数
type (
	BatchLock struct {
		AccountType  string            `json:"accountType"`
		UID          string            `json:"uid" binding:"required"`
		UserType     int               `json:"userType"`
		MarginMode   domain.MarginMode `json:"marginMode"`
		Base         string            `json:"base" binding:"required"`
		Quote        string            `json:"quote" binding:"required"`
		Side         int32             `json:"side" binding:"required"`
		PosSide      int32             `json:"pos_side" binding:"required"`
		OperateTime  int64             `json:"operateTime"`
		Platform     string            `json:"platform"`      // 来源 (WEB,APP,H5)
		PositionMode int32             `json:"position_mode"` // 持仓模式 1.双向 2.单向 委托接口需要加参数
		Orders       []BatchLockOrder  `json:"orders"`
	}
	BatchLockOrder struct {
		OrderId   string          `json:"orderId"`
		OrderType int32           `json:"orderType"` // 委托类型 (1:限价 2:市价 3-限价止盈 4-市价止盈 5-限价止损 6-市价止损)
		Side      int32           `json:"side"`      // 委托单方向 (1:买 2:卖)
		Price     decimal.Decimal `json:"price"`
		Amount    decimal.Decimal `json:"amount"`
		Leverage  int             `json:"leverage"`
	}

	BatchLockItem struct {
		Code         domain.Code     `json:"code"`
		OrderId      string          `json:"orderId"`
		Amount       decimal.Decimal `json:"amount"`       // 市价开仓或止盈止损平仓,返回实际开仓或平仓的数量(需考虑不足的情况)
		FrozenMargin decimal.Decimal `json:"frozenMargin"` // 开仓时-返回实际冻结的保证金数量
		HaveTrial    int             `json:"haveTrial"`
	}
	BatchLockReply struct {
		Code   int             `json:"code"`
		Msg    string          `json:"msg"`
		Orders []BatchLockItem `json:"orders"`
	}
)

func (p BatchLock) IsClose() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Short) ||
		(p.Side == domain.Sell && p.PosSide == domain.Long)
}

func (p BatchLock) IsOpen() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Long) ||
		(p.Side == domain.Sell && p.PosSide == domain.Short)
}

// 批量解冻参数
type (
	// BatchUnlockParam 批量解锁用户保证金
	BatchUnlockParam struct {
		UID         string      `json:"uid"`
		Base        string      `json:"base"`
		Quote       string      `json:"quote"`
		AccountType string      `json:"accountType"`
		OperateTime int64       `json:"operateTime"`
		Orders      []LockParam `json:"orders" binding:"required"`
	}

	BatchUnlockItem struct {
		Code    domain.Code `json:"code"`
		OrderId string      `json:"orderId"`
	}
)
