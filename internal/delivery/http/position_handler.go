package http

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"futures-asset/cache"
	"futures-asset/cache/cachelock"
	"futures-asset/cache/swapcache"
	"futures-asset/handler/cron"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type PositionHandlerParam struct {
	dig.In

	PositionUseCase usecase.PositionUseCase
}

type positionHandler struct {
	positionUseCase usecase.PositionUseCase
}

func newPositionHandler(param PositionHandlerParam) *positionHandler {
	return &positionHandler{
		positionUseCase: param.PositionUseCase,
	}
}

// UserPos 获取用户持仓
func (handler *positionHandler) UserPos(c *gin.Context) {
	var req payload.SwapParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 {
		msg := fmt.Sprintf("param err: %+v", req)
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(msg)))

		return
	}

	pos, err := handler.positionUseCase.UserPos(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(pos))
}

func (handler *positionHandler) UserIsBurst(c *gin.Context) {
	var req payload.UserIsBurstReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 {
		msg := fmt.Sprintf("param err: %+v", req)
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(msg)))
		return
	}

	checkBurst := cachelock.BurstLockParams{
		ContractCode: strings.ToUpper(req.ContractCode),
		Liquidation: cache.LiquidationInfo{
			UID:        req.UID,
			MarginMode: domain.MarginMode(req.MarginMode),
			PosSide:    req.PosSide,
		},
	}
	if swapcache.UserBursting(checkBurst) {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(false))
}

// SendRebornCard 发放重生卡
// func (handler *positionHandler) SendRebornCard(c *gin.Context) {
// 	var req payload.SendRebornCardParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}

// 	if len(req.UID) <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no user id err: %+v", req))))
// 		return
// 	}

// 	if req.ExpireTime <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no expire time err: %+v", req))))
// 		return
// 	}

// 	if req.Period <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no period time err: %+v", req))))
// 		return
// 	}

// 	card := cache.RebornCard{
// 		Id:               util.GenerateId(),
// 		ContractCodeList: make([]string, 0),
// 		UID:              req.UID,
// 		ExpireTime:       req.ExpireTime,
// 		TriggerPosIds:    make([]string, 0),
// 		Period:           req.Period,
// 	}
// 	err := cache.SaveUserRebornCard(req.UID, card)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252407, errors.New(fmt.Sprintf("save reborn card err: %+v", req))))
// 		return
// 	}
// 	c.JSON(http.StatusOK, response.NewSuccess(map[string]interface{}{"id": card.Id}))
// }

// TestSettlement 测试时使用，
func (handler *positionHandler) TestSettlement(c *gin.Context) {
	var req payload.TestSettlement
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	req.ContractCode = strings.ToUpper(req.ContractCode)

	// usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	// redis := redislib.Redis()
	// users, err := redis.HGetAll(usersKey)
	// if err != nil {
	//	logrus.Error("burstService HGetAll", usersKey, "error:", err)
	//	return
	// }
	// logrus.Info(fmt.Sprintf("Settlement All._contractConfig:%+v", param.ContractCode))
	// logrus.Info(fmt.Sprintf("Settlement pCache:%+v", pCache))
	// logrus.Info(fmt.Sprintf("Settlement users:%+v", users))
	contractConfig, err := setting.Service.GetPairSettingInfo(util.BaseQuote(req.ContractCode))
	if err != nil {
		logrus.Error("TestSettlement", err)
	}
	fundRate := swapcache.FundRate(req.ContractCode, *contractConfig)

	go cron.SettlementAll(fundRate, req.ContractCode, *contractConfig, nil)

	c.JSON(http.StatusOK, response.NewSuccess(""))
}
