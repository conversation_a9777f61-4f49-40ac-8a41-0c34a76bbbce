package repository

import (
	"context"
	"strings"

	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"
)

type FuturesEngineRepositoryParam struct {
	dig.In

	FuturesEngineRepository *futuresEngineRepository
}

type futuresEngineRepository struct {
	futuresEngineRepo *futuresEngineRepository
}

func NewFuturesEngineRepository(param FuturesEngineRepositoryParam) repository.FuturesEngineRepository {
	return &futuresEngineRepository{
		futuresEngineRepo: param.FuturesEngineRepository,
	}
}

// GetDepthFirstPrice TODO 获取深度卖一买一价格
func (f *futuresEngineRepository) GetDepthFirstPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal, error) {
	return decimal.Zero, decimal.Zero, nil
}

// BothReducePositions TODO
func (f *futuresEngineRepository) BothReducePositions(ctx context.Context, params *repository.ReducePositionsParams) (*repository.BaseReply, error) {
	reduceData := new(repository.BaseReply)

	return reduceData, nil
}

// GetContractLastPrice 获取合约最新成交价
//
//	Params:
//	  base string: 交易币
//	  quote string: 计价币
//	Return:
//	  price decimal.Decimal: 最新成交价
func (f *futuresEngineRepository) GetContractLastPrice(ctx context.Context, base, quote string) (price decimal.Decimal, err error) {
	base = strings.ToLower(base)
	quote = strings.ToLower(quote)

	return decimal.Zero, nil
}

// ClosePositions implements repository.FuturesEngineRepository.
func (f *futuresEngineRepository) ClosePositions(ctx context.Context, params *repository.ClosePositionParams) (*repository.ClosePositionReply, error) {
	panic("unimplemented")
}

// GetSpotLastPrice 获取现货最新成交价
func (f *futuresEngineRepository) GetSpotLastPrice(ctx context.Context, base string, quote string) (price decimal.Decimal, err error) {
	panic("unimplemented")
}
