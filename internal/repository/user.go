package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/db/swap"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/message"
	"futures-asset/pkg/match"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/setting"
	"futures-asset/pkg/user"
	"futures-asset/util"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	"gorm.io/gorm"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// UserRepositoryParam 用户仓储参数
type UserRepositoryParam struct {
	dig.In

	RDB *redis.ClusterClient `name:"redis-cluster"`
	RS  *redsync.Redsync     `name:"rs"`
	DB  *gorm.DB             `name:"db"`

	PriceRepository   repository.PriceRepository
	CacheRepository   repository.CacheRepository
	AssetRepository   repository.AssetRepository
	TrialRepository   repository.TrialRepository
	FormulaRepository repository.FormulaRepository
}

type userRepository struct {
	rdb *redis.ClusterClient
	rs  *redsync.Redsync
	db  *gorm.DB

	priceRepo   repository.PriceRepository
	cacheRepo   repository.CacheRepository
	assetRepo   repository.AssetRepository
	trialRepo   repository.TrialRepository
	formulaRepo repository.FormulaRepository
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(param UserRepositoryParam) repository.UserRepository {
	return &userRepository{
		rdb: param.RDB,
		rs:  param.RS,
		db:  param.DB,

		priceRepo:   param.PriceRepository,
		cacheRepo:   param.CacheRepository,
		assetRepo:   param.AssetRepository,
		trialRepo:   param.TrialRepository,
		formulaRepo: param.FormulaRepository,
	}
}

// AdjustCross 仓位模式一键改为全仓
func (repo *userRepository) AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error) {
	var res []string
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return res, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	asset, err := repo.cacheRepo.Load(ctx, p.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return res, err
	}
	if asset.HasFrozen("") {
		return []string{}, fmt.Errorf("user have Frozen order")
	}

	for k, v := range asset.IsolatedList {
		for _, pos := range v {
			if pos.Pos.IsZero() {
				continue
			}
			res = append(res, k)
		}
	}
	res = util.SliceUnique(res)
	if len(res) > 0 {
		return []string{}, fmt.Errorf("%s has isolated pos", strings.Join(res, ","))
	}

	for i := range asset.Leverage {
		if asset.Leverage[i].MarginMode == domain.MarginModeIsolated {
			asset.Leverage[i].MarginMode = domain.MarginModeCross
			res = append(res, asset.Leverage[i].ContractCode)
		}
	}
	if err = repo.cacheRepo.UpdateLeverage(ctx, p.UID, asset.Leverage); err != nil {
		logrus.Error(fmt.Sprintf("update leverage err:%+v,userAsset:%+v", err, *asset))
		return res, err
	}

	return res, nil
}

func (repo *userRepository) AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, error) {
	hasAgree := user.Service.HasAgree(p.UID)
	if hasAgree.HasAgree != 1 {
		return domain.Code251003, errors.New(domain.ErrMsg[domain.Code251003])
	}
	// lock taker & maker
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	base, quote := util.BaseQuote(p.ContractCode)
	// 获取用户资产
	asset, err := repo.cacheRepo.Load(ctx, p.UID, p.ContractCode)
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, err
	}

	change := false

	// 普通模式（锁仓模式） 不能有锁定仓位
	if asset.PositionMode == domain.HoldModeHedge {
		if !asset.LongPos.FrozenPos().IsZero() || !asset.ShortPos.FrozenPos().IsZero() {
			return domain.Code251001, fmt.Errorf("user holds unfinish order")
		}
	}

	leverage := asset.GetLeverage(p.ContractCode)
	// 修改杠杆倍数逻辑
	{
		switch asset.PositionMode {
		case domain.HoldModeBoth:
			if p.BLeverage > 0 && leverage.BLeverage != p.BLeverage {
				if !asset.BothPos.Pos.IsZero() && leverage.MarginMode == domain.MarginModeIsolated {
					if p.BLeverage >= leverage.BLeverage { // 调大杠杆倍数
						marginLevel, _, _ := setting.FetchMarginLevel(base, quote, asset.BothPos.CalcPosValue(repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)))
						if p.BLeverage > marginLevel.Lever {
							return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
						}
					} else {
						return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
					}
				}
				// 更新币对下所有逐仓杠杆倍数
				asset.BothPos.Leverage = p.BLeverage
				err = repo.cacheRepo.UpdateBothPos(ctx, p.ContractCode, asset)
				if err != nil {
					logrus.Error(fmt.Sprintf("update both pos err:%+v,userAsset:%+v", err, *asset))
				}
				leverage.BLeverage = p.BLeverage
				change = true
			}

		default:
			// 普通模式（锁仓模式） 不能有锁定仓位
			if asset.PositionMode == domain.HoldModeHedge {
				if !asset.LongPos.FrozenPos().IsZero() || !asset.ShortPos.FrozenPos().IsZero() {
					return domain.Code251001, fmt.Errorf("user holds unfinish order")
				}
			}
			switch leverage.MarginMode {
			case domain.MarginModeIsolated:
				if p.LLeverage > 0 && leverage.LLeverage != p.LLeverage {
					if !asset.LongPos.Pos.IsZero() {
						if p.LLeverage >= leverage.LLeverage { // 调大杠杆倍数
							marginLevel, _, _ := setting.FetchMarginLevel(base, quote,
								asset.LongPos.CalcPosValue(repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)).Add(asset.ShortPos.CalcPosValue(repo.priceRepo.GetMarkPrice(ctx, p.ContractCode))))
							if p.LLeverage > marginLevel.Lever {
								return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}

						asset.LongPos.Leverage = p.LLeverage
						err = repo.cacheRepo.UpdateLongPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update long pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					// 体验金仓位
					if !asset.TrialLongPos.Pos.IsZero() {
						if p.LLeverage >= leverage.LLeverage { // 调大
							trialList := asset.TrialDetail.Gets(asset.TrialLongPos.AwardOpIds...)
							if len(trialList) > 0 {
								if p.LLeverage > int(trialList.GetLeverage()) {
									return domain.TrialMaxLeverageErr, fmt.Errorf("%v", trialList.GetLeverage())
								}
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}

						asset.TrialLongPos.Leverage = p.LLeverage
						err = repo.trialRepo.UpdateTrialLongPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update trial long pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					leverage.LLeverage = p.LLeverage
					change = true
				}

				if p.SLeverage > 0 && leverage.SLeverage != p.SLeverage {
					if !asset.ShortPos.Pos.IsZero() {
						if p.SLeverage >= leverage.SLeverage { // 调大杠杆倍数
							marginLevel, _, _ := setting.FetchMarginLevel(base, quote,
								asset.LongPos.CalcPosValue(repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)).Add(asset.ShortPos.CalcPosValue(repo.priceRepo.GetMarkPrice(ctx, p.ContractCode))))
							if p.SLeverage > marginLevel.Lever {
								return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}

						asset.ShortPos.Leverage = p.SLeverage
						err = repo.trialRepo.UpdateTrialShortPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update short pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					// 体验金
					if !asset.TrialShortPos.Pos.IsZero() {
						if p.SLeverage >= leverage.SLeverage {
							trialList := asset.TrialDetail.Gets(asset.TrialShortPos.AwardOpIds...)
							if len(trialList) > 0 {
								if p.SLeverage > int(trialList.GetLeverage()) {
									return domain.TrialMaxLeverageErr, fmt.Errorf("%v", trialList.GetLeverage())
								}
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}

						asset.TrialShortPos.Leverage = p.SLeverage
						err = repo.trialRepo.UpdateTrialShortPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update trial short pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					leverage.SLeverage = p.SLeverage
					change = true
				}

			case domain.MarginModeCross:
				if p.Leverage > 0 && leverage.Leverage != p.Leverage {
					if !asset.LongPos.Pos.Add(asset.ShortPos.Pos).Add(asset.BothPos.Pos.Abs()).IsZero() {
						code, err := repo.adjustCrossLeverageCfg(ctx, p, asset, &leverage)
						if err != nil {
							return code, err
						}
					}
					repo.UpdatePosLeverage(ctx, asset, p.Leverage)
					leverage.Leverage = p.Leverage
					change = true
				}

			default:

			}

		}
	}

	if change {
		newLeverage := repo.adjustLeverage(ctx, asset, leverage)
		if err := repo.cacheRepo.UpdateLeverage(ctx, asset.UID, newLeverage); err != nil {
			return domain.Code251022, err
		}
		repo.pushData(ctx, p.UID, p.ContractCode, leverage, leverage.MarginMode, asset) // 推送仓位模式 和杠杠倍数

		// 更新库中杠杆倍数
		dbswap := entity.NewPosSwap()
		dbswap.UID = p.UID
		dbswap.ContractCode = p.ContractCode
		dbswap.Leverage = p.Leverage
		if err := dbswap.UpdatePosLeverage(nil); err != nil {
			logrus.Error(fmt.Sprintf("update pos_swap leverage err:%+v,userAsset:%+v", err, dbswap))
		}
	}
	p.Leverage = leverage.Leverage
	p.LLeverage = leverage.LLeverage
	p.SLeverage = leverage.SLeverage
	p.BLeverage = leverage.BLeverage

	return http.StatusOK, nil
}

func (repo *userRepository) AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error) {
	hasAgree := user.Service.HasAgree(p.UID)
	if hasAgree.HasAgree != 1 {
		return domain.Code251003, errors.New(domain.ErrMsg[domain.Code251003])
	}
	// lock taker & maker
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	base, quote := util.BaseQuote(p.ContractCode)
	// 获取用户资产
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base: base, Quote: quote,
		},
	}, p.UID)

	asset, err := repo.cacheRepo.Load(ctx, p.UID, p.ContractCode)
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, err
	}

	change := false

	// 当前币对不能有冻结资金
	if asset.GetFrozenByCode(p.ContractCode).Sign() > 0 {
		return domain.Code251001, fmt.Errorf("user holds unfinish order")
	}

	// 当前币对不能有体验金冻结资金
	if asset.GetFrozenByCode(p.ContractCode, true).Sign() > 0 {
		return domain.Code251001, fmt.Errorf("user trial holds unfinish order")
	}

	// 普通模式（锁仓模式） 不能有锁定仓位
	if asset.PositionMode == domain.HoldModeHedge {
		if !asset.LongPos.FrozenPos().IsZero() || !asset.ShortPos.FrozenPos().IsZero() {
			return domain.Code251001, fmt.Errorf("user holds unfinish order")
		}
	}

	leverage := asset.GetLeverage(p.ContractCode)
	// 修改币对全、逐仓逻辑
	{
		if p.MarginMode > 0 && leverage.MarginMode != p.MarginMode && p.MarginMode != domain.MarginModeNone {
			// 混合保证金不允许切换成逐仓
			if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI && p.MarginMode == domain.MarginModeIsolated {
				return domain.Code251023, fmt.Errorf("join margin not support isolated")
			}

			// 币对下不能有持仓
			crossPosList, ok := userCache.CrossList[p.ContractCode]
			if ok {
				for _, pos := range crossPosList {
					if !pos.Pos.IsZero() {
						return domain.Code251020, domain.ErrOrderOrPos
					}
				}
			}
			isolatedPosList, ok := userCache.IsolatedList[p.ContractCode]
			if ok {
				for _, pos := range isolatedPosList {
					if !pos.Pos.IsZero() {
						return domain.Code251020, domain.ErrOrderOrPos
					}
				}
			}

			leverage.MarginMode = p.MarginMode
			change = true
		}
	}

	// 修改杠杆倍数逻辑
	{
		switch asset.PositionMode {
		case domain.HoldModeBoth:
			if p.BLeverage > 0 && leverage.BLeverage != p.BLeverage {
				if !asset.BothPos.Pos.IsZero() && leverage.MarginMode == domain.MarginModeIsolated {
					if p.BLeverage >= leverage.BLeverage { // 调大杠杆倍数
						markPrice := repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)
						marginLevel, _, _ := setting.FetchMarginLevel(base, quote, asset.BothPos.CalcPosValue(markPrice))
						if p.BLeverage > marginLevel.Lever {
							return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
						}
					} else {
						return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
					}
				}
				// 更新币对下所有逐仓杠杆倍数
				asset.BothPos.Leverage = p.BLeverage
				err = userCache.UpdateBothPos(asset)
				if err != nil {
					logrus.Error(fmt.Sprintf("update both pos err:%+v,userAsset:%+v", err, *asset))
				}
				leverage.BLeverage = p.BLeverage
				change = true
			}

		default:
			switch leverage.MarginMode {
			case domain.MarginModeIsolated:
				if p.LLeverage > 0 && leverage.LLeverage != p.LLeverage {
					if !asset.LongPos.Pos.IsZero() {
						if p.LLeverage >= leverage.LLeverage { // 调大杠杆倍数
							markPrice := repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)
							marginLevel, _, _ := setting.FetchMarginLevel(base, quote,
								asset.LongPos.CalcPosValue(markPrice).Add(asset.ShortPos.CalcPosValue(markPrice)))
							if p.LLeverage > marginLevel.Lever {
								return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}

						asset.LongPos.Leverage = p.LLeverage
						err = userCache.UpdateLongPos(asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update long pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					// 体验金仓位
					if !asset.TrialLongPos.Pos.IsZero() {
						if p.LLeverage >= leverage.LLeverage { // 调大
							trialList := asset.TrialDetail.Gets(asset.TrialLongPos.AwardOpIds...)
							if len(trialList) > 0 {
								if p.LLeverage > int(trialList.GetLeverage()) {
									return domain.TrialMaxLeverageErr, fmt.Errorf("%v", trialList.GetLeverage())
								}
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}
						asset.TrialLongPos.Leverage = p.LLeverage
						err = repo.trialRepo.UpdateTrialLongPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update trial long pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					leverage.LLeverage = p.LLeverage
					change = true
				}

				if p.SLeverage > 0 && leverage.SLeverage != p.SLeverage {
					if !asset.ShortPos.Pos.IsZero() {
						if p.SLeverage >= leverage.SLeverage { // 调大杠杆倍数
							markPrice := repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)
							marginLevel, _, _ := setting.FetchMarginLevel(base, quote,
								asset.LongPos.CalcPosValue(markPrice).Add(asset.ShortPos.CalcPosValue(markPrice)))
							if p.SLeverage > marginLevel.Lever {
								return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}
						asset.ShortPos.Leverage = p.SLeverage
						err = userCache.UpdateShortPos(asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update short pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					// 体验金
					if !asset.TrialShortPos.Pos.IsZero() {
						if p.SLeverage >= leverage.SLeverage {
							trialList := asset.TrialDetail.Gets(asset.TrialShortPos.AwardOpIds...)
							if len(trialList) > 0 {
								if p.SLeverage > int(trialList.GetLeverage()) {
									return domain.TrialMaxLeverageErr, fmt.Errorf("%v", trialList.GetLeverage())
								}
							}
						} else {
							return domain.Code251005, fmt.Errorf(domain.ErrMsg[domain.Code251005])
						}
						asset.TrialShortPos.Leverage = p.SLeverage
						err = repo.trialRepo.UpdateTrialShortPos(ctx, p.ContractCode, asset)
						if err != nil {
							logrus.Error(fmt.Sprintf("update trial short pos err:%+v,userAsset:%+v", err, *asset))
						}
					}

					leverage.SLeverage = p.SLeverage
					change = true
				}

			case domain.MarginModeCross:
				if p.Leverage > 0 && leverage.Leverage != p.Leverage {
					if !asset.LongPos.Pos.Add(asset.ShortPos.Pos).Add(asset.BothPos.Pos.Abs()).IsZero() {
						code, err := repo.adjustCrossLeverage(ctx, p, asset, &leverage)
						if err != nil {
							return code, err
						}
					}
					repo.UpdatePosLeverage(ctx, asset, p.Leverage)
					leverage.Leverage = p.Leverage
					change = true
				}

			default:
			}
		}
	}

	if change {
		newLeverage := repo.adjustLeverage(ctx, asset, leverage)
		if err := repo.cacheRepo.UpdateLeverage(ctx, p.UID, newLeverage); err != nil {
			return domain.Code251022, err
		}
		repo.pushData(ctx, p.UID, p.ContractCode, leverage, leverage.MarginMode, asset) // 推送仓位模式 和杠杠倍数

		// 更新库中杠杆倍数
		dbswap := entity.NewPosSwap()
		dbswap.UID = p.UID
		dbswap.ContractCode = p.ContractCode
		dbswap.Leverage = p.Leverage
		if err := dbswap.UpdatePosLeverage(nil); err != nil {
			logrus.Error(fmt.Sprintf("update pos_swap leverage err:%+v,userAsset:%+v", err, dbswap))
		}
	}
	p.Leverage = leverage.Leverage
	p.LLeverage = leverage.LLeverage
	p.SLeverage = leverage.SLeverage
	p.BLeverage = leverage.BLeverage
	p.MarginMode = leverage.MarginMode
	return http.StatusOK, nil
}

func (repo *userRepository) AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error) {
	// lock taker & maker
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	// 获取用户资产
	base, quote := util.BaseQuote(p.ContractCode)
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base:  base,
			Quote: quote,
		},
	}, p.UID)

	asset, err := repo.cacheRepo.Load(ctx, p.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, err
	}

	// 获取需要更改的仓位
	isTrial := len(p.AwardOpIds) > 0
	pos := asset.GetPos(p.PosSide, isTrial)
	var trialList repository.TrialTimeList

	if !isTrial {
		if p.Amount.IsNegative() {
			// 减少逐仓保证金
			markPrice := repo.priceRepo.GetMarkPrice(ctx, pos.ContractCode)
			maxOut := decimal.Max(decimal.NewFromInt(0), pos.IsolatedMargin.Sub(pos.GetIsolatedHoldingMargin(markPrice)).Sub(pos.TrialMargin)) // max(0, 仓位保证金-仓位维持保证金-仓位体验金)
			if pos.Leverage <= 0 {
				return domain.Code252404, errors.New("leverage err")
			}
			posMargin, _ := util.RoundCeil(pos.CalcPosValue(markPrice).Div(decimal.NewFromInt(int64(pos.Leverage))), domain.CurrencyPrecision)
			canOut := pos.IsolatedMarginBalance(markPrice).Sub(posMargin) // 逐仓仓位保证金余额-仓位保证金
			if decimal.Min(canOut, maxOut).LessThan(p.Amount.Abs()) {
				logrus.Error(fmt.Sprintf("maxOut:%+v,less than p.amount:%+v", maxOut, p.Amount))
				return domain.Code252001, errors.New("isolated pos margin insufficient")
			}
		} else {
			// 追加逐仓保证金 可转
			canOut, err := repo.assetRepo.CanTransfer(ctx, asset, quote)
			if err != nil {
				logrus.Error(fmt.Sprintf("AdjustMargin CanTransfer error:%v, canOut:%+v, p.amount:%+v", err, canOut, p.Amount))
				return domain.Code252407, err
			}
			if canOut.LessThan(p.Amount.Abs()) {
				logrus.Error(fmt.Sprintf("canOut:%+v,less than p.amount:%+v", canOut, p.Amount))
				return domain.Code252002, errors.New("amount insufficient")
			}
		}
	} else {
		// 体验金划转保证金
		trialList = asset.TrialDetail.Gets(p.AwardOpIds...)
		if len(trialList) <= 0 {
			return domain.TrialOpIdErr, errors.New("trial not found")
		}

		if p.Amount.Abs().Equal(trialList.GetOpenAmount()) {
			return domain.TrialInsufficientErr, errors.New("isolated pos trial margin not equal")
		}

		if p.Amount.IsNegative() {
			return domain.TrialSubMarginErr, errors.New("isolated pos trial margin sub err")
		} else {
			if trialList.GetAvailableAmount().LessThan(p.Amount.Abs()) {
				logrus.Error(fmt.Sprintf("trial canOut:%+v,less than p.amount:%+v", trialList.GetAvailableAmount(), p.Amount))
				return domain.TrialInsufficientErr, errors.New("trial amount insufficient")
			}

			trialList.AddOpenAmount(p.Amount.Abs())
			pos.AddAwardOpIds(p.AwardOpIds...)
		}
	}

	pos.IsolatedMargin = pos.IsolatedMargin.Add(p.Amount)
	if isTrial {
		pos.TrialMargin = pos.TrialMargin.Add(p.Amount)
	}
	totalBalance := asset.CBalance(pos.Currency)
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		totalBalance, err = repo.assetRepo.TotalJoinBalance(ctx, asset)
		if err != nil {
			logrus.Error(fmt.Sprintf("AdjustMargin TotalJoinBalance error:%v", err))
			return domain.Code252407, err
		}
		rate = repo.priceRepo.SpotURate(ctx, pos.Currency)
	}

	if err != nil {
		logrus.Error(fmt.Sprintf("AdjustMargin OtherCrossUnreal error:%v", err))
		return domain.Code252407, err
	}

	posSwap := repository.PosQuery{
		PosSwap: repository.PosSwap{
			Currency:        pos.Currency,
			Leverage:        pos.Leverage,
			IsolatedMargin:  pos.IsolatedMargin,
			Liquidation:     pos.Liquidation,
			LiquidationType: domain.LiquidationType(pos.LiquidationType),
			MarginMode:      pos.MarginMode,
			OpenPriceAvg:    pos.OpenPriceAvg.Truncate(domain.PricePrecision),
			OpenTime:        pos.OpenTime / 1e9,
			PosAvailable:    pos.PosAvailable,
			Pos:             pos.Pos,
			PosSide:         pos.PosSide,
			ContractCode:    pos.ContractCode,
			UID:             pos.UID,
			UserType:        int32(pos.UserType),
			PosStatus:       domain.PosStatus(pos.PosStatus),
			ProfitReal:      pos.ProfitReal,
			Subsidy:         pos.Subsidy,
		},
		Margin: pos.IsolatedMargin,
	}

	markPrice := repo.priceRepo.GetMarkPrice(ctx, pos.ContractCode)
	if !isTrial {
		repo.formulaRepo.CalcLiquidationPrice(&posSwap, &asset.LongPos, &asset.ShortPos, &asset.BothPos,
			repo.assetRepo.HoldCostTotalIsolated(ctx, asset, quote, true).Mul(rate),
			totalBalance, repo.assetRepo.OtherCrossUnreal(ctx, asset, quote).Mul(rate),
			repo.assetRepo.OtherCrossMaintainMargin(ctx, asset, quote).Mul(rate), markPrice)
	} else {
		repo.formulaRepo.CalcLiquidationPrice(&posSwap, &asset.TrialLongPos, &asset.TrialShortPos, &asset.TrialBothPos,
			repo.assetRepo.HoldCostTotalIsolated(ctx, asset, quote, false).Mul(rate),
			totalBalance, repo.assetRepo.OtherCrossUnreal(ctx, asset, quote).Mul(rate),
			repo.assetRepo.OtherCrossMaintainMargin(ctx, asset, quote).Mul(rate), markPrice)
	}

	err = userCache.SetPos(p.PosSide, pos, len(p.AwardOpIds) > 0)
	if err != nil {
		logrus.Error(fmt.Sprintf("update pos error:%v", err))
		return domain.Code251122, err
	}

	// 如果是体验金，更新体验金
	if isTrial && len(trialList) > 0 {
		err = repo.trialRepo.UpdateTrialAsset(ctx, asset)
		if err != nil {
			logrus.Error(fmt.Sprintf("update trial asset error:%v", err))
			return domain.Code251122, err
		}
	}

	// 推送仓位变化
	_amqp, err := mqlib.New()
	if err != nil {
		logrus.Println("mqlib.New() err in sync asset db")
		return http.StatusOK, nil
	}

	go func() {
		assetMsg := message.AssetMsg{
			Currency: quote,
			Balance:  asset.CBalance(quote),
			Frozen:   asset.Frozen,
		}

		message.New(pos.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
			MsgType: message.AccountMsgTypeAsset,
			MsgData: assetMsg,
		})

		message.New(pos.UID, message.PosQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(pos)

		_ = _amqp.Close()

		// TODO 仓位变化异步存库
		// posSync := modelutil.NewDbPosSwapSync(pos, time.Now().UnixNano())
		// if err := persist.SyncPos(repo.rdb, pos.ContractCode, posSync); err != nil {
		// 	logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, posSync))
		// }

		// if len(trialList) > 0 && isTrial {
		// 	redis := repo.rdb
		// 	if err := redis.LPush(domain.SyncTrialAssetSwap, lo.ToAnySlice(trialList)...); err != nil {
		// 		logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,swapTrialAsset:%+v", err, trialList))
		// 	}
		// }
	}()

	return http.StatusOK, nil
}

func (repo *userRepository) AdjustMarginCfg(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error) {
	hasAgree := user.Service.HasAgree(p.UID)
	if hasAgree.HasAgree != 1 {
		return domain.Code251003, errors.New(domain.ErrMsg[domain.Code251003])
	}
	// lock taker & maker
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	base, quote := util.BaseQuote(p.ContractCode)
	// 获取用户资产
	userCache := swapcache.NewPosCache(swapcache.CacheParam{
		TradeCommon: repository.TradeCommon{
			Base: base, Quote: quote,
		},
	}, p.UID)

	asset, err := repo.cacheRepo.Load(ctx, p.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, err
	}
	if p.MarginMode == domain.MarginModeIsolated && asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		return domain.Code251023, err
	}
	// 不能有挂单(包含止赢止损单)
	if match.NewMatchData().SwapOrders(p.UID, p.ContractCode).Total > 0 {
		return domain.Code251020, errors.New(domain.ErrOrderOrPos.Error())
	}
	change := false

	// 当前币对不能有冻结资金
	if asset.GetFrozenByCode(p.ContractCode).Sign() > 0 {
		return domain.Code251020, fmt.Errorf("user holds unfinish order")
	}

	// 当前币对不能有体验金冻结资金
	if asset.GetFrozenByCode(p.ContractCode, true).Sign() > 0 {
		return domain.Code251020, fmt.Errorf("user trial holds unfinish order")
	}

	// 普通模式（锁仓模式） 不能有锁定仓位
	if asset.PositionMode == domain.HoldModeHedge {
		if !asset.LongPos.FrozenPos().IsZero() || !asset.ShortPos.FrozenPos().IsZero() {
			return domain.Code251020, fmt.Errorf("user holds unfinish order")
		}
	}

	leverage := asset.GetLeverage(p.ContractCode)
	// 修改币对全、逐仓逻辑
	{
		if p.MarginMode > 0 && leverage.MarginMode != p.MarginMode && p.MarginMode != domain.MarginModeNone {

			// 币对下不能有持仓
			crossPosList, ok := userCache.CrossList[p.ContractCode]
			if ok {
				for _, pos := range crossPosList {
					if !pos.Pos.IsZero() {
						return domain.Code251020, domain.ErrOrderOrPos
					}
				}
			}
			isolatedPosList, ok := userCache.IsolatedList[p.ContractCode]
			if ok {
				for _, pos := range isolatedPosList {
					if !pos.Pos.IsZero() {
						return domain.Code251020, domain.ErrOrderOrPos
					}
				}
			}

			leverage.MarginMode = p.MarginMode
			change = true
		}
	}

	if change {
		newLeverage := repo.adjustLeverage(ctx, asset, leverage)
		if err := repo.cacheRepo.UpdateLeverage(ctx, p.UID, newLeverage); err != nil {
			return domain.Code251022, err
		}
		repo.pushData(ctx, p.UID, p.ContractCode, leverage, leverage.MarginMode, asset) // 推送仓位模式 和杠杠倍数
	}
	p.MarginMode = leverage.MarginMode
	return http.StatusOK, nil
}

func (repo *userRepository) AdjustHoldMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error) {
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	userCache := swapcache.NewPosCache(swapcache.CacheParam{}, p.UID)
	userCache = userCache.LoadPos()
	for _, v := range userCache.CrossList {
		for _, pos := range v {
			if !pos.Pos.IsZero() {
				logrus.Infof("UID:%s ContractCode:%s,cross pos no zero", p.UID, p.ContractCode)
				return domain.HoldModeHavePos, fmt.Errorf("user holds unfinish order")
			}
		}
	}
	for _, v := range userCache.IsolatedList {
		for _, pos := range v {
			if !pos.Pos.IsZero() {
				logrus.Infof("UID:%s ContractCode:%s,Isolated pos no zero", p.UID, p.ContractCode)
				return domain.HoldModeHavePos, fmt.Errorf("user holds unfinish order")
			}
		}
	}
	asset, err := repo.cacheRepo.Load(ctx, p.UID, "")
	if err != nil {
		logrus.Errorf("UID:%s,query asset err: %s", p.UID, err.Error())
		return domain.Code252404, err
	}
	// 不能有冻结
	if asset.HasFrozen("") {
		logrus.Infof("UID:%s Isolated pos HasFrozen:%+v", p.UID, asset.Frozen)
		return domain.HoldModeHaveFrozen, fmt.Errorf("user have Frozen order")
	}

	err = userCache.UpdatePositionMode(p.PositionMode)
	if err != nil {
		logrus.Errorf("UID:%s,query asset err: %s", p.UID, err.Error())
		return domain.HoldModeRedisErr, err
	}

	// 推送配置变化
	_amqp, err := mqlib.New()
	if err == nil {
		message.New(asset.UID, message.AssetQueueIndex, _amqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
			MsgType: message.AccountMsgTypeHoldMode,
			MsgData: map[string]interface{}{
				"uid":          asset.UID,
				"positionMode": p.PositionMode,
			},
		})
	}
	_ = _amqp.Close()

	return http.StatusOK, nil
}

func (repo *userRepository) ChangeJoinMargin(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error) {
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	asset, err := repo.cacheRepo.Load(ctx, p.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, err
	}
	// 当前币对不能有冻结
	if asset.HasFrozen("") {
		return domain.ChangeJoinMarginHaveFrozen, fmt.Errorf("user have Frozen order")
	}
	if !asset.LongPos.FrozenPos().IsZero() || !asset.ShortPos.FrozenPos().IsZero() {
		return domain.ChangeJoinMarginHaveFrozen, fmt.Errorf("user holds unfinish order")
	}

	for _, v := range asset.CrossList {
		for _, pos := range v {
			if !pos.Pos.IsZero() {
				return domain.ChangeJoinMarginHavePos, fmt.Errorf("user holds unfinish order")
			}
		}
	}
	for _, v := range asset.IsolatedList {
		for _, pos := range v {
			if !pos.Pos.IsZero() {
				return domain.ChangeJoinMarginHavePos, fmt.Errorf("user holds unfinish order")
			}
		}
	}

	if p.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		for _, leverageInfo := range asset.Leverage {
			if leverageInfo.MarginMode == domain.MarginModeIsolated {
				return domain.Code252515, fmt.Errorf("user holds has isolate")
			}
		}
	}

	err = repo.cacheRepo.UpdateAssetMode(ctx, p.UID, p.AssetMode)
	if err != nil {
		return domain.ChangeJoinMarginRedisErr, err
	}

	// 推送配置变化
	_amqp, err := mqlib.New()
	if err != nil {
		logrus.Println("mqlib.New() err in sync asset db")
		return http.StatusOK, nil
	}

	go func() {
		message.New(p.UID, message.PosQueueIndex, _amqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
			MsgType: message.AccountMsgTypeJoinMode,
			MsgData: map[string]interface{}{
				"uid":          asset.UID,
				"contractCode": p.ContractCode,
				"assetMode":    p.AssetMode,
			},
		})
		_ = _amqp.Close()
	}()

	return http.StatusOK, nil
}

func (repo *userRepository) ChangeOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error) {
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	orderConfirmBytes, err := json.Marshal(p.OrderConfirm)
	if err != nil {
		logrus.Error(fmt.Sprintf("get order confirm err:%+v", err))
		return domain.ChangeOrderConfirmErr, errors.New(fmt.Sprintln("Marshal error:", err, fmt.Sprintf("%+v", p.OrderConfirm)))
	}
	redisCli := repo.rdb
	err = redisCli.HSet(ctx, cache.Prefix+cache.OrderConfirmSuffix, p.UID, string(orderConfirmBytes)).Err()
	if err != nil {
		logrus.Error(fmt.Sprintf("save order confirm err:%+v", err))
		return domain.ChangeOrderConfirmRedisErr, err
	}

	// 推送配置变化
	_amqp, err := mqlib.New()
	if err != nil {
		logrus.Println("mqlib.New() err in sync asset db")
		return http.StatusOK, nil
	}

	go func() {
		message.New(p.UID, message.PosQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(p.OrderConfirm)
		_ = _amqp.Close()
	}()

	return http.StatusOK, nil
}

func (repo *userRepository) ChangeOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error) {
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+p.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.Code251101, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	orderConfigBytes, err := json.Marshal(p.OrderConfig)
	if err != nil {
		logrus.Error(fmt.Sprintf("get order config err:%+v", err))
		return domain.ChangeOrderConfigErr, errors.New(fmt.Sprintln("Marshal error:", err, fmt.Sprintf("%+v", p.OrderConfig)))
	}
	redisCli := repo.rdb
	err = redisCli.HSet(ctx, cache.Prefix+cache.OrderConfigSuffix, p.UID, string(orderConfigBytes)).Err()
	if err != nil {
		logrus.Error(fmt.Sprintf("save order config err:%+v", err))
		return domain.ChangeOrderConfigRedisErr, err
	}
	// 推送配置变化
	_amqp, err := mqlib.New()
	if err != nil {
		logrus.Println("mqlib.New() err in sync asset db")
		return http.StatusOK, nil
	}

	go func() {
		message.New(p.UID, message.PosQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(p.OrderConfig)
		_ = _amqp.Close()
	}()

	return http.StatusOK, nil
}

func (repo *userRepository) LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error) {
	// 获取用户的持仓模式
	reply := repository.JoinMarginRes{
		UID: param.UID,
	}
	redisCli := repo.rdb
	assetMode, err := redisCli.HGet(ctx, cache.Prefix+param.UID, cache.JoinMarginSuffix).Result()
	if err != nil && err != redis.Nil {
		logrus.Error(fmt.Sprintf("get join margin err:%+v", err))
		return reply, err
	}
	if len(assetMode) == 0 {
		reply.AssetMode = int(futuresassetpb.AssetMode_ASSET_MODE_SINGLE)
		joinMarginStr := strconv.Itoa(reply.AssetMode)
		if err = repo.rdb.HSet(ctx, cache.Prefix+param.UID, cache.JoinMarginSuffix, joinMarginStr).Err(); err != nil {
			return reply, err
		}
	} else {
		reply.AssetMode, _ = strconv.Atoi(assetMode)
	}

	return reply, nil
}

func (repo *userRepository) GetOrderConfirm(ctx context.Context, param *repository.CommonParam) (repository.OrderConfirmRes, error) {
	orderConfirmData := repository.OrderConfirm{
		LimitConfirm:    1,
		MarketConfirm:   1,
		TriggerConfirm:  1,
		PostOnlyConfirm: 1,
		BackhandConfirm: 1,
	}
	// 获取用户的持仓模式
	reply := repository.OrderConfirmRes{
		UID:          param.UID,
		OrderConfirm: orderConfirmData,
	}
	redisCli := repo.rdb
	orderConfirmStr, err := redisCli.HGet(ctx, cache.Prefix+cache.OrderConfirmSuffix, param.UID).Result()
	if err != nil && err != redis.Nil {
		logrus.Error(fmt.Sprintf("get order confirm err: %+v", err))
		return reply, err
	}
	if len(orderConfirmStr) > 0 {
		orderConfirmList := strings.Split(orderConfirmStr, ",")
		if len(orderConfirmList) > 0 {
			err = json.Unmarshal([]byte(orderConfirmStr), &orderConfirmData)
			if err != nil {
				logrus.Error(fmt.Sprintf("get order confirm Unmarshal err: %+v, %s", err, orderConfirmStr))
				return reply, err
			}
		}
	}
	reply.OrderConfirm = orderConfirmData

	return reply, nil
}

func (repo *userRepository) GetOrderConfig(ctx context.Context, param *repository.CommonParam) (repository.OrderConfigRes, error) {
	orderConfigData := repository.OrderConfig{
		RebornCard: 1,
		Trial:      1,
	}
	// 获取用户的持仓模式
	reply := repository.OrderConfigRes{
		UID:         param.UID,
		OrderConfig: orderConfigData,
	}
	redisCli := repo.rdb
	orderConfigStr, err := redisCli.HGet(ctx, cache.Prefix+cache.OrderConfigSuffix, param.UID).Result()
	if err != nil && err != redis.Nil {
		logrus.Error(fmt.Sprintf("get order config err: %+v", err))
		return reply, err
	}

	if len(orderConfigStr) > 0 {
		orderConfigList := strings.Split(orderConfigStr, ",")
		if len(orderConfigList) > 0 {
			err = json.Unmarshal([]byte(orderConfigStr), &orderConfigData)
			if err != nil {
				logrus.Error(fmt.Sprintf("get order config Unmarshal err: %+v, %s", err, orderConfigStr))
				return reply, err
			}
		}
	}
	reply.OrderConfig = orderConfigData

	return reply, nil
}

func (repo *userRepository) GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset) {
	var data []repository.Asset
	asset, err := repo.cacheRepo.Load(ctx, param.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, data
	}
	totalBalance := asset.CBalance(param.Currency)
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		totalBalance, err = repo.assetRepo.TotalJoinBalance(ctx, asset)
		if err != nil {
			logrus.Errorf("GetUserAsset TotalJoinBalance err: %s", err.Error())
			return domain.Code252407, data
		}
	}
	parAsset := repository.Asset{
		Currency:   param.Currency,
		TotalTotal: totalBalance,
	}

	data = append(data, parAsset)
	return http.StatusOK, data
}

func (repo *userRepository) SwapInit(ctx context.Context, param *repository.SwapInit) domain.Code {
	contractSettingMap, err := setting.Service.GetAllPairSettingInfo()
	if err != nil {
		logrus.Error("makeFundRates GetAllPairSettingInfo err.")
		return domain.Code252404
	}
	mutex := repo.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if mutex.Lock() != nil {
		return domain.Code251101
	}
	defer mutex.Unlock()

	asset, err := repo.cacheRepo.Load(ctx, param.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404
	}

	// 开通过不能重置资产
	if len(asset.Leverage) > 0 {
		// if asset.AssetMode > 0 && asset.PositionMode > 0 {
		hash := domain.AssetPrefix.Key(domain.RedisAllUser)
		repo.rdb.HSet(ctx, hash, param.UID, "{}")
		return http.StatusOK
	}

	for contractCode, contractSetting := range contractSettingMap {
		asset.Leverage = append(asset.Leverage, &repository.Leverage{
			ContractCode: strings.ToUpper(contractCode),
			MarginMode:   domain.MarginModeCross,
			Leverage:     contractSetting.DefaultLeverage,
			LLeverage:    contractSetting.DefaultLeverage,
			SLeverage:    contractSetting.DefaultLeverage,
			BLeverage:    contractSetting.DefaultLeverage,
		})
	}

	err = repo.cacheRepo.InitUser(ctx, param.UID, asset.Leverage)
	if err != nil {
		logrus.Error(fmt.Sprintf("init base info err:%+v, uid:%+v, leverage:%+v", err, param.UID, asset.Leverage))
	}

	hash := domain.AssetPrefix.Key(domain.RedisAllUser)
	err = repo.rdb.HSet(ctx, hash, param.UID, "{}").Err()
	if err != nil {
		logrus.Error(fmt.Sprintf("init users hash:%+v,err:%+v, param:%+v", hash, err, param))
	}

	// 初始化盈亏记录
	profitTime := util.OneDayBeginAndEndTimeStamp(time.Now())
	profitTimeStr := strconv.FormatInt(profitTime, 10)
	_, err = repo.rdb.SAdd(ctx, setting.GetProfitDateKey(), profitTimeStr).Result()
	if err != nil {
		logrus.Error(fmt.Sprintf("SAdd ProfitDate:%+v, err:%+v", profitTimeStr, err))
	}

	profitHashKey := setting.GetProfitDataKeyByTime(profitTimeStr)
	for _, currency := range domain.CurrencyList {
		profitLossData := entity.ProfitLoss{
			UID:         param.UID,
			Currency:    currency,
			NetIn:       decimal.Zero,
			ProfitLoss:  decimal.Zero,
			OperateTime: profitTime,
		}
		profitLossData.CreateTime = time.Now().UnixNano()
		hashKey := profitLossData.UID + util.ProfitLossBassKey(currency)
		profitData, _ := json.Marshal(&profitLossData)
		err = repo.rdb.HSet(ctx, profitHashKey, hashKey, string(profitData)).Err()
		if err != nil {
			logrus.Error(fmt.Sprintf("create profit redis err.hash:%s, key:%s, data:%+v", profitHashKey, hashKey, profitLossData))
		}
	}

	go message.New(param.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAgree(message.SwapAccount).Push(message.AccountMsg{
		MsgType: message.AccountMsgTypeAgree,
		MsgData: map[string]interface{}{},
	})

	return http.StatusOK
}

func (repo *userRepository) UserHoldMode(ctx context.Context, param *repository.CommonParam) (repository.HoldModeRes, error) {
	// 获取用户的持仓模式
	reply := repository.HoldModeRes{
		UID: param.UID,
	}
	redisCli := repo.rdb
	positionMode, err := redisCli.HGet(ctx, cache.Prefix+param.UID, cache.HoldModeSuffix).Result()
	if err != nil && err != redis.Nil {
		logrus.Error(1, param.UID, "[UserHoldMode] redis.HGet err:%s", err)
		return reply, err
	}
	if len(positionMode) == 0 {
		reply.PositionMode = domain.HoldModeHedge

		// 当查询不到用户的持仓模式时，设置默认值
		err = redisCli.HSet(ctx, cache.Prefix+param.UID, cache.HoldModeSuffix, strconv.Itoa(domain.HoldModeHedge)).Err()
		if err != nil {
			logrus.Error(1, param.UID, "[UserHoldMode] redis.HSet err:%s", err)
		}
	} else {
		reply.PositionMode, _ = strconv.Atoi(positionMode)
	}

	return reply, nil
}

func (repo *userRepository) UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error) {
	// 获取用户配置的合约币对杠杆倍数
	reply := make([]*repository.Leverage, 0)
	redisCli := repo.rdb
	tmpLeverage := make([]repository.Leverage, 0)
	leverageStr, err := redisCli.HGet(ctx, cache.Prefix+param.UID, cache.LeverageSuffix).Result()
	if err != nil {
		logrus.Error(1, param.UID, "[UserLeverage] redis.HGet err:%s", err)
		return reply, err
	}
	if err := json.Unmarshal([]byte(leverageStr), &tmpLeverage); err != nil {
		logrus.Error(1, param.UID, "[UserLeverage] Unmarshal err:%s", err)
		return reply, err
	}

	contractSettingMap, err := setting.Service.GetAllPairSettingInfo()
	if err != nil {
		logrus.Error("get all contract setting err.")
		return reply, err
	}

	isUpdate := false
	for contractCode, contractSetting := range contractSettingMap {
		existed := false
		for _, l := range tmpLeverage {
			if strings.EqualFold(l.ContractCode, strings.ToUpper(contractCode)) {
				reply = append(reply, &repository.Leverage{
					ContractCode: l.ContractCode,
					MarginMode:   l.MarginMode,
					Leverage:     l.Leverage,
					LLeverage:    l.LLeverage,
					SLeverage:    l.SLeverage,
					BLeverage:    l.BLeverage,
				})
				existed = true
				break
			}
		}
		if !existed {
			reply = append(reply, &repository.Leverage{
				ContractCode: strings.ToUpper(contractCode),
				Leverage:     contractSetting.DefaultLeverage,
				MarginMode:   domain.MarginModeCross,
				LLeverage:    contractSetting.DefaultLeverage,
				SLeverage:    contractSetting.DefaultLeverage,
				BLeverage:    contractSetting.DefaultLeverage,
			})
			isUpdate = true
		}
	}
	if isUpdate {
		bLeverage, _ := json.Marshal(reply)
		err = redisCli.HSet(ctx, cache.Prefix+param.UID, cache.LeverageSuffix, string(bLeverage)).Err()
		if err != nil {
			logrus.Error(fmt.Sprintf("add contract init leverage err:%+v, leverageStr:%+v", err, string(bLeverage)))
		}
	}

	return reply, nil
}

func (repo *userRepository) adjustLeverage(ctx context.Context, asset *repository.AssetSwap, leverage repository.Leverage) []*repository.Leverage {
	existed := false
	for i, l := range asset.Leverage {
		if strings.EqualFold(l.ContractCode, leverage.ContractCode) {
			asset.Leverage[i] = &leverage
			existed = true
			break
		}
	}
	if !existed {
		asset.Leverage = append(asset.Leverage, &leverage)
	}

	return asset.Leverage
}

func (repo *userRepository) adjustCrossLeverage(ctx context.Context, p *repository.LeverageMarginAdAdjust, asset *repository.AssetSwap,
	leverage *repository.Leverage,
) (domain.Code, error) {
	base, quote := util.BaseQuote(p.ContractCode)

	if p.Leverage > leverage.Leverage {
		markPrice := repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)
		posValue := asset.LongPos.CalcPosValue(markPrice).Add(asset.ShortPos.CalcPosValue(markPrice)).Add(asset.BothPos.CalcPosValue(markPrice))
		marginLevel, _, _ := setting.FetchMarginLevel(base, quote, posValue)
		if p.Leverage > marginLevel.Lever {
			return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
		}
	}

	for contractCode, pos := range asset.CrossList {
		if contractCode == p.ContractCode {
			for posIndex := 0; posIndex < len(pos); posIndex++ {
				pos[posIndex].Leverage = p.Leverage
			}
			break
		}
	}
	// 按调整之后的杠杆倍数重新算一下可用， 如果小于0 则提示不能调
	available, err := repo.assetRepo.GetAvailableBase(ctx, asset, futuresassetpb.MarginMode(p.MarginMode), quote)
	if err != nil {
		logrus.Println("adjustCrossLeverage GetAvailableBase error:", err)
		return domain.Code252407, err
	}
	if available.IsNegative() {
		return domain.Code251002, fmt.Errorf(domain.ErrMsg[domain.Code251002])
	}

	return http.StatusOK, nil
}

func (repo *userRepository) adjustCrossLeverageCfg(ctx context.Context, p *repository.LeverageAdjust, asset *repository.AssetSwap,
	leverage *repository.Leverage,
) (domain.Code, error) {
	base, quote := util.BaseQuote(p.ContractCode)

	if p.Leverage > leverage.Leverage {
		markPrice := repo.priceRepo.GetMarkPrice(ctx, p.ContractCode)
		posValue := asset.LongPos.CalcPosValue(markPrice).Add(asset.ShortPos.CalcPosValue(markPrice)).Add(asset.BothPos.CalcPosValue(markPrice))
		marginLevel, _, _ := setting.FetchMarginLevel(base, quote, posValue)
		if p.Leverage > marginLevel.Lever {
			return domain.Code251006, fmt.Errorf(domain.ErrMsg[domain.Code251006])
		}
	}

	for contractCode, pos := range asset.CrossList {
		if contractCode == p.ContractCode {
			for posIndex := 0; posIndex < len(pos); posIndex++ {
				pos[posIndex].Leverage = p.Leverage
			}
			break
		}
	}
	// 按调整之后的杠杆倍数重新算一下可用， 如果小于0 则提示不能调
	available, err := repo.assetRepo.GetAvailableBase(ctx, asset, futuresassetpb.MarginMode(leverage.MarginMode), quote)
	if err != nil {
		logrus.Println("adjustCrossLeverage GetAvailableBase error:", err)
		return domain.Code252407, err
	}
	if available.IsNegative() {
		return domain.Code251002, fmt.Errorf(domain.ErrMsg[domain.Code251002])
	}

	return http.StatusOK, nil
}

// UpdatePosLeverage 调整杠杆倍数之后修改仓位
func (repo *userRepository) UpdatePosLeverage(ctx context.Context, asset *repository.AssetSwap, l int) {
	asset.SetPosLeverAge(l)
	if !asset.LongPos.Pos.IsZero() {
		err := repo.cacheRepo.UpdateLongPos(ctx, asset.UID, asset)
		if err != nil {
			logrus.Error(fmt.Sprintf("update long pos err:%+v,userAsset:%+v", err, *asset))
		}
	}
	if !asset.ShortPos.Pos.IsZero() {
		err := repo.cacheRepo.UpdateShortPos(ctx, asset.UID, asset)
		if err != nil {
			logrus.Error(fmt.Sprintf("update short pos err:%+v,userAsset:%+v", err, *asset))
		}
	}
	if !asset.BothPos.Pos.IsZero() {
		err := repo.cacheRepo.UpdateBothPos(ctx, asset.UID, asset)
		if err != nil {
			logrus.Error(fmt.Sprintf("update both pos err:%+v,userAsset:%+v", err, *asset))
		}
	}
}

func (repo *userRepository) pushData(ctx context.Context, uid, code string, leverage repository.Leverage, mode domain.MarginMode, asset *repository.AssetSwap) {
	leveragePush := message.LeveragePushData{
		UID:          uid,
		Leverage:     leverage.Leverage,
		LLeverage:    leverage.LLeverage,
		SLeverage:    leverage.SLeverage,
		BLeverage:    leverage.BLeverage,
		MarginMode:   int(mode),
		ContractCode: strings.ToUpper(code),
	}
	_amqp, err := mqlib.New()
	if err == nil {
		message.New(leveragePush.UID, message.AssetQueueIndex, _amqp).TopicLeverage(message.SwapAccount).Push(leveragePush)
		if asset.LongPos.Pos.GreaterThan(decimal.Zero) {
			message.New(leveragePush.UID, message.AssetQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(asset.LongPos)
		}
		if asset.ShortPos.Pos.GreaterThan(decimal.Zero) {
			message.New(leveragePush.UID, message.AssetQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(asset.ShortPos)
		}
		if !asset.BothPos.Pos.IsZero() {
			message.New(leveragePush.UID, message.AssetQueueIndex, _amqp).TopicPos(message.SwapAccount).Push(asset.BothPos)
		}
	}
	_ = _amqp.Close()
}

func (repo *userRepository) GetUserOpenCloseTimes(ctx context.Context, req *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error) {
	closeData := new(entity.UserWinRate)
	err := repo.db.Table(closeData.TableName()).Where("user_id = ?", req.UID).Find(closeData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.Errorf("find user close times error, err: %v", err)
		return nil, err
	}

	openData := new(swap.UserStatistics)
	err = repo.db.Table(openData.TableName()).Where("user_id = ?", req.UID).Find(openData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logrus.Errorf("find user open times error, err: %v", err)
		return nil, err
	}

	return &repository.OpenCloseTimesRes{
		UID:        req.UID,
		OpenTimes:  openData.HoldPosTimes,
		CloseTimes: int(closeData.Times),
	}, nil
}

func (repo *userRepository) GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error) {
	reply := make([]repository.UserStatisticsReply, 0)

	// // 从内存获取统计数据
	// allStatistics := memorycache.GetAllStatistics()
	// timeNow := time.Now().UnixNano()
	// for _, dbWinRate := range dbWinRateList {
	// 	// 获取用户胜率数据
	// 	tmp := repository.UserStatisticsReply{
	// 		UID:              dbWinRate.UID,
	// 		TotalCloseMargin: dbWinRate.TotalCloseMargin,
	// 	}
	// 	if dbWinRate.Times != 0 {
	// 		tmp.WinRate = decimal.NewFromFloat(float64(dbWinRate.WinTimes) / float64(dbWinRate.Times)).Truncate(domain.RatePrecision)
	// 	}
	// 	// 获取用户合约资产
	// 	asset, err := repo.cacheRepo.Load(ctx, dbWinRate.UID, "")
	// 	if err != nil {
	// 		continue
	// 	}
	// 	totalBalance := decimal.Zero
	// 	for _, currency := range domain.CurrencyList {
	// 		if currency == domain.CurrencyUSDT {
	// 			totalBalance.Add(asset.CBalance(currency))
	// 		} else {
	// 			uRate := repo.priceRepo.SpotURate(ctx, currency)
	// 			totalBalance.Add(asset.CBalance(currency).Mul(uRate))
	// 		}
	// 	}
	// 	tmp.Balance = totalBalance
	// 	// 	获取用户统计数据
	// 	for key, statistics := range allStatistics {
	// 		if strings.EqualFold(util.StatisticsId(tmp.UID, param.AccountType), key) {
	// 			tmp.TxDays = int((timeNow - statistics.FirstOpenTime) / (86400 * 1e9))
	// 			tmp.HoldPosValue = statistics.HoldPosValue
	// 			tmp.HoldPosTimes = statistics.HoldPosTimes
	// 		}
	// 	}
	// 	reply = append(reply, tmp)
	// }

	return reply, nil
}
