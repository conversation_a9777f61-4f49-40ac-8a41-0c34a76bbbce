package repository

import (
	"context"
	"encoding/json"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type OptionRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	MDB *mongo.Database      `name:"mongodb"`
	RDB *redis.ClusterClient `name:"redis-cluster"`
}

type optionRepository struct {
	db  *gorm.DB
	mdb *mongo.Database
	rdb *redis.ClusterClient
}

func NewOptionRepository(param OptionRepositoryParam) repository.OptionRepository {
	return &optionRepository{
		db:  param.DB,
		mdb: param.MDB,
		rdb: param.RDB,
	}
}

// OptionPnlRecord 期权盈亏记录
func (repo *optionRepository) OptionPnlRecord(ctx context.Context, param *repository.ReqPLRecord) (repository.OptionPnlReply, error) {
	records, err := repo.optionPnlTrade(param)
	if err != nil {
		return repository.OptionPnlReply{}, errors.Wrap(err, "get profit loss data")
	}

	data := repository.OptionPnlReply{}
	recordDataMap := map[int64]int{}
	for _, record := range records {
		if dayIndex, ok := recordDataMap[record.OperateTime]; ok {
			data.ProfitLossRecords[dayIndex].NetIn = data.ProfitLossRecords[dayIndex].NetIn.Add(record.NetIn)
			data.ProfitLossRecords[dayIndex].TotalFee = data.ProfitLossRecords[dayIndex].TotalFee.Add(record.TotalFee)
			data.ProfitLossRecords[dayIndex].DayProfitLoss = data.ProfitLossRecords[dayIndex].DayProfitLoss.Add(record.ProfitLoss)
		} else {
			data.ProfitLossRecords = append(data.ProfitLossRecords, repository.OptionPnlItem{
				DayTime:       record.OperateTime,
				NetIn:         record.NetIn,
				TotalFee:      record.TotalFee,
				ProfitPremium: record.ProfitPremium,
				ProfitCount:   record.ProfitCount,
				LossCount:     record.LossCount,
				DayProfitLoss: record.ProfitLoss,
				DayProfit:     record.Profit,
				DayLoss:       record.Loss,
			})
			recordDataMap[record.OperateTime] = len(data.ProfitLossRecords) - 1
		}
	}
	if len(data.ProfitLossRecords) == 0 {
		return data, nil
	}
	data.DaysAnalysis.Days = len(data.ProfitLossRecords)

	// 当前余额 - 累计盈亏 = 开始日账户余额 + 净转入
	// 累计盈亏率 = 累计盈亏 / （当前余额 - 累计盈亏） *100%
	// 开始统计分析
	totalProfitLoss := decimal.Zero
	for index := len(data.ProfitLossRecords) - 1; index >= 0; index-- {
		totalProfitLoss = totalProfitLoss.Add(data.ProfitLossRecords[index].DayProfitLoss)
		// 累计总盈亏
		data.ProfitLossRecords[index].TotalProfitLoss = totalProfitLoss
		// 当日净盈亏 = 当日累计盈利 - 当日所有期权的权利金 (当日累计亏损+当日盈利订单的权利金总和) - 当日所有手续费总和
		dayNetProfit := data.ProfitLossRecords[index].DayProfitLoss.Sub(data.ProfitLossRecords[index].TotalFee).Sub(data.ProfitLossRecords[index].ProfitPremium)
		// 按时间筛选后的累计净盈亏
		data.DaysAnalysis.NetProfitLoss = data.DaysAnalysis.NetProfitLoss.Add(dayNetProfit)
		data.DaysAnalysis.ProfitCount += data.ProfitLossRecords[index].ProfitCount
		data.DaysAnalysis.LossCount += data.ProfitLossRecords[index].LossCount
		data.DaysAnalysis.DaysCumulativeLoss = data.DaysAnalysis.DaysCumulativeLoss.Add(data.ProfitLossRecords[index].DayLoss)
		data.DaysAnalysis.DaysCumulativeProfit = data.DaysAnalysis.DaysCumulativeProfit.Add(data.ProfitLossRecords[index].DayProfit)
		data.DaysAnalysis.LossDays++
		data.DaysAnalysis.ProfitDays++
	}
	if data.DaysAnalysis.ProfitDays != 0 {
		data.DaysAnalysis.AverageProfit = data.DaysAnalysis.DaysCumulativeProfit.Div(decimal.NewFromInt(data.DaysAnalysis.ProfitDays))
	}
	if data.DaysAnalysis.LossDays != 0 {
		data.DaysAnalysis.AverageLoss = data.DaysAnalysis.DaysCumulativeLoss.Div(decimal.NewFromInt(data.DaysAnalysis.LossDays))
	}

	data.TotalProfitLoss = repo.optionTotalPnl(param)

	return data, nil
}

func (repo *optionRepository) optionPnlTrade(param *repository.ReqPLRecord) ([]entity.OptionProfitLoss, error) {
	var records []entity.OptionProfitLoss
	sql := repo.db.Table(new(entity.OptionProfitLoss).TableName()).Where("user_id = ?", param.UID)
	if param.StartTime != 0 {
		sql = sql.Where("operate_time >= ?", param.StartTime)
	}
	if param.EndTime != 0 {
		sql = sql.Where("operate_time <= ?", param.EndTime)
	}

	sql = sql.Table(new(entity.OptionProfitLoss).TableName()).Where("user_id = ?", param.UID)
	if param.StartTime != 0 {
		sql = sql.Where("operate_time >= ?", param.StartTime)
	}
	if param.EndTime != 0 {
		sql = sql.Where("operate_time <= ?", param.EndTime)
	}
	sql = sql.Order("operate_time DESC")
	err := sql.Find(&records).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return records, nil
	}

	return records, err
}

// optionTotalPnl 期权总盈亏
func (repo *optionRepository) optionTotalPnl(param *repository.ReqPLRecord) (totalProfit decimal.Decimal) {
	var profitList []entity.OptionTotalProfit

	sql := repo.db.Table(new(entity.OptionTotalProfit).TableName()).Where("user_id = ?", param.UID)
	err := sql.Find(&profitList).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		for _, profit := range profitList {
			totalProfit = totalProfit.Add(profit.TotalProfit).Sub(profit.TotalLoss)
		}

		return
	}

	return
}

// GetAllDemoUsers implements repository.OptionRepository.
func (repo *optionRepository) GetAllDemoUsers(ctx context.Context) ([]string, error) {
	vals, err := repo.rdb.HKeys(ctx, domain.OptionDemoBalancePrefix).Result()
	if err != nil {
		return vals, err
	}

	return vals, nil
}

// GetDemoAsset implements repository.OptionRepository.
func (repo *optionRepository) GetDemoAsset(ctx context.Context, uid string) (decimal.Decimal, error) {
	balanceStr, err := repo.rdb.HGet(ctx, domain.OptionDemoBalancePrefix, uid).Result()
	if err == redis.Nil {
		balance := decimal.NewFromInt(100000)
		_ = repo.rdb.HSet(ctx, domain.OptionDemoBalancePrefix, uid, balance.String())
		return balance, nil
	}
	if err != nil {
		return decimal.Zero, err
	}

	balance, err := decimal.NewFromString(balanceStr)
	if err != nil {
		return decimal.Zero, err
	}

	return balance, nil
}

// UpdateDemoAsset implements repository.OptionRepository.
func (repo *optionRepository) UpdateDemoAsset(ctx context.Context, uid string, balance decimal.Decimal) error {
	err := repo.rdb.HSet(ctx, domain.OptionDemoBalancePrefix, uid, balance.String()).Err()
	if err != nil {
		return err
	}

	return nil
}

// OptionHashKey set option redis key
func (repo *optionRepository) OptionHashKey(uid string, optionType int) string {
	if optionType == domain.OptionTypeFirm {
		return domain.OptionPrefix + uid
	}

	if optionType == domain.OptionTypeDemo {
		return domain.OptionDemoPrefix + uid
	}

	return ""
}

// LoadUserOption implements repository.OptionRepository.
func (repo *optionRepository) LoadUserOption(ctx context.Context, uid string, optionType int) ([]repository.Option, error) {
	userOption := make([]repository.Option, 0)
	optionStr, err := repo.rdb.HGet(ctx, repo.OptionHashKey(uid, optionType), uid).Result()
	if err != nil {
		return userOption, err
	}

	err = json.Unmarshal([]byte(optionStr), &userOption)
	if err != nil {
		return userOption, err
	}

	return userOption, nil
}

// LoadUserOptions implements repository.OptionRepository.
func (repo *optionRepository) LoadUserOptions(ctx context.Context, uid string, optionType int, optionKeys []string) (map[string][]repository.Option, error) {
	options := make(map[string][]repository.Option)
	optionMap, err := repo.rdb.HMGet(ctx, repo.OptionHashKey(uid, optionType), optionKeys...).Result()
	if err != nil {
		return options, err
	}
	if len(optionMap) == 0 {
		return options, nil
	}

	result := make(map[string]string)
	for index, value := range optionMap {
		valueStr, ok := value.(string)
		if ok {
			result[optionKeys[index]] = valueStr
		}
	}

	for k, o := range result {
		tmp := make([]repository.Option, 0)
		err = json.Unmarshal([]byte(o), &tmp)
		if err != nil {
			continue
		}
		options[k] = tmp
	}

	return options, nil
}

// RemoveOption implements repository.OptionRepository.
func (repo *optionRepository) RemoveOption(ctx context.Context, uid string, optionType int) error {
	err := repo.rdb.HDel(ctx, repo.OptionHashKey(uid, optionType), uid).Err()
	if err != nil {
		return err
	}

	return nil
}

// UpdateMultiOption implements repository.OptionRepository.
func (repo *optionRepository) UpdateMultiOption(ctx context.Context, uid string, optionType int, options map[string]repository.Option) error {
	fields := map[string]interface{}{}

	optionKeys := make([]string, 0, len(options))
	for subKey := range options {
		optionKeys = append(optionKeys, subKey)
	}

	oldOptions, err := repo.LoadUserOptions(ctx, uid, optionType, optionKeys)
	if err != nil {
		return err
	}

	for k, newOption := range options {
		old, ok := oldOptions[k]
		if !ok { // 如果旧数据中不存在, 新增
			old = []repository.Option{newOption}
		} else { // 旧数据中存在, 更新旧数据
			old = append(old, newOption)
		}
		itemObj, _ := json.Marshal(old)
		fields[k] = itemObj
	}

	return repo.rdb.HMSet(ctx, repo.OptionHashKey(uid, optionType), fields).Err()
}

// UpdateOption implements repository.OptionRepository.
func (repo *optionRepository) UpdateOption(ctx context.Context, uid string, optionType int, optionKey string, options []repository.Option) error {
	itemObj, _ := json.Marshal(options)
	err := repo.rdb.HSet(ctx, repo.OptionHashKey(uid, optionType), optionKey, string(itemObj)).Err()
	if err != nil {
		return err
	}

	return nil
}
