package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/libs/pager"
	"futures-asset/util/modelutil"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type TrialRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	MDB *mongo.Database      `name:"mongodb"`
	RDB *redis.ClusterClient `name:"redis-cluster"`
	RS  *redsync.Redsync     `name:"redsync"`

	FormulaRepository  repository.FormulaRepository
	PriceRepository    repository.PriceRepository
	AssetRepository    repository.AssetRepository
	BurstRepository    repository.BurstRepository
	CacheRepository    repository.CacheRepository
	PositionRepository repository.PositionRepository
}

type trialRepository struct {
	db  *gorm.DB
	mdb *mongo.Database
	rdb *redis.ClusterClient
	rs  *redsync.Redsync

	formulaRepo  repository.FormulaRepository
	priceRepo    repository.PriceRepository
	assetRepo    repository.AssetRepository
	burstRepo    repository.BurstRepository
	cacheRepo    repository.CacheRepository
	positionRepo repository.PositionRepository
}

func NewTrialRepository(param TrialRepositoryParam) repository.TrialRepository {
	return &trialRepository{
		db:  param.DB,
		mdb: param.MDB,
		rdb: param.RDB,
		rs:  param.RS,

		formulaRepo:  param.FormulaRepository,
		priceRepo:    param.PriceRepository,
		assetRepo:    param.AssetRepository,
		burstRepo:    param.BurstRepository,
		cacheRepo:    param.CacheRepository,
		positionRepo: param.PositionRepository,
	}
}

// UpdateTrialPos 更新体验金仓位
func (repo *trialRepository) UpdateTrialPos(ctx context.Context, symbol string, asset *repository.AssetSwap) error {
	longStr, _ := json.Marshal(asset.TrialLongPos)
	shortStr, _ := json.Marshal(asset.TrialShortPos)
	bothStr, _ := json.Marshal(asset.TrialBothPos)
	fields := map[string]interface{}{
		domain.TrialAssetPrefix.Key(symbol, domain.LongPosSuffix):  longStr,
		domain.TrialAssetPrefix.Key(symbol, domain.ShortPosSuffix): shortStr,
		domain.TrialAssetPrefix.Key(symbol, domain.BothPosSuffix):  bothStr,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}

// UpdateTrialLongPos 更新体验金多仓仓位
func (repo *trialRepository) UpdateTrialLongPos(ctx context.Context, symbol string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.TrialLongPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	trialBalance, _ := json.Marshal(asset.TrialBalance)
	trialConsume, _ := json.Marshal(asset.TrialConsume)
	trialLoss, _ := json.Marshal(asset.TrialLoss)
	trialRecovery, _ := json.Marshal(asset.TrialRecovery)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetail, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		domain.TrialAssetPrefix.Key(symbol, domain.LongPosSuffix): posStr,
		domain.Balance.String():                                   balance,
		domain.Frozen.String():                                    frozen,
		domain.TrialBalance.String():                              trialBalance,
		domain.TrialConsume.String():                              trialConsume,
		domain.TrialLoss.String():                                 trialLoss,
		domain.TrialRecovery.String():                             trialRecovery,
		domain.TrialDetail.String():                               trialDetail,
	}

	return repo.rdb.HMSet(ctx, domain.TrialAssetPrefix.Key(symbol), fields).Err()
}

// UpdateTrialShortPos 更新体验金空仓仓位
func (repo *trialRepository) UpdateTrialShortPos(ctx context.Context, symbol string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.TrialShortPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	trialBalance, _ := json.Marshal(asset.TrialBalance)
	trialConsume, _ := json.Marshal(asset.TrialConsume)
	trialLoss, _ := json.Marshal(asset.TrialLoss)
	trialRecovery, _ := json.Marshal(asset.TrialRecovery)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetail, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		domain.TrialAssetPrefix.Key(symbol, domain.ShortPosSuffix): posStr,
		domain.Balance.String():       balance,
		domain.Frozen.String():        frozen,
		domain.TrialBalance.String():  trialBalance,
		domain.TrialConsume.String():  trialConsume,
		domain.TrialLoss.String():     trialLoss,
		domain.TrialRecovery.String(): trialRecovery,
		domain.TrialDetail.String():   trialDetail,
	}

	return repo.rdb.HMSet(ctx, domain.TrialAssetPrefix.Key(symbol), fields).Err()
}

// UpdateTrialBothPos 更新体验金双向仓位
func (repo *trialRepository) UpdateTrialBothPos(ctx context.Context, symbol string, asset *repository.AssetSwap) error {
	posStr, _ := json.Marshal(asset.TrialBothPos)
	balance, _ := json.Marshal(asset.Balance)
	frozen, _ := json.Marshal(asset.Frozen)
	trialBalance, _ := json.Marshal(asset.TrialBalance)
	trialConsume, _ := json.Marshal(asset.TrialConsume)
	trialLoss, _ := json.Marshal(asset.TrialLoss)
	trialRecovery, _ := json.Marshal(asset.TrialRecovery)
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}
	trialDetail, _ := json.Marshal(asset.TrialDetail)

	fields := map[string]interface{}{
		domain.TrialAssetPrefix.Key(symbol, domain.BothPosSuffix): posStr,
		domain.Balance.String():                                   balance,
		domain.Frozen.String():                                    frozen,
		domain.TrialBalance.String():                              trialBalance,
		domain.TrialConsume.String():                              trialConsume,
		domain.TrialLoss.String():                                 trialLoss,
		domain.TrialRecovery.String():                             trialRecovery,
		domain.TrialDetail.String():                               trialDetail,
	}

	return repo.rdb.HMSet(ctx, domain.TrialAssetPrefix.Key(symbol), fields).Err()
}

// GetNoInvalidTrial implements repository.TrialRepository.
func (repo *trialRepository) GetNoInvalidTrial(ctx context.Context, req *repository.GetNoInvalidTrialReq) ([]*entity.TrialAsset, error) {
	data := []*entity.TrialAsset{}
	userAsset, err := repo.cacheRepo.Load(ctx, req.UID, req.Currency)
	if err != nil {
		return nil, errors.Wrap(err, "get trial asset list")
	}

	for _, trial := range userAsset.TrialDetail {
		if trial.RecycleAmount.IsZero() {
			continue
		}
		if trial.TrialAssetStatus == domain.TrialAssetStatusNoEffect {
			continue
		}
		if req.ActivityId == 0 || trial.ActivityId == req.ActivityId {
			data = append(data, trial)
		}
	}
	if len(data) == 0 {
		data = make([]*entity.TrialAsset, 0)
		// return domain.ErrTrialAssetNotFind, data
	}

	return data, nil
}

// GetTrialAssetDetailList implements repository.TrialRepository.
func (repo *trialRepository) GetTrialAssetDetailList(ctx context.Context, req *repository.GetTrialAssetDetailListReq) ([]*entity.TrialAssetDetailInfo, int64, error) {
	// 体验金明细数据
	if req.ActivityId > 0 {
		req.ActivityId = req.ActivityId % 1000000
	}

	sqlStr := "SELECT create_time,`type`,activity_id,award_op_id,award_amount,recovery_amount,open_count,trial_asset_status,open_amount,amount_used FROM trial_asset"
	sqlCountStr := "SELECT COUNT(*) AS total FROM trial_asset"
	if req.UID != "" {
		sqlStr = sqlStr + " WHERE user_id='" + req.UID + "'"
		sqlCountStr = sqlCountStr + " WHERE user_id='" + req.UID + "'"
	}
	if req.ActivityId > 0 {
		activityIdStr := strconv.FormatInt(req.ActivityId, 10)
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND activity_id=" + activityIdStr
			sqlCountStr = sqlCountStr + " AND activity_id=" + activityIdStr
		} else {
			sqlStr = sqlStr + " WHERE activity_id=" + activityIdStr
			sqlCountStr = sqlCountStr + " WHERE activity_id=" + activityIdStr
		}
	}
	if req.TrialTicketOrderId != "" {
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND award_op_id='" + req.TrialTicketOrderId + "'"
			sqlCountStr = sqlCountStr + " AND award_op_id='" + req.TrialTicketOrderId + "'"
		} else {
			sqlStr = sqlStr + " WHERE award_op_id='" + req.TrialTicketOrderId + "'"
			sqlCountStr = sqlCountStr + " WHERE award_op_id='" + req.TrialTicketOrderId + "'"
		}
	}
	if req.LastTime > 0 {
		lastTimeStr := strconv.FormatInt(req.LastTime, 10)
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND create_time<=" + lastTimeStr
			sqlCountStr = sqlCountStr + " AND create_time<=" + lastTimeStr
		} else {
			sqlStr = sqlStr + " WHERE status_time<=" + lastTimeStr
			sqlCountStr = sqlCountStr + " WHERE create_time<=" + lastTimeStr
		}
	}
	if req.TrialTicketType != 0 {
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND type=" + strconv.Itoa(req.TrialTicketType)
			sqlCountStr = sqlCountStr + " AND type=" + strconv.Itoa(req.TrialTicketType)
		} else {
			sqlStr = sqlStr + " WHERE type=" + strconv.Itoa(req.TrialTicketType)
			sqlCountStr = sqlCountStr + " WHERE type=" + strconv.Itoa(req.TrialTicketType)
		}
	}
	// 状态
	switch req.TrialAssetStatus {
	case domain.TrialAssetDisplayStatusPending:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusNoEffect)
			sqlCountStr = sqlCountStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusNoEffect)
		} else {
			sqlStr = sqlStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusNoEffect)
			sqlCountStr = sqlCountStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusNoEffect)
		}
		break
	case domain.TrialAssetDisplayStatusAvailable:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount<=0.0"
			sqlCountStr = sqlCountStr + " AND (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount<=0.0"
		} else {
			sqlStr = sqlStr + " WHERE (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount<=0.0"
			sqlCountStr = sqlCountStr + " WHERE (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount<=0.0"
		}
		break
	case domain.TrialAssetDisplayStatusUsing:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount>0.0"
			sqlCountStr = sqlCountStr + " AND (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount>0.0"
		} else {
			sqlStr = sqlStr + " WHERE (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount>0.0"
			sqlCountStr = sqlCountStr + " WHERE (trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusEffect) + " OR trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusWarn) + ") AND open_amount>0.0"
		}
		break
	case domain.TrialAssetDisplayStatusRecycled:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND type=" + strconv.Itoa(domain.TrialAssetTypeOnce) + " AND amount_used>0.0"
			sqlCountStr = sqlCountStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND type=" + strconv.Itoa(domain.TrialAssetTypeOnce) + " AND amount_used>0.0"
		} else {
			sqlStr = sqlStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND type=" + strconv.Itoa(domain.TrialAssetTypeOnce) + " AND amount_used>0.0"
			sqlCountStr = sqlCountStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND type=" + strconv.Itoa(domain.TrialAssetTypeOnce) + " AND amount_used>0.0"
		}
		break
	case domain.TrialAssetDisplayStatusExpired:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND (type <> 2 OR amount_used<=0.0)"
			sqlCountStr = sqlCountStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND (type <> 2 OR amount_used<=0.0)"
		} else {
			sqlStr = sqlStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND (type <> 2 OR amount_used<=0.0)"
			sqlCountStr = sqlCountStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusInvalid) + " AND (type <> 2 OR amount_used<=0.0)"
		}
		break
	case domain.TrialAssetDisplayStatusFinished:
		if strings.Contains(sqlStr, "WHERE") {
			sqlStr = sqlStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusFinish)
			sqlCountStr = sqlCountStr + " AND trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusFinish)
		} else {
			sqlStr = sqlStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusFinish)
			sqlCountStr = sqlCountStr + " WHERE trial_asset_status=" + strconv.Itoa(domain.TrialAssetStatusFinish)
		}
		break
	}

	var total int64
	err := repo.db.Raw(sqlCountStr).Scan(&total).Error
	if err != nil {
		return nil, 0, err
	}
	skipStr := strconv.FormatInt(int64((req.PageIndex-1)*req.PageSize), 10)
	pageSizeStr := strconv.FormatInt(int64(req.PageSize), 10)
	sqlStr = sqlStr + " ORDER BY create_time DESC LIMIT " + skipStr + "," + pageSizeStr
	trialAssetDetailInfos := make([]*entity.TrialAssetDetailInfo, 0)
	err = repo.db.Raw(sqlStr).Scan(&trialAssetDetailInfos).Error
	if err != nil {
		return nil, 0, err
	}

	for _, trialAssetDetail := range trialAssetDetailInfos {
		trialAssetDetail.TrialAssetStatus = trialAssetDetail.UpdateDisplayStatus()
		trialAssetDetail.StatusTime = trialAssetDetail.StatusTime / 1000000000
	}

	return trialAssetDetailInfos, total, nil
}

// GetTrialAssetList implements repository.TrialRepository.
func (repo *trialRepository) GetTrialAssetList(ctx context.Context, param *repository.TAListReq) (repository.TAListRes, error) {
	data := repository.TAListRes{
		Page: pager.Page{
			PageIndex: param.PageIndex,
			PageSize:  param.PageSize,
		},
	}

	param.Currency = domain.CurrencyUSDT
	isolatedPoss, _ := repo.positionRepo.UserPos(ctx, &repository.SwapParam{
		UID:      param.UID,
		Currency: param.Currency,
	})

	trialQuery := entity.NewTrialAssetQuery(nil)
	res, err := trialQuery.GetTrialStatsData(param.UID)
	if err != nil {
		return data, errors.Wrap(err, "get trial stats data")
	}
	data.TAwardAmount = res.TotalAmount
	data.NoEffectiveAmount = res.TotalPending
	data.UsedAmount = res.TotalUsed
	data.Amount = res.TotalAvailable.Sub(res.TotalPending)
	data.TInvalidAmount = data.TAwardAmount.Sub(data.UsedAmount).Sub(data.NoEffectiveAmount).Sub(data.Amount)
	// 可以考虑RecoveryAmount

	data.TrialList, data.Total, err = repo.TrialAssetList(ctx, trialQuery, param)
	if err != nil {
		return data, errors.Wrap(err, "get trial asset list")
	}

	data.Page.Total = data.Total
	for idx, trial := range data.TrialList {
		data.TrialList[idx].DisplayStatus = trial.UpdateDisplayStatus()
		if trial.TrialAssetStatus == domain.TrialAssetStatusWarn {
			data.TrialList[idx].TrialAssetStatus = domain.TrialAssetStatusEffect
		}
		for _, pos := range isolatedPoss {
			if pos.ContainsAwardOpId(trial.AwardOpId) {
				data.TrialList[idx].ProfitUnreal = pos.CalcProfitUnreal(repo.priceRepo.GetMarkPrice(ctx, pos.ContractCode))
			}
		}
	}

	return data, nil
}

// GetTrialAssetSummaryList implements repository.TrialRepository.
func (repo *trialRepository) GetTrialAssetSummaryList(ctx context.Context, req *repository.GetTrialAssetSummaryListReq) ([]*entity.TrialAssetSummaryInfo, int64, error) {
	// 体验金汇总数据
	lastTime := req.LastTime * 1e9
	var total int64

	trialAssetSummaryInfos := make([]*entity.TrialAssetSummaryInfo, 0)
	sqlStr := "SELECT user_id,SUM(award_amount) AS sum_amount,COUNT(award_op_id) AS count_ticket,SUM(recycle_amount) AS sum_remain_amount,COUNT(CASE WHEN (open_amount=NULL OR open_amount<=0.0) AND (trial_asset_status=2 OR trial_asset_status=3) THEN 1 ELSE NULL END) AS count_remain_ticket,SUM(CASE WHEN open_amount>0.0 AND (trial_asset_status=2 OR trial_asset_status=3) THEN open_amount ELSE 0 END) AS sum_using_amount,COUNT(CASE WHEN open_amount>0.0 AND (trial_asset_status=2 OR trial_asset_status=3) THEN 1 ELSE NULL END) AS count_using_ticket,SUM(CASE WHEN trial_asset_status=4 AND (type <> 2 OR amount_used<=0.0) THEN award_amount-amount_used ELSE 0 END) AS sum_overdue_amount,COUNT(CASE WHEN trial_asset_status=4 AND (type <> 2 OR amount_used <= 0.0) THEN 1 ELSE NULL END) AS count_overdue_ticket,SUM(CASE WHEN trial_asset_status=4 AND type=2 AND amount_used>0.0 THEN award_amount-amount_used ELSE 0 END) AS sum_recycle_amount,COUNT(CASE WHEN trial_asset_status=4 AND type=2 AND amount_used>0.0 THEN 1 ELSE NULL END) AS count_recycle_ticket,SUM(CASE WHEN trial_asset_status=5 THEN amount_used ELSE 0 end) AS sum_spend_all_amount,COUNT(CASE WHEN trial_asset_status=5 THEN 1 ELSE NULL end) AS count_spend_all_ticket FROM trial_asset"
	sqlCountStr := "SELECT COUNT(*) AS total FROM (" + sqlStr
	if req.UID != "" {
		sqlStr = sqlStr + " WHERE user_id='" + req.UID + "'"
		sqlCountStr = sqlCountStr + " WHERE user_id='" + req.UID + "'"
	}
	if lastTime > 0 {
		lastTimeStr := strconv.FormatInt(lastTime, 10)
		if req.UID != "" {
			sqlStr = sqlStr + " AND create_time<=" + lastTimeStr
			sqlCountStr = sqlCountStr + " AND create_time<=" + lastTimeStr
		} else {
			sqlStr = sqlStr + " WHERE create_time<=" + lastTimeStr
			sqlCountStr = sqlCountStr + " WHERE create_time<=" + lastTimeStr
		}
	}
	sqlCountStr = sqlCountStr + " GROUP BY user_id) AS total"
	err := repo.db.Raw(sqlCountStr).Scan(&total).Error
	if err != nil {
		return nil, 0, err
	}

	skipStr := strconv.FormatInt(int64((req.PageIndex-1)*req.PageSize), 10)
	pageSizeStr := strconv.FormatInt(int64(req.PageSize), 10)
	sqlStr = sqlStr + " GROUP BY user_id LIMIT " + skipStr + "," + pageSizeStr
	err = repo.db.Raw(sqlStr).Scan(&trialAssetSummaryInfos).Error
	if err != nil {
		return nil, 0, err
	}

	// 用户类型、是否开通合约
	userIds := make([]string, 0)
	userIdm := make(map[string]int)
	for index, trialAssetSummaryInfo := range trialAssetSummaryInfos {
		userIds = append(userIds, trialAssetSummaryInfo.UID)
		userIdm[trialAssetSummaryInfo.UID] = index
	}
	if len(userIds) == 0 {
		return trialAssetSummaryInfos, total, nil
	}

	return trialAssetSummaryInfos, total, nil
}

// TrialAssetList implements repository.TrialRepository.
func (repo *trialRepository) TrialAssetList(ctx context.Context, req *entity.TrialAssetQuery, param *repository.TAListReq) ([]*entity.TrialAsset, int64, error) {
	var assetList []*entity.TrialAsset
	var total int64

	sql := repo.db.WithContext(ctx).
		Model((*entity.TrialAsset)(nil)).
		Where("currency = ?", param.Currency)

	if param.UID != "" {
		sql = sql.Where("user_id = ?", param.UID)
	}
	if param.Type > 0 {
		sql = sql.Where("type = ?", param.Type)
	}
	if param.TrialAssetStatus != 0 {
		if param.TrialAssetStatus == domain.TrialAssetStatusEffect {
			sql = sql.Where("trial_asset_status = ? or trial_asset_status = ?", domain.TrialAssetStatusEffect, domain.TrialAssetStatusWarn)
		} else {
			sql = sql.Where("trial_asset_status = ?", param.TrialAssetStatus)
		}
	}

	if param.DisplayStatus > 0 {
		effectStatus := []int8{domain.TrialAssetStatusEffect, domain.TrialAssetStatusWarn}
		if param.DisplayStatus == domain.TrialAssetDisplayStatusPending {
			sql = sql.Where("trial_asset_status = ?", domain.TrialAssetStatusNoEffect)
		} else if param.DisplayStatus == domain.TrialAssetDisplayStatusFinished {
			sql = sql.Where("trial_asset_status = ?", domain.TrialAssetStatusFinish)
		} else if param.DisplayStatus == domain.TrialAssetDisplayStatusRecycled {
			sql = sql.Where("trial_asset_status = ? AND type = ? AND amount_used > 0", domain.TrialAssetStatusInvalid, domain.TrialAssetTypeOnce)
		} else if param.DisplayStatus == domain.TrialAssetDisplayStatusExpired {
			sql = sql.Where("trial_asset_status = ? AND invalid_time < now()", domain.TrialAssetStatusInvalid)
		} else if param.DisplayStatus == domain.TrialAssetDisplayStatusUsing {
			sql = sql.Where("trial_asset_status IN (?) AND open_amount > 0", effectStatus)
		} else if param.DisplayStatus == domain.TrialAssetDisplayStatusAvailable {
			sql = sql.Where("trial_asset_status IN (?) AND open_amount = 0", effectStatus)
		}
	}

	timeStr := ""
	if param.TimeStart > 0 || param.TimeEnd > 0 {
		switch param.TimeType {
		case domain.TATTAward:
			timeStr = "award_time"
		case domain.TATTEffective:
			timeStr = "effective_time"
		case domain.TATTInvalid:
			timeStr = "invalid_time"
		}
		if param.TimeStart >= 0 {
			sql = sql.Where(timeStr+" >= ?", param.TimeStart)
		}
		if param.TimeEnd >= 0 {
			sql = sql.Where(timeStr+" <= ?", param.TimeEnd)
		}
	}
	if len(timeStr) == 0 {
		timeStr = "award_time"
	}
	err := sql.Count(&total).Error
	if err != nil {
		return assetList, total, errors.Wrap(err, "count trial asset list")
	}

	err = sql.Offset((param.PageIndex - 1) * param.PageSize).Limit(param.PageSize).Order(timeStr + " desc").Find(&assetList).Error
	if err != nil {
		return assetList, total, errors.Wrap(err, "find trial asset list")
	}

	return assetList, total, nil
}

func (repo *trialRepository) GetLastUpdateTime(ctx context.Context, uid string) (int64, error) {
	var sqlStr string
	if uid != "" {
		sqlStr = "SELECT update_time FROM trial_asset WHERE user_id=" + "'" + uid + "' ORDER BY update_time DESC LIMIT 1"
	} else {
		sqlStr = "SELECT update_time FROM trial_asset ORDER BY update_time DESC LIMIT 1"
	}

	var lastTime int64
	err := repo.db.Raw(sqlStr).Scan(&lastTime).Error
	if err != nil {
		return 0, err
	}

	return lastTime / 1e9, nil
}

func (repo *trialRepository) RecycleTrialAsset(ctx context.Context, req *repository.OperateRecycleTrialAsset) error {
	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+req.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return errors.New("user lock err when recycle trial asset")
	}
	defer userMutex.Unlock()

	userAsset, err := repo.cacheRepo.Load(ctx, req.UID, "")
	if err != nil {
		return errors.New("load asset err when recycle trial asset")
	}
	nowTime := time.Now().Unix()

	for i := 0; i < len(userAsset.TrialDetail); i++ {
		if userAsset.TrialDetail[i].AwardOpId == req.AwardOpId {
			userAsset.TrialDetail[i].InvalidTime = nowTime
			userAsset.TrialDetail[i].RecycleOpId = req.RecycleOpId
			err = repo.UpdateTrialAsset(ctx, userAsset)
			if err != nil {
				return errors.New("update trial asset err when recycle trial asset")
			}
			return nil
		}
	}

	trialQuery := entity.NewTrialAssetQuery(nil)
	err = trialQuery.FindDataByAwardOpId(req.AwardOpId)
	if err != nil {
		return errors.New("find data by opId err when recycle trial asset")
	}
	if trialQuery.TrialAssetStatus == domain.TrialAssetStatusInvalid || trialQuery.TrialAssetStatus == domain.TrialAssetStatusFinish {
		return errors.New("trial finish or invalid when recycle trial asset")
	}

	return errors.New("opID not find when recycle trial asset")
}

func (repo *trialRepository) AddTrialAsset(ctx context.Context, addTrial *repository.TrialBase) error {
	logrus.Info(0, "received trial asset info:", addTrial)
	if !addTrial.CheckSlf() {
		logrus.Error(fmt.Sprintf("TrialBase param check err.addTrial:%+v", addTrial))
		return nil
	}

	checkBurst := repository.CheckBurstParam{UID: addTrial.UID}
	bursting, err := repo.burstRepo.IsBursting(ctx, checkBurst)
	if err != nil || bursting {
		logrus.Error(fmt.Sprintf("user in bursting.addTrial:%+v", addTrial))
		return errors.New(domain.ErrMsg[domain.ErrUserBurst])
	}

	userMutex := repo.rs.NewMutex(domain.MutexSwapPosLock+addTrial.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return domain.ErrLockPos
	}
	defer userMutex.Unlock()

	userAsset, err := repo.cacheRepo.Load(ctx, addTrial.UID, "")
	if err != nil {
		logrus.Info(fmt.Sprintf("TrialBase get asset err.addTrial:%+v", addTrial))
		return err
	}

	swapTrialAsset := &entity.TrialAsset{}
	modelutil.LoadTrialAssetByBase(addTrial, swapTrialAsset)

	swapTrialAsset.TrialAssetStatus = domain.TrialAssetStatusNoEffect
	timeNow := time.Now().Unix()
	if timeNow >= addTrial.EffectiveTime && timeNow <= addTrial.InvalidTime {
		swapTrialAsset.TrialAssetStatus = domain.TrialAssetStatusEffect
		userAsset.AddTrialBalance(addTrial.Currency, addTrial.AwardAmount)
	}

	userAsset.TrialDetail = append(userAsset.TrialDetail, swapTrialAsset)

	tryAssetAdd := repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:       addTrial.UID,
			OperateId: addTrial.AwardOpId,
			// BillId:    util.GenerateId(), // TODO snowflake ID
			Currency: addTrial.Currency,
			BillType: domain.BillTrialAssetAdd,
			Amount:   addTrial.AwardAmount,
			// Balance:     userAsset.TrialCBalance(addTrial.Currency),
			OperateTime: addTrial.AwardTime * 1e9,
		},
	}
	swapTrialAsset.Amount = swapTrialAsset.AwardAmount
	mqTAsset := repository.CmsTrialAsset{
		TAmount: swapTrialAsset.Amount,
	}
	mqTAsset.TDetail = append(mqTAsset.TDetail, swapTrialAsset)
	mqTrialAsset := repository.MqCmsAsset{
		Currency:    addTrial.Currency,
		OrderId:     addTrial.AwardOpId,
		UID:         addTrial.UID,
		Amount:      decimal.Zero,
		TAsset:      mqTAsset,
		OperateTime: addTrial.AwardTime * 1e9,
		OperateType: domain.BillTrialAssetAdd,
	}
	swapTrialAsset.OpType = domain.BillTrialAssetAdd

	err = repo.UpdateTrialAsset(ctx, userAsset)
	if err != nil {
		logrus.Info(fmt.Sprintf("UpdateTrialAsset err.userCache:%+v,addTrial:%+v", addTrial, addTrial))
		return err
	}

	logAssetSync := modelutil.NewLogAssetSync(userAsset, "", time.Now().UnixNano())
	go func() {
		redis := repo.rdb

		for _, bill := range []*repository.LogAssetSync{logAssetSync} {
			if bill.AssetSwap.UID == "" {
				logrus.Error(fmt.Sprintf("asset log not have user id, asset:%+v", bill))
				continue
			}
			// wallet资产异步存库
			if err := redis.LPush(ctx, domain.SyncListSwapAsset+bill.AssetSwap.UID[len(bill.AssetSwap.UID)-1:], bill); err != nil {
				logrus.Error(fmt.Sprintf("lpush asset err:%v", err))
			}
		}

		if err := redis.LPush(ctx, domain.SyncTrialAssetSwap, swapTrialAsset); err != nil {
			logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,swapTrialAsset:%+v", err, swapTrialAsset))
		}
		if err := redis.LPush(ctx, domain.ContractAssetKey, mqTrialAsset); err != nil {
			logrus.Error(fmt.Sprintf("mq lpush Asset fund Cost err:%v", err))
		}

		for _, bill := range []repository.BillAssetSync{tryAssetAdd} {
			if bill.Amount.IsZero() {
				continue
			}
			redisKey := domain.SyncListSwapBill
			if domain.RobotUsers.HasKey(bill.UID) {
				redisKey = domain.SyncListSwapBillRobot
			}
			if err := redis.LPush(ctx, redisKey, bill); err != nil {
				logrus.Error(fmt.Sprintf("lpush bill err:%v, bill:%+v", err, bill))
			}
		}
	}()
	// TODO 账本统计
	// go func() {
	// 	balance := userAsset.TrialBalance[strings.ToUpper(addTrial.Currency)]
	// 	es.SaveLedgerDetail(addTrial.UID, addTrial.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.CONTRACT_TRIAL_LEDGER_TYPE, 0, addTrial.AwardAmount, balance)
	// }()

	return nil
}

// UpdateTrialAsset 更新体验金
func (repo *trialRepository) UpdateTrialAsset(ctx context.Context, asset *repository.AssetSwap) error {
	if asset.TrialDetail == nil {
		asset.TrialDetail = repository.TrialTimeList{}
	}

	frozen, _ := json.Marshal(asset.Frozen)
	tryDetail, _ := json.Marshal(asset.TrialDetail)
	tryBalance, _ := json.Marshal(asset.TrialBalance)
	tryConsume, _ := json.Marshal(asset.TrialConsume)
	fields := map[string]interface{}{
		domain.TrialDetail.Key():  tryDetail,
		domain.TrialBalance.Key(): tryBalance,
		domain.TrialConsume.Key(): tryConsume,
		domain.Frozen.Key():       frozen,
	}

	return repo.rdb.HMSet(ctx, domain.AssetPrefix.Key(asset.UID), fields).Err()
}
