package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/match"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/go-redis/redis"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type FormulaRepositoryParm struct {
	dig.In

	RDB *redis.Client `name:"redis-cluster"`

	PriceRepo repository.PriceRepository
	AssetRepo repository.AssetRepository
}

type formulaRepository struct {
	rdb *redis.Client

	priceRepo     repository.PriceRepository
	assetRepo     repository.AssetRepository
	tempMarkPrice sync.Map
}

func NewFormulaRepository(parm FormulaRepositoryParm) repository.FormulaRepository {
	return &formulaRepository{
		rdb: parm.RDB,

		priceRepo:     parm.PriceRepo,
		assetRepo:     parm.AssetRepo,
		tempMarkPrice: sync.Map{},
	}
}

// CalcLiquidationPrice 计算预估强评价
/*
// 分子=[账户余额]+∑m-2{其他标的全仓未实现盈亏}-∑n{全部标的逐仓持仓成本}-∑m-2{其他标的全仓维持保证金}-[多仓仓位(正)*持仓均价]-[空仓仓位(负)* 持仓均价]-[单向仓位* 持仓均价]
// 分母=(多仓仓位(绝对值)*维持保证金率-多仓仓位(正)) + (空仓仓位(绝对值)*维持保证金率-空仓仓位(负)) + (单向仓位(绝对值)*维持保证金率-单向仓位)
*/
func (repo *formulaRepository) CalcLiquidationPrice(basePos *repository.PosQuery, longPos, shortPos, bothPos *repository.PosSwap,
	totalIsoMargin, balance, crossOther, crossOtherMargin, markPrice decimal.Decimal,
) decimal.Decimal {
	liquidationPrice, molecular, denominator := decimal.Zero, decimal.Zero, decimal.Zero
	base, quote := util.BaseQuote(basePos.ContractCode)
	if basePos.Isolated() {
		marginLevel, _, _ := setting.FetchMarginLevel(base, quote, basePos.CalcPosValue(markPrice))
		pos := decimal.Zero
		if basePos.PosSide == domain.LongPos {
			pos = longPos.Pos
		} else if basePos.PosSide == domain.ShortPos {
			pos = shortPos.Pos.Neg()
		} else if basePos.PosSide == domain.BothPos {
			pos = bothPos.Pos
		}
		molecular = pos.Mul(basePos.OpenPriceAvg).Sub(basePos.IsolatedMargin)
		denominator = pos.Sub(pos.Abs().Mul(marginLevel.HoldingMarginRate))
		if !denominator.IsZero() {
			liquidationPrice = molecular.Div(denominator) // 计算预估强评价
			if basePos.PosSide == domain.LongPos {
				liquidationPrice, _ = util.RoundCeil(liquidationPrice, domain.PricePrecision)
			} else {
				liquidationPrice = liquidationPrice.Truncate(domain.PricePrecision)
			}
		}
	} else {
		posValue := decimal.Zero
		posValue = longPos.CalcPosValue(markPrice).Add(shortPos.CalcPosValue(markPrice)).
			Add(bothPos.CalcPosValue(markPrice))

		marginLevel, _, _ := setting.FetchMarginLevel(base, quote, posValue)

		molecular = balance.Add(crossOther).Sub(crossOtherMargin).Sub(totalIsoMargin).
			Sub(longPos.GetPosWithSide().Mul(longPos.OpenPriceAvg)).
			Sub(shortPos.GetPosWithSide().Mul(shortPos.OpenPriceAvg)).
			Sub(bothPos.GetPosWithSide()).Mul(bothPos.OpenPriceAvg)

		denominator = marginLevel.HoldingMarginRate.Mul(longPos.Pos).Sub(longPos.GetPosWithSide()).
			Add(marginLevel.HoldingMarginRate.Mul(shortPos.GetPosWithSide()).Sub(shortPos.GetPosWithSide())).
			Add(marginLevel.HoldingMarginRate.Mul(bothPos.GetPosWithSide()).Sub(bothPos.GetPosWithSide()))

		if !denominator.IsZero() {
			liquidationPrice = molecular.Div(denominator) // 计算预估强评价
			if longPos.Pos.GreaterThan(shortPos.Pos) || bothPos.Pos.IsPositive() {
				liquidationPrice, _ = util.RoundCeil(liquidationPrice, domain.PricePrecision)
			} else {
				liquidationPrice = liquidationPrice.Truncate(domain.PricePrecision)
			}
		}
	}

	if liquidationPrice.IsNegative() {
		return decimal.Zero
	}

	return liquidationPrice
}

// IsolatedCollapsePrice 逐仓破产价格
/*
[仓位方向 * 持仓均价 * 持仓仓位 - 成本] / [(仓位方向 - Max(Taker手续费率,Maker手续费率)) * 持仓仓位]
*/
func (repo *formulaRepository) IsolatedCollapsePrice(pos *repository.PosSwap, takerFeeRate, makerFeeRate decimal.Decimal) (decimal.Decimal, string) {
	formulaRows := make([]string, 0)

	molecular := decimal.Zero
	denominator := decimal.Zero
	posAmount := pos.Pos
	posSide := decimal.NewFromInt(1)
	switch pos.PosSide {
	case domain.LongPos:

	case domain.ShortPos:
		posSide = decimal.NewFromInt(-1)

	case domain.BothPos:
		if pos.Pos.LessThan(decimal.Zero) {
			posSide = decimal.NewFromInt(-1)
			posAmount = posAmount.Abs()
		}

	default:
		return decimal.Zero, ""
	}

	// 分子 = 仓位方向 * 持仓均价 * 持仓仓位 - 成本
	molecular = posSide.Mul(pos.OpenPriceAvg.Mul(posAmount)).Sub(pos.IsolatedMargin)
	{
		formulaRows = append(formulaRows, fmt.Sprintf(
			"分子: 仓位方向<%s> * 持仓均价<%s> * 持仓仓位<%s> - 持仓成本<%s> = <%s>",
			posSide, pos.OpenPriceAvg, posAmount, pos.IsolatedMargin, molecular,
		))
	}
	// 分母 = (仓位方向 - Max(Taker手续费率,Maker手续费率)) * 持仓仓位
	denominator = posSide.Sub(decimal.Max(takerFeeRate, makerFeeRate)).Mul(posAmount)
	{
		formulaRows = append(formulaRows, fmt.Sprintf(
			"分母: (仓位方向<%s> * Max(Taker手续费率<%s>,Maker手续费率<%s>)) * 持仓仓位<%s> = <%s>",
			posSide, takerFeeRate, makerFeeRate, posAmount, denominator,
		))
	}

	if denominator.IsZero() {
		// 逐仓破产价格公式记录
		{
			formulaRows = append(formulaRows, fmt.Sprintf("破产价格: %s / %s = 0", molecular, denominator))
		}
		return decimal.Zero, strings.Join(formulaRows, "\n")
	}
	collapsePrice := molecular.Div(denominator)
	switch pos.PosSide {
	case domain.LongPos:
		collapsePrice = collapsePrice.Round(domain.CurrencyPrecision)

	case domain.ShortPos:
		collapsePrice = collapsePrice.Truncate(domain.CurrencyPrecision)

	case domain.BothPos:
		if pos.Pos.GreaterThan(decimal.Zero) {
			collapsePrice = collapsePrice.Round(domain.CurrencyPrecision)
		} else if pos.Pos.LessThan(decimal.Zero) {
			collapsePrice = collapsePrice.Truncate(domain.CurrencyPrecision)
		}

	default:
	}

	// 逐仓破产价格公式记录
	{
		formulaRows = append(formulaRows, fmt.Sprintf("破产价格: %s / %s = %s", molecular, denominator, collapsePrice))
	}

	return collapsePrice, strings.Join(formulaRows, "\n")
}

// CrossCollapsePrice 全仓破产价格
/*
[Max{0,账户余额 - ∑{全部逐仓持仓成本} + ∑{其它全仓未实现盈亏} }- 仓位方向 * 持仓均价 * 持仓仓位] / 持仓仓位 * (Max(Taker手续费率,Maker手续费率) - 仓位方向)
isolatedAllMargin 	全部逐仓仓位保证金
otherCrossUnreal	其他全仓合约未实现盈亏
otherCrossMargin	其他全仓合约维持保证金
*/
func (repo *formulaRepository) CrossCollapsePrice(ctx context.Context, asset *repository.AssetSwap, pos *repository.PosSwap, isolatedAllMargin decimal.Decimal, otherCrossUnreal decimal.Decimal, otherCrossMargin decimal.Decimal, takerFeeRate decimal.Decimal, makeFeeRate decimal.Decimal) (decimal.Decimal, string, error) {
	formulaRows := make([]string, 0)

	posSide := decimal.NewFromInt(1)
	posAmount := pos.Pos
	switch pos.PosSide {
	case domain.LongPos:

	case domain.ShortPos:
		posSide = decimal.NewFromInt(-1)

	case domain.BothPos:
		if pos.Pos.LessThan(decimal.Zero) {
			posSide = decimal.NewFromInt(-1)
			posAmount = posAmount.Abs()
		}

	default:

	}

	totalBalance := asset.CBalance(pos.Currency)
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		var err error = nil
		totalBalance, err = repo.assetRepo.TotalJoinBalance(ctx, asset)
		if err != nil {
			return decimal.Zero, "", err
		}

		rate = repo.priceRepo.SpotURate(ctx, pos.Currency)
	}

	tempBalance := decimal.Max(decimal.NewFromInt(0), isolatedAllMargin.Mul(rate).Sub(totalBalance).Sub(otherCrossUnreal.Mul(rate)))
	// molecular = Max{0, 账户余额 - ∑{全部逐仓持仓成本} + ∑{其它全仓未实现盈亏}} - 仓位方向 * 持仓均价 * 持仓仓位
	molecular := tempBalance.Sub(posSide.Mul(pos.OpenPriceAvg.Mul(rate).Mul(posAmount)))
	{
		formulaRows = append(formulaRows, fmt.Sprintf("%s-USDT 汇率: %s", pos.Currency, rate),
			fmt.Sprintf(
				"分子: Max{0, 账户余额<%s> - ∑{全部逐仓持仓成本}<%s> + ∑{其它全仓未实现盈亏}<%s> } - 仓位方向<%s> * 持仓均价<%s> * 持仓仓位<%s> = <%s>",
				totalBalance, isolatedAllMargin.Mul(rate), otherCrossUnreal.Mul(rate), posSide, pos.OpenPriceAvg.Mul(rate), posAmount, molecular,
			))
	}

	// denominator = 持仓仓位 * (Max(Taker手续费率,Maker手续费率) - 仓位方向)
	denominator := posAmount.Mul(decimal.Max(takerFeeRate, makeFeeRate).Sub(posSide))
	{
		formulaRows = append(formulaRows, fmt.Sprintf(
			"分母: 持仓仓位<%s> * (Max(Taker手续费率<%s>,Maker手续费率<%s>) - 仓位方向<%s>) = <%s>",
			posAmount, takerFeeRate, makeFeeRate, posSide, denominator,
		))
	}

	if denominator.IsZero() {
		// 全仓破产价格公式记录
		{
			formulaRows = append(formulaRows, fmt.Sprintf("破产价格: %s / %s = 0", molecular, denominator))
		}
		return decimal.Zero, strings.Join(formulaRows, "\n"), nil
	}

	collapsePrice := molecular.Div(denominator)
	switch pos.PosSide {
	case domain.LongPos:
		collapsePrice = collapsePrice.Round(domain.PricePrecision)

	case domain.ShortPos:
		collapsePrice = collapsePrice.Truncate(domain.PricePrecision)

	case domain.BothPos:
		if pos.Pos.GreaterThan(decimal.Zero) {
			collapsePrice = collapsePrice.Round(domain.PricePrecision)
		} else if pos.Pos.LessThan(decimal.Zero) {
			collapsePrice = collapsePrice.Truncate(domain.PricePrecision)
		}

	default:
	}

	// 全仓破产价格公式记录
	{
		formulaRows = append(formulaRows, fmt.Sprintf("破产价格: %s / %s = %s", molecular, denominator, collapsePrice))
	}

	return collapsePrice, strings.Join(formulaRows, "\n"), nil
}

// GetMaxPosValueWithLeverage 获取最大可开仓位价值
func (repo *formulaRepository) GetMaxPosValueWithLeverage(ctx context.Context, base string, quote string, leverage int) (decimal.Decimal, error) {
	maxPosValue := decimal.Zero
	levelHoldSortList, err := setting.Service.GetMarginRateLevel(base, quote)
	if err != nil {
		logrus.Error("HoldingMarginRate GetMarginRate error:", err)
		return maxPosValue, errors.New(fmt.Sprint("HoldingMarginRate GetMarginRate error:", err))
	}

	leverSortList := setting.LevelLeverSortList(levelHoldSortList)
	sort.Sort(leverSortList)

	for _, levelFilter := range leverSortList {
		if levelFilter.Lever >= leverage {
			maxPosValue = levelFilter.HighLimit
			break
		}
	}

	return maxPosValue, nil
}

// PremiumIndex 计算溢价指数
func (repo *formulaRepository) PremiumIndex(ctx context.Context, symbol string, cfg setting.ContractPair) decimal.Decimal {
	// 冲击保证金额 （IGM）= 200 USDT * 最高杠杆倍数
	igm := decimal.NewFromInt(200).Mul(decimal.NewFromInt(int64(cfg.MaxLeverage)))
	depth := match.NewMatchData().SwapDepth(symbol)
	if depth.Time <= 0 {
		logrus.Errorln(symbol, "PremiumIndex SwapDepth failed")
		return decimal.Zero
	}

	if depth.Time <= 0 {
		logrus.Errorln(symbol, "PremiumIndex SwapDepth failed")
		return decimal.Zero
	}
	buyIGMList := make([]decimal.Decimal, 0)
	buyVolumeTotal := decimal.Zero // 买盘满足冲击保证金额的所有档位交易额
	prevBuyVolume := decimal.Zero  // 买盘满足冲击保证金额前一档所有档位交易额
	prevBuySize := decimal.Zero    // 买盘满足冲击保证金额前一档位价格
	buyPrice := decimal.Zero       // 买盘满足冲击保证金额最后档位价格

	sellIGMList := make([]decimal.Decimal, 0)
	sellVolumeTotal := decimal.Zero // 卖盘满足冲击保证金额的所有档位交易额
	prevSellVolume := decimal.Zero  // 卖盘满足冲击保证金额前一档所有档位交易额
	prevSellSize := decimal.Zero    // 卖盘满足冲击保证金额前一档位价格
	sellPrice := decimal.Zero       // 卖盘满足冲击保证金额最后档位价格
	for _, dInfo := range depth.Asks {
		p, _ := decimal.NewFromString(dInfo[0])
		amount, _ := decimal.NewFromString(dInfo[1])
		volume := p.Mul(amount)

		sellIGMList = append(sellIGMList, p)
		sellVolumeTotal = sellVolumeTotal.Add(volume)

		if sellVolumeTotal.GreaterThan(igm) {
			sellPrice = p
			break
		}
		prevSellVolume = prevSellVolume.Add(volume)
		prevSellSize = prevSellSize.Add(amount)
	}
	for _, dInfo := range depth.Bids {
		p, _ := decimal.NewFromString(dInfo[0])
		amount, _ := decimal.NewFromString(dInfo[1])
		volume := p.Mul(amount)

		buyIGMList = append(buyIGMList, p)
		buyVolumeTotal = buyVolumeTotal.Add(volume)

		if buyVolumeTotal.GreaterThan(igm) {
			buyPrice = p
			break
		}
		prevBuyVolume = prevBuyVolume.Add(volume)
		prevBuySize = prevBuySize.Add(amount)
	}

	buyIGM := decimal.Zero
	if len(buyIGMList) == 1 {
		buyIGM = buyIGMList[0]
	} else if len(buyIGMList) > 1 {
		buyIGM = decimal.Avg(buyIGMList[0], buyIGMList[1:]...)
		// 按照实际溢价指数公式计算 IMN / [(IMN-multiplier * ∑px-1 * qx-1)/px+multiplier * ∑qx-1]
		if !buyPrice.IsZero() {
			molecular := igm
			denominator := igm.Sub(prevBuyVolume).Div(buyPrice).Add(prevBuySize)
			if !denominator.IsZero() {
				buyIGM = molecular.Div(denominator)
			}
		}
	}
	sellIGM := decimal.Zero
	if len(sellIGMList) == 1 {
		sellIGM = sellIGMList[0]
	} else if len(sellIGMList) > 1 {
		sellIGM = decimal.Avg(sellIGMList[0], sellIGMList[1:]...)
		// 按照实际溢价指数公式计算 IMN / [(IMN-multiplier * ∑px-1 * qx-1)/px+multiplier * ∑qx-1]
		if !sellPrice.IsZero() {
			molecular := igm
			denominator := igm.Sub(prevSellVolume).Div(sellPrice).Add(prevSellSize)
			if !denominator.IsZero() {
				sellIGM = molecular.Div(denominator)
			}
		}
	}

	indexPrice := repo.priceRepo.GetIndexPrice(ctx, symbol)
	if indexPrice.IsZero() {
		logrus.Errorln(symbol, "PremiumIndex indexPrice is zero")
		return decimal.Zero
	}

	pIndex := decimal.Max(decimal.NewFromInt(0), buyIGM.Sub(indexPrice)).
		Sub(decimal.Max(decimal.NewFromInt(0), indexPrice.Sub(sellIGM))).
		Div(indexPrice)
	// HY-334 限制溢价指数范围 +-0.9
	if pIndex.LessThan(decimal.NewFromFloat32(-0.9)) || pIndex.GreaterThan(decimal.NewFromFloat32(0.9)) {
		return decimal.Zero
	}
	logrus.Infof("verify calculation premium index pair: %s impact ask price: %s impact bid price: %s index price: %s pIndex: %s", symbol, sellIGM.String(), buyIGM.String(), indexPrice.String(), pIndex.String())

	pIndexSetKey := domain.GetPIndexRedisKey(symbol)
	err := repo.rdb.ZAdd(pIndexSetKey, redis.Z{
		Score:  float64(time.Now().Truncate(time.Minute).Unix()),
		Member: pIndex.String(),
	}).Err()
	if err != nil {
		logrus.Errorln(symbol, "PremiumIndex ZAdd error:", err)
	}

	return pIndex
}

// SetOneFundRate 生成单次资金费率
func (repo *formulaRepository) SetOneFundRate(ctx context.Context, fundRateListKey string, symbol string, indexPrice decimal.Decimal, interest decimal.Decimal) {
	// TODO 获取盘口买一卖一价格
	price := repo.priceRepo.GetLastPrice(ctx, symbol)
	buyFirst := price
	sellFirst := price
	// buyFirst, sellFirst, err := sharedcache.GetBestPrice(symbol)
	// if err != nil {
	// 	price := repo.priceRepo.GetLastPrice(ctx, symbol)
	// 	buyFirst = price
	// 	sellFirst = price
	// }

	if buyFirst.IsPositive() && sellFirst.IsPositive() {
		// 指数价格不为0 获取这一分钟的指数价格 并且放到 redis 中
		if !indexPrice.IsZero() {
			fundRate := buyFirst.Add(sellFirst).Div(decimal.NewFromFloat(2)).Sub(indexPrice).Div(indexPrice).Sub(interest).Truncate(domain.FundRatePrecision)
			fundRateStr := fundRate.String() + domain.FundRateSplit + strconv.Itoa(int(time.Now().Unix()))
			err := repo.rdb.ZAdd(fundRateListKey, redis.Z{
				Member: fundRateStr,
				Score:  float64(time.Now().Unix()),
			}).Err()
			if err != nil {
				logrus.Error(fmt.Sprintf("fundRateListKey add err:%+v,fundRate:%+v,_fundRateListKey:%s", err, fundRate, fundRateListKey))
			}
			listLen, err := repo.rdb.ZCard(fundRateListKey).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				logrus.Errorln(fmt.Sprintf("fundRateListKey ZCard error: %s", err))
			}
			if err == nil && listLen > domain.MaxFundRateNum {
				go repo.rdb.ZRemRangeByRank(fundRateListKey, 0, listLen-domain.MaxFundRateNum)
			}
		} else {
			logrus.Error(fmt.Sprintf("indexPrice is 0._fundRateListKey:%s", fundRateListKey))
		}
	}
}

// MakeMarkPrice 计算标记价格
//
// 标记价格 = 合约中位数(价位1, 价位2, 合约最新成交价)
// 价位 1 = 现货指数价格 + 现货指数价格 * 当前资金费率 * (距下次结算剩余时间(小时)/8)
// 价位 2 = 现货指数价格 + 基差移动平均值
func (repo *formulaRepository) MakeMarkPrice(ctx context.Context, symbol string) {
	fundPrice, basisPrice, lastPrice, markPrice := decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero

	contractSetting, err := setting.Service.GetPairSettingInfo(util.BaseQuote(symbol))
	if err != nil {
		logrus.Errorln(fmt.Sprintln(symbol, "MakeMarkPrice GetPairSettingInfo error:", err))
		return
	}
	if contractSetting == nil {
		logrus.Errorln(fmt.Sprintln(symbol, "MakeMarkPrice contractSetting is nil"))
		return
	}

	lastPrice = repo.priceRepo.GetLastPrice(ctx, strings.ToUpper(symbol))
	indexPrice := repo.priceRepo.GetIndexPrice(ctx, symbol)
	if strings.Contains(symbol, "shib") || strings.Contains(symbol, "xec") ||
		strings.Contains(symbol, "SHIB") || strings.Contains(symbol, "XEC") {
		logrus.Info(0, "finish sharedcache.IndexPrice", symbol, indexPrice.String())
	}
	if indexPrice.IsZero() {
		markPrice = lastPrice
	} else {
		fundPrice = repo.makeFundRatePrice(ctx, symbol, indexPrice, *contractSetting)
		basisPrice = repo.makeBasisPrice(ctx, symbol, indexPrice, *contractSetting)
		markPrice = repo.midPrice(ctx, fundPrice, basisPrice, lastPrice)

		// 标记价格修正
		// if indexPrice.Sub(markPrice).Abs().Div(indexPrice).GreaterThan(decimal.NewFromFloat(0.03)) {
		//	markPrice = basisPrice
		// }

		// 标记价格计算为0，强制改为合约最新成交价
		if markPrice.LessThanOrEqual(decimal.Zero) {
			markPrice = lastPrice
		}
	}

	if oldPrice, ok := repo.tempMarkPrice.Load(symbol); !ok || oldPrice.(string) != markPrice.Truncate(contractSetting.PricePrecision).String() {
		// 保存标记价格
		repo.priceRepo.SetMarkPrice(ctx, symbol, markPrice)
		// 保存标记价格记录
		markPriceRecordData := map[string]string{
			"index_price": indexPrice.String(),
			"basis_price": basisPrice.String(),
			"fund_price":  fundPrice.String(),
			"last_price":  lastPrice.String(),
			"mark_price":  markPrice.String(),
		}
		jsonBytes, _ := json.Marshal(markPriceRecordData)
		repo.rdb.HSet(domain.GetUsingMarkPriceRedisKey(), symbol, string(jsonBytes))
		repo.tempMarkPrice.Store(symbol, markPrice.Truncate(contractSetting.PricePrecision).String())

		// TODO 检查下面所有逻辑

		// if contractSetting.State == 1 || contractSetting.State == 3 {
		// 	// 保存撮合队列
		// 	err = cache.RedisCli.RPush(fmt.Sprintf("%s%s:mark_price:list", cache.Prefix, symbol), markPrice.String())
		// 	if err != nil {
		// 		logrus.Error(fmt.Sprintf("MakeMarkPrice(%s) RPush mark_price error:", symbol), err)
		// 	}
		// 	err = cache.RedisCli.RPush(fmt.Sprintf("%s%s:index_price:list", cache.Prefix, symbol), indexPrice.String())
		// 	if err != nil {
		// 		logrus.Error(fmt.Sprintf("MakeMarkPrice(%s) RPush index_price error:", symbol), err)
		// 	}

		// 	// 增量推送
		// 	base, quote := util.BaseQuote(symbol)
		// 	// 触发爆仓检查
		// 	TriggerBurstScan(base, quote)

		// 	// k线保存
		// 	go func() {
		// 		timestamp := time.Now().Unix()
		// 		// 指数价格k线保存
		// 		indexPriceKlineKey := cache.GetContractPriceKlineRedisKey(message.SwapAccount, strings.ToLower(base), strings.ToLower(quote), cache.KLineTypeIndexPrice)
		// 		_saveRedisKline(indexPriceKlineKey, indexPrice.Truncate(domain.PricePrecision), timestamp)
		// 		// 标记价格k线保存
		// 		markPriceKlineKey := cache.GetContractPriceKlineRedisKey(message.SwapAccount, strings.ToLower(base), strings.ToLower(quote), cache.KLineTypeMarkPrice)
		// 		_saveRedisKline(markPriceKlineKey, markPrice.Truncate(domain.PricePrecision), timestamp)
		// 	}()

		// 	// 前端ws推送
		// 	go func() {
		// 		defer func() {
		// 			if err := recover(); err != nil {
		// 				logrus.Error(fmt.Sprintf("MakeMarkPrice symbol(%s) message push error:", symbol), err)
		// 				return
		// 			}
		// 		}()
		// 		time.Sleep(time.Second * 5)
		// 		pushPrecision := contractSetting.PricePrecision
		// 		if pushPrecision < 1 {
		// 			pushPrecision = domain.PricePrecision
		// 		}
		// 		message.New("", message.PosQueueIndex, mqlib.CommonAmqp).TopicMarkPrice(message.SwapAccount, symbol).Push(map[string]interface{}{
		// 			"symbol": strings.ToUpper(symbol),
		// 			"markPrice":    markPrice.Truncate(pushPrecision).String(),
		// 			"indexPrice":   indexPrice.Truncate(pushPrecision).String(),
		// 		})
		// 	}()
		// }
	}

	return
}

func (repo *formulaRepository) saveRedisKline(_redisKey string, _price decimal.Decimal, _timestamp int64) {
	secondKLine := struct {
		Time  int64           // 起始时间戳
		Open  decimal.Decimal // 开盘价
		Close decimal.Decimal // 收盘价
		High  decimal.Decimal // 最高价
		Low   decimal.Decimal // 最低价
	}{
		Time:  _timestamp,
		Open:  _price.Truncate(domain.PricePrecision),
		Close: _price.Truncate(domain.PricePrecision),
		High:  _price.Truncate(domain.PricePrecision),
		Low:   _price.Truncate(domain.PricePrecision),
	}
	secondBytes, err := json.Marshal(secondKLine)
	if err != nil {
		logrus.Error(fmt.Sprintf("saveRedisKline Marshal(%s) error:", string(secondBytes)), err)
	}
	err = repo.rdb.ZAdd(_redisKey, redis.Z{
		Member: secondBytes,
		Score:  float64(_timestamp),
	}).Err()
	if err != nil {
		logrus.Error(fmt.Sprintf("saveRedisKline SetString(%s) error:", string(secondBytes)), err)
	}
}

func (repo *formulaRepository) midPrice(ctx context.Context, price ...decimal.Decimal) decimal.Decimal {
	sortedList := util.SortDecimals(false, price...)
	if len(sortedList) == 3 {
		return sortedList[1]
	}
	// 3个价格意外情况产品未定义
	return decimal.Zero
}

// makeBasisPrice 价格指数+ 移动平均值
func (repo *formulaRepository) makeBasisPrice(ctx context.Context, _contractCode string, _indexPrice decimal.Decimal, _contractSetting setting.ContractPair) decimal.Decimal {
	// maBasis := sharedcache.MaBasis(GetBaseNum(domain.UBaseNum), _contractCode)
	maBasis := repo.MaBasis(30, _contractCode)
	markPrice := _indexPrice.Add(maBasis)
	return markPrice.Truncate(_contractSetting.PricePrecision)
}

// MaBasis MA基差
func (repo *formulaRepository) MaBasis(_num int, _contractCode string) decimal.Decimal {
	basisListKey := domain.AssetPrefix.Key(fmt.Sprintf("%s:basis_list", _contractCode))
	basisList, err := repo.rdb.ZRevRangeByScoreWithScores(basisListKey, redis.ZRangeBy{
		Min:    "0",
		Max:    "*",
		Offset: 0,
		Count:  int64(_num),
	}).Result()
	if err != nil {
		logrus.Error(fmt.Sprintln("MarkPrice ZRevRangeByScoreWithScores ", basisListKey, " error:", err))
		return decimal.Zero
	}
	if len(basisList) < 1 {
		return decimal.Zero
	}

	total := decimal.Zero
	for _, z := range basisList {
		if price, ok := z.Member.(string); ok {
			priceList := strings.Split(price, "_")
			if len(priceList) < 1 {
				logrus.Error(fmt.Sprintln(_contractCode, "MaBasis", _num, "priceList is empty"))
				continue
			}
			temp, _ := decimal.NewFromString(priceList[0])
			total = total.Add(temp)
		} else {
			return decimal.Zero
		}
	}

	return total.Div(decimal.NewFromFloat(float64(len(basisList))))
}

// makeFundRatePrice 价格指数* (1 + 资金费率 *(距离下次资金费率收取的时间（小时）/8))
func (repo *formulaRepository) makeFundRatePrice(ctx context.Context, symbol string, indexPrice decimal.Decimal, contractSetting setting.ContractPair) decimal.Decimal {
	fundRate := repo.FundingRate(ctx, symbol, contractSetting)

	currentHour := time.Now().Hour()
	fundTimes := math.Ceil(float64(currentHour) / 8)
	if fundTimes < 1 {
		fundTimes = 1
	}

	// 距下次结算剩余时间(小时)
	rangeHour := fundTimes*float64(8) - float64(currentHour)
	// (距下次结算剩余时间(小时)/8)
	rangeHourRate := rangeHour / float64(8)

	// ( 现货指数价格 * 当前资金费率 * (距下次结算剩余时间(小时)/8) ) + 现货指数价格
	markPrice := indexPrice.Mul(fundRate).Mul(decimal.NewFromFloat(rangeHourRate)).Add(indexPrice)

	return markPrice.Truncate(contractSetting.PricePrecision)
}

func (repo *formulaRepository) FundingRate(ctx context.Context, symbol string, cfg setting.ContractPair) decimal.Decimal {
	pIndexSetKey := domain.GetPIndexRedisKey(symbol)
	indexPriceWithScores, err := repo.rdb.ZRangeByScoreWithScores(pIndexSetKey, redis.ZRangeBy{
		Min: "-inf",
		Max: "+inf",
	}).Result()
	if err != nil {
		logrus.Errorln(symbol, "FundRate ZRevRangeByScore:", pIndexSetKey, "error:", err)
		return decimal.Zero
	}

	// 获取溢价指数列表
	pIndexList := repo.GetPremiumIndexList(indexPriceWithScores)
	fmt.Println(fmt.Sprintf("pair: %s pIndexList len: %d data: %+v", symbol, len(pIndexList), pIndexList))

	totalCount := len(pIndexList)
	pCount := len(pIndexList)
	if pCount > domain.DefaultBaseNum {
		pCount = domain.DefaultBaseNum
	}
	// 计算平均溢价指数
	pAvg := decimal.Zero
	{
		pSum := decimal.Zero
		pRight := decimal.Zero
		for i := 0; i < pCount; i++ {
			right := decimal.NewFromInt(int64(i + 1))
			pSum = pSum.Add(pIndexList[i].Mul(right))
			pRight = pRight.Add(right)
		}
		// fmt.Println("premium index sum: ", pSum.String(), "divisor sum: ", pRight.String())
		if totalCount > 0 {
			pAvg = pSum.Div(pRight)
		}
	}

	I := decimal.NewFromFloat(0.0001) // 基础利率(I)
	if !cfg.Interest.IsZero() {
		I = cfg.Interest // 从后台配置获取-基础利率(I)
	}
	diff := I.Sub(pAvg)
	minValue, maxValue := decimal.NewFromFloat(-0.0005), decimal.NewFromFloat(0.0005)
	if !cfg.MinCapitalFeeRate.IsZero() {
		minValue = cfg.MinCapitalFeeRate // 从后台配置获取-公式限定区间下限
	}
	if !cfg.MaxCapitalFeeRate.IsZero() {
		maxValue = cfg.MaxCapitalFeeRate // 从后台配置获取-公式限定区间上限
	}

	clamp := Clamp(minValue, maxValue, diff)
	fundRate := pAvg.Add(clamp)
	// 获取最大杠杆倍数以及对应维持保证金率
	// 对应 https://qfglxo2m3dc.sg.larksuite.com/docx/ZlyddmOJWopmZVxET71lpp4ugrd 6、资金费率的峰值边界限制
	maxLevelInfo := setting.MarginRateLevelInfo{}
	for _, level := range cfg.MarginRateGear {
		if level.LeverMultiple > maxLevelInfo.LeverMultiple {
			maxLevelInfo = level
		}
	}
	boundFloor, boundCap := fundRate, fundRate
	if maxLevelInfo.LeverMultiple > 25 {
		boundFloor, boundCap = maxLevelInfo.MaintenanceRate.Mul(decimal.NewFromFloat(-0.75)), maxLevelInfo.MaintenanceRate.Mul(decimal.NewFromFloat(0.75))
	}
	if maxLevelInfo.LeverMultiple <= 25 {
		boundFloor, boundCap = decimal.NewFromFloat(-0.03), decimal.NewFromFloat(0.03)
	}
	fundRate = Clamp(boundFloor, boundCap, fundRate)
	fmt.Println(fmt.Sprintf("pair: %s funcing rate: %s interest rate: %s avg premium index: %s\ni-P: %s clamp: %s\n", symbol, fundRate.String(), I.String(), pAvg.String(), diff.String(), clamp.String()))

	fundRateListKey := domain.GetFundRateRedisKey(symbol)
	err = repo.rdb.ZAdd(fundRateListKey, redis.Z{
		Score:  float64(time.Now().Truncate(time.Minute).Unix()),
		Member: fundRate.String(),
	}).Err()
	if err != nil {
		logrus.Errorln(symbol, "FundRate ZAdd error:", err)
	}

	if len(pIndexList) > 1440 {
		for _, str := range pIndexList[1441:] {
			repo.rdb.ZRem(pIndexSetKey, str)
		}
	}

	return fundRate
}

func Clamp(_min, _max, _value decimal.Decimal) decimal.Decimal {
	if _value.LessThan(_min) {
		return _min
	}
	if _value.GreaterThan(_max) {
		return _max
	}
	return _value
}

// GetPremiumIndexList 获取溢价指数列表
func (repo *formulaRepository) GetPremiumIndexList(indexPriceWithScores []redis.Z) []decimal.Decimal {
	timeIntervals := make([]int64, 0)
	timePriceMap := make(map[int64]decimal.Decimal)
	currentTime := time.Now().Unix()
	compareTime := currentTime - 480*60

	pIndexList := make([]decimal.Decimal, 0)
	if len(indexPriceWithScores) == 0 {
		for i := 0; i < domain.DefaultBaseNum; i++ {
			pIndexList = append(pIndexList, decimal.Zero)
		}
	} else {
		var lastPremiumIndex decimal.Decimal // 最后一次溢价指数
		for _, score := range indexPriceWithScores {
			premiumIndexTime := int64(score.Score)
			premiumIndex := parsePrice(score.Member)
			if premiumIndexTime >= compareTime {
				timePriceMap[premiumIndexTime] = premiumIndex
				timeIntervals = append(timeIntervals, premiumIndexTime)
			}
			lastPremiumIndex = premiumIndex
		}
		sort.Slice(timeIntervals, func(i, j int) bool {
			return timeIntervals[i] < timeIntervals[j]
		})

		switch len(timeIntervals) {
		case 0:
			fallthrough
		case 1:
			for i := 0; i < domain.DefaultBaseNum; i++ {
				pIndexList = append(pIndexList, lastPremiumIndex)
			}
			break
		default:
			for i := 0; i < len(timeIntervals)-1; i++ {
				for j := 0; j < int((timeIntervals[i+1]-timeIntervals[i])/60); j++ {
					pIndexList = append(pIndexList, timePriceMap[timeIntervals[i]])
				}
			}
			if len(pIndexList) < domain.DefaultBaseNum {
				diffLen := domain.DefaultBaseNum - len(pIndexList)
				for i := 0; i < diffLen; i++ {
					pIndexList = append(pIndexList, lastPremiumIndex)
				}
			}
		}
	}
	return pIndexList
}

func parsePrice(member interface{}) decimal.Decimal {
	if pstr, ok := member.(string); ok {
		price, err := decimal.NewFromString(pstr)
		if err == nil {
			return price
		}
	}

	return decimal.Zero
}
