package router

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"runtime"
	"strings"
	"time"

	"futures-asset/api/web"
	"futures-asset/api/web/middleware"
	"futures-asset/conf"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/service/burst"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upGrader = websocket.Upgrader{
	HandshakeTimeout:  time.Second * 10,
	WriteBufferPool:   nil,
	Subprotocols:      nil,
	Error:             nil,
	EnableCompression: false,
}

func Init() *gin.Engine {
	if !conf.Conf.Biz.Debug {
		gin.SetMode(gin.ReleaseMode)
	}
	// engine := gin.Default()
	engine := gin.New()

	// add support cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("X-Requested-With")
	corsConf.AllowAllOrigins = true
	engine.Use(gin.Recovery(), cors.New(corsConf))

	// use middleware
	engine.Use(middleware.ApiLogMiddleware)

	// gzip conpression
	engine.Use(gzip.Gzip(gzip.DefaultCompression))

	// health check api
	engine.GET("/api/wallet/health", func(ctx *gin.Context) {
		ctx.JSON(http.StatusOK, gin.H{
			"code": http.StatusOK,
			"msg":  "success",
		})
	})

	// init swap api
	web.InitApi(engine)

	return engine
}

func InitIndexRouter() *gin.Engine {
	if !conf.Conf.Biz.Debug {
		gin.SetMode(gin.ReleaseMode)
	}
	engine := gin.New()
	engine.Use(gin.Recovery())
	if !conf.Conf.Biz.Debug {
		engine.Use(gin.Logger())
	}

	// add support cors
	corsConf := cors.DefaultConfig()
	corsConf.AddAllowHeaders("X-Requested-With")
	corsConf.AllowAllOrigins = true
	engine.Use(cors.New(corsConf))

	// use middleware
	engine.Use(middleware.ApiLogMiddleware)

	// gzip conpression
	engine.Use(gzip.Gzip(gzip.DefaultCompression))

	// update index
	engine.POST("/index/update", func(c *gin.Context) {
		req := struct {
			ContractCode string            `json:"symbol"`
			IndexPrice   string            `json:"price"`
			Platforms    []string          `json:"platforms"`
			FundRateInfo map[string]string `json:"fund_rate_info"`
		}{}
		if err := request.ShouldBindQuery(c, &req); err != nil {
			c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

			return
		}

		if len(req.ContractCode) > 0 && len(req.IndexPrice) > 0 {
			burst.UpdateIndexPrice(req.ContractCode, req.IndexPrice, req.Platforms)
			if burstService, ok := burst.RunningServices.Map[strings.ToUpper(req.ContractCode)]; ok {
				burstService.UpdateSyncFundRate(req.ContractCode, req.FundRateInfo)
			}
		}

		c.JSON(http.StatusOK, response.NewSuccess(""))
	})

	// update index
	engine.GET("/index/push", func(ctx *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				log.Println("IndexPush recover error:", err)
				return
			}
		}()
		conn, err := upGrader.Upgrade(ctx.Writer, ctx.Request, nil)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		defer conn.Close()

		params := struct {
			ContractCode string            `json:"symbol"`
			IndexPrice   string            `json:"price"`
			Platforms    []string          `json:"platforms"`
			FundRateInfo map[string]string `json:"fund_rate_info"`
		}{}

		err = conn.WriteMessage(websocket.TextMessage, []byte(`{"code":200,"msg":"ok"}`))
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}

		for {
			mt, message, err := conn.ReadMessage()
			if err != nil {
				log.Println("read:", err)
				break
			}

			switch mt {
			case websocket.TextMessage:
				err = json.Unmarshal(message, &params)
				if err != nil {
					log.Println("read:", err, string(message))
					continue
				} else {
					// fmt.Println(fmt.Sprintf("params: %+v", params))
					if len(params.ContractCode) > 0 && len(params.IndexPrice) > 0 {
						burst.UpdateIndexPrice(params.ContractCode, params.IndexPrice, params.Platforms)
						if burstService, ok := burst.RunningServices.Map[strings.ToUpper(params.ContractCode)]; ok {
							burstService.UpdateSyncFundRate(params.ContractCode, params.FundRateInfo)
						}
					}
				}

			default:
				fmt.Println(mt, message)
			}

			runtime.Gosched()
		}

		ctx.JSON(http.StatusOK, gin.H{
			"code": http.StatusOK,
			"msg":  "success",
		})
	})

	return engine
}
