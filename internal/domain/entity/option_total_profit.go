package entity

import (
	"fmt"

	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

type OptionTotalProfit struct {
	UID                string          `gorm:"user_id;type:varchar(20);not null"`               // 用户ID
	Currency           string          `gorm:"currency;type:varchar(25);not null"`              // 资产币种
	TotalProfit        decimal.Decimal `gorm:"total_profit" sql:"type:decimal(30,15);"`         // 累计盈利 (累计期权订单已实现盈亏>=0总和)
	TotalLoss          decimal.Decimal `gorm:"total_loss" sql:"type:decimal(30,15);"`           // 累计亏损 (累计期权订单已实现盈亏<0权利金总和)
	TotalProfitPremium decimal.Decimal `gorm:"total_profit_premium" sql:"type:decimal(30,15);"` // 累计盈利订单权利金总和
	Unexercised        decimal.Decimal `gorm:"unexercised" sql:"type:decimal(30,15);"`          // 未行权期权总额

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func NewOptionTotalProfit() *OptionTotalProfit {
	return &OptionTotalProfit{}
}

func (slf *OptionTotalProfit) TableName() string {
	return "option_total_profit"
}

func (slf *OptionTotalProfit) CreateTable(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	tableName := slf.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !db.HasTable(tableName) {
			err := db.Table(tableName).CreateTable(slf).Error
			if err != nil {
				return err
			}
			db.Table(tableName).AddUniqueIndex("idx_profit_loss", "user_id", "currency")
		}
	}
	util.SetNewTableName(tableName)
	return nil
}

func (slf *OptionTotalProfit) GetData(_userId, _currency string) error {
	db, err := sqllib.Db()
	if err != nil {
		return err
	}
	tableName := slf.TableName()
	err = db.Table(tableName).Where("user_id = ? and currency = ?", _userId, _currency).Find(slf).Error
	return err
}

// UpsertTotalProfit insert or update swap pos
func (slf *OptionTotalProfit) UpsertTotalProfit(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	insertColumns := "`user_id`,`currency`,`total_profit`,`total_loss`,`total_profit_premium`, `unexercised`, `create_time`"
	updateColumns := "`total_profit`=?,`total_loss`=?,`total_profit_premium`=?,`unexercised`=?,`update_time`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		slf.TableName(), insertColumns, updateColumns)
	if err := db.Exec(sql,
		slf.UID, slf.Currency, slf.TotalProfit, slf.TotalLoss, slf.TotalProfitPremium, slf.Unexercised, slf.CreateTime,
		slf.TotalProfit, slf.TotalLoss, slf.TotalProfitPremium, slf.Unexercised, slf.UpdateTime,
	).Error; err != nil {
		return err
	}

	return nil
}
