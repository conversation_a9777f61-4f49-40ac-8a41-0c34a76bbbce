package entity

import (
	"fmt"
	"log"

	"futures-asset/internal/domain"
	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

type Position struct {
	Id              string          `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`                       // 持仓ID
	UID             string          `gorm:"user_id;type:varchar(20);not null" json:"uid"`                     // 用户ID
	UserType        int             `gorm:"user_type;type:SMALLINT;" json:"userType"`                         // 用户类型
	PosSide         int32           `gorm:"pos_side;not null" json:"posSide"`                                 // 方向 (1:多仓 2:空仓)
	Leverage        int             `gorm:"leverage;not null" json:"leverage"`                                // 杠杆倍数
	AccountType     string          `gorm:"account_type;type:varchar(20);" json:"accountType"`                // 合约类型
	ContractCode    string          `gorm:"contract_code;type:varchar(50);not null" json:"contractCode"`      // 合约代码
	Currency        string          `gorm:"currency;type:varchar(25);not null" json:"currency"`               // 资产币种
	Pos             decimal.Decimal `gorm:"pos" sql:"type:decimal(30,15);" json:"pos"`                        // 仓位
	PosAvailable    decimal.Decimal `gorm:"pos_available" sql:"type:decimal(30,15);" json:"posAvailable"`     // 可平仓位
	MarginMode      int32           `gorm:"margin_mode" sql:"type:SMALLINT;" json:"marginMode"`               // 保证金模式 (1:全仓模式 2:追仓模式)
	IsolatedMargin  decimal.Decimal `gorm:"isolated_margin" sql:"type:decimal(30,15);" json:"isolatedMargin"` // 逐仓位的保证金
	OpenPriceAvg    decimal.Decimal `gorm:"open_price_avg" sql:"type:decimal(30,15);" json:"openPriceAvg"`    // 开仓均价
	OpenTime        int64           `gorm:"open_time" json:"openTime"`                                        // 开仓时间
	PosStatus       int32           `gorm:"pos_status" sql:"type:SMALLINT;" json:"posStatus"`                 // 持仓状态(1:持仓中 2:已结束)
	LiquidationType int32           `gorm:"liquidation_type" sql:"type:SMALLINT;" json:"liquidationType"`     // 强平类型
	ProfitReal      decimal.Decimal `gorm:"profit_real" sql:"type:decimal(30,15);" json:"profitReal"`         // 持仓已实现盈亏
	Subsidy         decimal.Decimal `gorm:"subsidy" sql:"type:decimal(30,15);" json:"subsidy"`                // 穿仓补贴金额
	RebornId        string          `gorm:"reborn_id;type:varchar(50);not null" json:"rebornId"`              // 复活卡ID
	TrialMargin     decimal.Decimal `gorm:"trial_margin" sql:"type:decimal(30,15);" json:"trialMargin"`       // 体验金保证金
	AwardOpIds      string          `gorm:"award_op_ids;type:varchar(500);not null" json:"awardOpIds"`        // 奖励操作ID

	MaintenMarginRate decimal.Decimal `gorm:"-" json:"maintenMarginRate"` // 维持保证金率
	MaintenMargin     decimal.Decimal `gorm:"-" json:"maintenMargin"`     // 维持保证金
	MarginRate        decimal.Decimal `gorm:"-" json:"marginRate"`        // 保证金率
	MarginBalance     decimal.Decimal `gorm:"-" json:"marginBalance"`     // 保证金余额
	ReturnRate        decimal.Decimal `gorm:"-" json:"returnRate"`        // 回报率
	ProfitUnreal      decimal.Decimal `gorm:"-" json:"profitUnreal"`      // 未实现盈亏
	Liquidation       decimal.Decimal `gorm:"-" json:"liquidation"`       // 预估强评价
	MarkPrice         decimal.Decimal `gorm:"-" json:"markPrice"`         // 标记价格
	RivalScore        decimal.Decimal `gorm:"-" json:"rivalScore"`        // 对手方强制减仓指数

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func NewPosSwap() *Position {
	return &Position{}
}

func (slf *Position) TableName() string {
	return "position"
}

func (slf *Position) TableNameMonth() string {
	return "position" + util.MonthLayout(slf.OpenTime, util.EnumNanosecond)
}

func (slf *Position) CreateTable(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	tableName := slf.TableName()
	util.SetNewTableName(tableName)
	if !db.HasTable(tableName) {
		err := db.Table(tableName).CreateTable(slf).Error
		if err != nil {
			return err
		}
	} else {
		// db.AutoMigrate(slf)
	}
	util.SetNewTableName(tableName)
	return nil
}

// UpsertPos insert or update swap pos
func (slf *Position) UpsertPos(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	insertColumns := "`id`,`user_id`,`user_type`,`pos_side`,`leverage`," +
		"`account_type`,`contract_code`,`currency`,`pos`,`pos_available`," +
		"`margin_mode`,`isolated_margin`,`open_price_avg`,`open_time`,`pos_status`," +
		"`liquidation_type`,`profit_real`,`subsidy`,`create_time`," +
		"`trial_margin`,`award_op_ids`"

	updateColumns := "`leverage`=?,`pos`=?,`reborn_id`=?,`pos_available`=?,`isolated_margin`=?,`open_price_avg`=?," +
		"`margin_mode`=?,`open_time`=?,`pos_status`=?,`liquidation_type`=?,`profit_real`=`profit_real`+CAST(? AS DECIMAL(30,15))," +
		"`subsidy`=`subsidy`+CAST(? AS DECIMAL(30,15)),`update_time`=?,`trial_margin`=?,`award_op_ids`=?"
	sql := fmt.Sprintf(`INSERT INTO %s (%s) 
		VALUES (
		    ?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,
		    ?,?) ON DUPLICATE KEY UPDATE %s;`,
		slf.TableName(), insertColumns, updateColumns)
	if err := db.Exec(sql,
		slf.Id, slf.UID, slf.UserType, slf.PosSide, slf.Leverage,
		slf.AccountType, slf.ContractCode, slf.Currency, slf.Pos, slf.PosAvailable,
		slf.MarginMode, slf.IsolatedMargin, slf.OpenPriceAvg, slf.OpenTime, slf.PosStatus,
		slf.LiquidationType, slf.ProfitReal, slf.Subsidy, slf.CreateTime,
		slf.TrialMargin, slf.AwardOpIds,

		slf.Leverage, slf.Pos, slf.RebornId, slf.PosAvailable, slf.IsolatedMargin,
		slf.OpenPriceAvg, slf.MarginMode, slf.OpenTime, slf.PosStatus, slf.LiquidationType,
		slf.ProfitReal, slf.Subsidy, slf.UpdateTime, slf.TrialMargin, slf.AwardOpIds,
	).Error; err != nil {
		return err
	}

	return nil
}

// UpdatePosLeverage 更新仓位杠杆倍数
func (slf *Position) UpdatePosLeverage(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	sql := fmt.Sprintf("UPDATE %s SET `leverage`=? WHERE `user_id`=? AND `contract_code`=?", slf.TableName())
	if err := db.Exec(sql, slf.Leverage, slf.UID, slf.ContractCode).Error; err != nil {
		return err
	}

	return nil
}

// UpdateSubsidy 更新穿仓补贴金额
func (slf *Position) UpdateSubsidy(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	sql := fmt.Sprintf("UPDATE %s SET `subsidy`=? WHERE `user_id`=? AND `id`=?", slf.TableName())
	if err := db.Exec(sql, slf.Subsidy, slf.UID, slf.Id).Error; err != nil {
		return err
	}

	return nil
}

// GetPosSwapList 获取持仓信息
func GetPosSwapList(param *PositionSearch, userType, liquidationType []int, isHasPos bool) (int64, []Position, error) {
	// 获取数据和总数
	posList := make([]Position, 0)
	var total int64

	query := fmt.Sprintf("is_deleted=0")
	args := make([]interface{}, 0)
	if param.Id != "" {
		query += fmt.Sprintf(" and id like ?")
		args = append(args, param.Id)
	}
	if param.UID != "" {
		query += fmt.Sprintf(" and user_id=?")
		args = append(args, param.UID)
	}
	if param.Base != "" && param.Quote != "" {
		query += fmt.Sprintf(" and contract_code=?")
		args = append(args, util.ContractCode(param.Base, param.Quote))
	}
	if param.Base == "" && param.Quote != "" {
		query += fmt.Sprintf(" and contract_code like ?")
		args = append(args, "%"+param.Quote)
	}
	if param.Base != "" && param.Quote == "" {
		query += fmt.Sprintf(" and contract_code like ?")
		args = append(args, param.Base+"%")
	}
	if isHasPos {
		query += fmt.Sprintf(" and pos>0")
	}
	if param.PosSide != 0 {
		query += fmt.Sprintf(" and pos_side=?")
		args = append(args, param.PosSide)
	}
	if len(param.AccountType) > 0 {
		query += fmt.Sprintf(" and account_type=?")
		args = append(args, param.AccountType)
	}
	if len(userType) > 0 {
		query += fmt.Sprintf(" and user_type in (?)")
		args = append(args, userType)
	}
	if param.StartTime > 0 {
		query += fmt.Sprintf(" and open_time>=?")
		args = append(args, param.StartTime*1e9)
	}
	if param.EndTime > 0 {
		query += fmt.Sprintf(" and open_time<=?")
		args = append(args, param.EndTime*1e9)
	}
	if param.PosStatus > 0 {
		query += fmt.Sprintf(" and pos_status=?")
		args = append(args, param.PosStatus)
	}
	if param.MarginMode > 0 {
		query += fmt.Sprintf(" and margin_mode=?")
		args = append(args, param.MarginMode)
	}
	if len(liquidationType) > 0 {
		query += fmt.Sprintf(" and liquidation_type in (?)")
		args = append(args, liquidationType)
	}

	tmp := &Position{}
	coinSelect := sqllib.SelectSql{
		Query:  query,
		Args:   args,
		Page:   param.PageNum,
		Size:   param.PageSize,
		Obj:    &posList,
		Select: fmt.Sprintf("*"),
		Total:  &total,
		Order:  fmt.Sprintf("%s, %s %s", "pos_status", "create_time", sqllib.OrderMap[sqllib.OrderDesc]),
		Table:  tmp.TableName(),
		Model:  &Position{},
	}
	if err := sqllib.GetMysqlListAndTotal(&coinSelect); err != nil {
		return 0, posList, err
	}

	return total, posList, nil
}

// GetPosTotal 获取持仓总仓位
func GetPosTotal(db *gorm.DB, base, quote string) (decimal.Decimal, decimal.Decimal) {
	shortTotal := decimal.Zero
	longTotal := decimal.Zero
	if base == "" || quote == "" {
		return shortTotal, longTotal
	}

	var err error
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			log.Printf("GetPosTotal get db err:%s", err)
			return shortTotal, longTotal
		}
	}

	pos := Position{}
	rows, err := db.Table(pos.TableName()).Select("pos_side, sum(pos) as total").
		Where("contract_code=?", util.ContractCode(base, quote)).
		Group("pos_side").Rows()
	if err != nil {
		log.Printf("GetPosTotal Group Rows err:%s", err)
		return shortTotal, longTotal
	}
	defer rows.Close()
	for rows.Next() {
		posSide := 0
		total := decimal.Zero
		if err := rows.Scan(&posSide, &total); err != nil {
			log.Printf("GetPosTotal rows.Next() scan err:%s", err)
			continue
		}
		if int32(posSide) == domain.LongPos {
			longTotal = total
		} else {
			shortTotal = total
		}
	}

	return shortTotal, longTotal
}

// GetPosSwap 获取持仓详情
func GetPosSwap(db *gorm.DB, posId string) (pos Position, err error) {
	pos = Position{}
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return pos, err
		}
	}

	if err := db.Where("id = ?", posId).First(&pos).Error; err != nil {
		return pos, err
	}

	return pos, nil
}

// DeleteRecords 根据条件删除记录
func (slf *Position) DeleteRecords(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	sql := fmt.Sprintf("DELETE FROM `%s` WHERE `create_time`<? AND `user_type`=? AND `pos_status`=?;", slf.TableName())
	if err := db.Exec(sql, slf.CreateTime, domain.UserTypePlatformRobot, domain.PosStatusEnd).Error; err != nil {
		return err
	}

	return nil
}

type PosPartInfo struct {
	Id string `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`
}

func GetPosPartInfo(awardOpId string) ([]*PosPartInfo, error) {
	posPartInfos := make([]*PosPartInfo, 0)
	sqlStr := "SELECT id FROM pos_swap WHERE award_op_ids LIKE " + "'%" + awardOpId + "%'"
	err := sqllib.Raw(sqlStr).Scan(&posPartInfos).Error

	return posPartInfos, err
}
