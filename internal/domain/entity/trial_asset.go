package entity

import (
	"encoding/json"
	"fmt"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

type TrialAsset struct {
	Id               int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	Type             int8            `gorm:"type;type:tinyint(1);not null;default:0" json:"type"`               // 体验金类型, 1: 循环体验金, 2: 一次性体验金
	MaxLeverage      int16           `gorm:"max_leverage;type:smallint;not null;default:0" json:"max_leverage"` // 最大杠杆倍数
	MinHoldTime      int64           `gorm:"min_hold_time;not null;default:0" json:"min_hold_time"`             // 最小持仓时间
	UID              string          `gorm:"user_id;type:varchar(20);not null;" json:"uid"`                     // 用户ID
	Currency         string          `gorm:"currency;type:varchar(25);not null;" json:"currency"`               // 资产币种
	ActivityId       int64           `gorm:"activity_id" json:"activity_id"`                                    // 活动id
	AwardOpId        string          `gorm:"award_op_id;type:varchar(20);not null;" json:"award_op_id"`         // 体验金领取的唯一id
	AwardAmount      decimal.Decimal `gorm:"award_amount" sql:"type:decimal(30,15);" json:"award_amount"`       // 领取的体验金数量
	RecycleAmount    decimal.Decimal `gorm:"recycle_amount" sql:"type:decimal(30,15);" json:"recycle_amount"`   // 体验金剩余数量
	RecoveryAmount   decimal.Decimal `gorm:"recovery_amount" sql:"type:decimal(30,15);" json:"recovery_amount"` // 体验金回收数量
	LossAmount       decimal.Decimal `gorm:"loss_amount" sql:"type:decimal(30,15);" json:"loss_amount"`         // 体验金亏损数量
	OpenAmount       decimal.Decimal `gorm:"open_amount" sql:"type:decimal(30,15);" json:"open_amount"`         // 体验金开仓数量, 保证金数量
	LockAmount       decimal.Decimal `gorm:"lock_amount" sql:"type:decimal(30,15);" json:"lock_amount"`         // 体验金锁仓数量
	AmountUsed       decimal.Decimal `gorm:"amount_used" sql:"type:decimal(30,15);" json:"amount_used"`         // 体验金已用数量, 包含亏损，回收，手续费等
	OpenCount        int32           `gorm:"open_count" json:"open_count"`                                      // 体验金开仓次数
	InvalidTime      int64           `gorm:"invalid_time" json:"invalid_time"`                                  // 失效时间
	EffectiveTime    int64           `gorm:"effective_time" json:"effective_time"`                              // 生效时间
	StatusTime       int64           `gorm:"status_time" json:"status_time"`                                    // 最后状态变更时间
	WarningTime      int64           `gorm:"warning_time" json:"warning_time"`                                  // 预警时间
	AwardTime        int64           `gorm:"award_time" json:"award_time"`                                      // 领取时间
	TrialAssetStatus int             `gorm:"trial_asset_status" json:"trial_asset_status"`                      // 体验金状态
	RecycleOpId      string          `gorm:"recycle_op_id;type:varchar(20);not null;" json:"recycle_op_id"`     // 体验金回收的唯一id
	OpType           int             `gorm:"-" json:"op_type"`                                                  // 操作类型 同步数据库中要用，不需要存库
	Amount           decimal.Decimal `gorm:"-" json:"amount"`                                                   // 资金变化数量 推给财务的时候使用
	DisplayStatus    int8            `gorm:"-" json:"display_status"`                                           // 体验金显示状态
	ProfitUnreal     decimal.Decimal `gorm:"-" json:"profit_unreal"`                                            // 未实现盈亏

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

const (
	TrialAssetNoReceived     = 0 // 未收到, 或者筛选条件全部
	TrialAssetStatusNoEffect = 1 // 待生效
	TrialAssetStatusEffect   = 2 // 生效中
	TrialAssetStatusWarn     = 3 // 已预警
	TrialAssetStatusInvalid  = 4 // 已失效
	TrialAssetStatusFinish   = 5 // 体验金完全使用
)

func (slf *TrialAsset) UpdateDisplayStatus() int8 {
	// 如果状态是可用
	if slf.TrialAssetStatus == domain.TrialAssetStatusEffect || slf.TrialAssetStatus == domain.TrialAssetStatusWarn {
		if slf.OpenAmount.IsPositive() {
			return domain.TrialAssetDisplayStatusUsing
		}

		return domain.TrialAssetDisplayStatusAvailable
	} else if slf.TrialAssetStatus == domain.TrialAssetStatusInvalid { // 无效的分为两种, 回收跟过期
		if slf.Type == domain.TrialAssetTypeOnce && slf.AmountUsed.IsPositive() {
			return domain.TrialAssetDisplayStatusRecycled
		}

		return domain.TrialAssetDisplayStatusExpired
	}

	if slf.TrialAssetStatus == domain.TrialAssetStatusNoEffect {
		return domain.TrialAssetDisplayStatusPending
	}

	return domain.TrialAssetDisplayStatusFinished
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *TrialAsset) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *TrialAsset) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

type TrialAssetQuery struct {
	TrialAsset
	orm *gorm.DB
}

func (slf *TrialAsset) TableName() string {
	return "trial_asset"
}

func NewTrialAssetQuery(_orm *gorm.DB) *TrialAssetQuery {
	return &TrialAssetQuery{
		orm: _orm,
	}
}

func (slf *TrialAssetQuery) Orm() *gorm.DB {
	if slf.orm == nil {
		orm, err := sqllib.Db()
		if err != nil {
			logrus.Error(fmt.Sprintf("get orm err:%+v", err))
		}
		slf.orm = orm
	}

	return slf.orm
}

func (slf *TrialAssetQuery) CreateTable() (err error) {
	db := slf.Orm()
	tableName := slf.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !slf.Orm().HasTable(tableName) {
			err := db.Table(tableName).CreateTable(slf).Error
			if err != nil {
				return err
			}
			db.Table(tableName).AddUniqueIndex("idx_award_op_id", "award_op_id")
		} else {
			// err = slf.Orm().AutoMigrate((*TrialAsset)(nil)).Error // 自动迁移，只能针对表比较小的
			// if err != nil {
			// return err
			// }
		}
	}
	util.SetNewTableName(tableName)

	return nil
}

func (slf *TrialAssetQuery) CreateData() error {
	slf.CreateTime = time.Now().UnixNano()
	slf.UpdateTime = time.Now().UnixNano()
	err := slf.Orm().Table(slf.TableName()).Create(&slf.TrialAsset).Error

	return err
}

func (slf *TrialAssetQuery) Update() error {
	slf.UpdateTime = time.Now().UnixNano()
	return slf.Orm().Table(slf.TableName()).Where("award_op_id = ? and trial_asset_status <= ?", slf.AwardOpId, slf.TrialAssetStatus).Updates(&slf.TrialAsset).Error
}

func (slf *TrialAssetQuery) FindDataByAwardOpId(_awardOpId string) error {
	return slf.Orm().Table(slf.TableName()).Where("award_op_id = ?", _awardOpId).Find(&slf.TrialAsset).Error
}

func (slf *TrialAssetQuery) CountDataByAwardOpId(_awardOpId string) (int, error) {
	counter := 0
	err := slf.Orm().Table(slf.TableName()).Where("award_op_id = ?", _awardOpId).Count(&counter).Error
	if err != nil {
		return counter, err
	}

	return counter, nil
}

type TrialStatsData struct {
	TotalAmount    decimal.Decimal // 总金额
	TotalUsed      decimal.Decimal // 总使用
	TotalAvailable decimal.Decimal // 总可用
	TotalPending   decimal.Decimal // 总待生效
	TotalLoss      decimal.Decimal // 总亏损
	TotalLock      decimal.Decimal
	TotalOpen      decimal.Decimal
	TotalCount     int // 总数量
}

func (slf *TrialAssetQuery) GetTrialStatsData(uid string) (res TrialStatsData, err error) {
	sql := `SELECT SUM(award_amount) as total_amount, SUM(amount_used) as total_used, SUM(recycle_amount) as total_available, SUM(loss_amount) as total_loss, SUM(lock_amount) as total_lock, SUM(open_amount) as total_open, COUNT(*) as total_count, `
	sql += `SUM(CASE trial_asset_status WHEN 1 then award_amount ELSE 0 END) as total_pending `
	sql += `FROM trial_asset  WHERE user_id = ` + uid
	err = slf.Orm().Raw(sql).Scan(&res).Error

	return
}

func (slf *TrialAssetQuery) GetTotalAward(_userId string) (decimal.Decimal, error) {
	totalReceived := struct {
		Sum decimal.Decimal `gorm:"column:sum" sql:"type:decimal(35,18);"`
	}{}
	sqlStr := "SELECT SUM(award_amount) as sum FROM " + slf.TableName() + " WHERE user_id = " + _userId
	err := slf.Orm().Raw(sqlStr).Scan(&totalReceived).Error

	return totalReceived.Sum, err
}

func (slf *TrialAssetQuery) GetNoEffective(_userId string) (decimal.Decimal, error) {
	noEffectiveAmount := struct {
		Sum decimal.Decimal `gorm:"column:sum" sql:"type:decimal(35,18);"`
	}{}
	sqlStr := "SELECT SUM(award_amount) as sum FROM " + slf.TableName() + " WHERE trial_asset_status = 1 and user_id = " + _userId
	err := slf.Orm().Raw(sqlStr).Scan(&noEffectiveAmount).Error

	return noEffectiveAmount.Sum, err
}

func (slf *TrialAssetQuery) GetTotalUsed(_userId string) (decimal.Decimal, error) {
	totalUsed := struct {
		Sum decimal.Decimal `gorm:"column:sum" sql:"type:decimal(35,18);"`
	}{}
	sqlStr := "SELECT SUM(amount_used) as sum FROM " + slf.TableName() + " WHERE user_id = " + _userId
	err := slf.Orm().Raw(sqlStr).Scan(&totalUsed).Error

	return totalUsed.Sum, err
}

type TrialAssetSummaryInfo struct {
	UID                 string          `json:"uid" gorm:"column:user_id"`
	UserType            int             `json:"user_type" gorm:"-"`
	ContractHasAgree    int32           `json:"contract_has_agree" gorm:"-"`
	SumAmount           decimal.Decimal `json:"sum_amount" gorm:"column:sum_amount"`                         // 累计获得体验金金额
	CountTicket         int             `json:"count_ticket" gorm:"column:count_ticket"`                     // 累计获得体验金券张数
	SumRemainAmount     decimal.Decimal `json:"sum_remain_amount" gorm:"column:sum_remain_amount"`           // 剩余可用体验金金额
	CountRemainTicket   int             `json:"count_remain_ticket" gorm:"column:count_remain_ticket"`       // 剩余可用体验金券张数
	SumUsingAmount      decimal.Decimal `json:"using_amount" gorm:"column:sum_using_amount"`                 // 使用中体验金金额
	CountUsingTicket    int             `json:"count_using_ticket" gorm:"column:count_using_ticket"`         // 使用中体验金券张数
	SumOverdueAmount    decimal.Decimal `json:"sum_overdue_amount" gorm:"column:sum_overdue_amount"`         // 已过期体验金金额
	CountOverdueTicket  int             `json:"count_overdue_ticket" gorm:"column:count_overdue_ticket"`     // 已过期体验金券张数
	SumRecycleAmount    decimal.Decimal `json:"sum_recycle_amount" gorm:"column:sum_recycle_amount"`         // 已回收体验金金额
	CountRecycleTicket  int             `json:"count_recycle_ticket" gorm:"column:count_recycle_ticket"`     // 已回收体验金券张数
	SumSpendAllAmount   decimal.Decimal `json:"sum_spend_all_amount" gorm:"column:sum_spend_all_amount"`     // 已完成体验金金额
	CountSpendAllTicket int             `json:"count_spend_all_ticket" gorm:"column:count_spend_all_ticket"` // 已完成体验金券张数
}

type TrialAssetDetailInfo struct {
	StatusTime         int64           `json:"status_time" gorm:"column:create_time"` // 领取时间
	GetWay             int             `json:"get_way" gorm:"-"`
	TrialTicketType    int8            `json:"trial_ticket_type" gorm:"column:type"`                      // 体验金券类型
	ActivityId         int64           `json:"activity_id" gorm:"column:activity_id"`                     // 活动Id
	TrialTicketOrderId string          `json:"trial_ticket_order_id" gorm:"column:award_op_id"`           // 体验金券码
	TrialAmount        decimal.Decimal `json:"trial_amount" gorm:"column:award_amount"`                   // 体验金金额
	TrialRecycleAmount decimal.Decimal `json:"trial_recycle_amount" gorm:"column:recovery_amount"`        // 体验金回收金额
	OpenCount          int32           `json:"open_count" gorm:"column:open_count"`                       // 开仓次数
	TrialAssetStatus   int             `json:"trial_asset_status" gorm:"column:trial_asset_status"`       // 体验金状态
	PosOrderIds        []string        `json:"pos_order_id" gorm:"-"`                                     // 持仓单号
	OpenAmount         decimal.Decimal `gorm:"open_amount" sql:"type:decimal(30,15);" json:"open_amount"` // 体验金开仓数量, 保证金数量
	AmountUsed         decimal.Decimal `gorm:"amount_used" sql:"type:decimal(30,15);" json:"amount_used"` // 体验金已用数量, 包含亏损，回收，手续费等
}

func (tad *TrialAssetDetailInfo) UpdateDisplayStatus() int {
	// 如果状态是可用
	if tad.TrialAssetStatus == domain.TrialAssetStatusEffect || tad.TrialAssetStatus == domain.TrialAssetStatusWarn {
		if tad.OpenAmount.IsPositive() {
			return domain.TrialAssetDisplayStatusUsing
		}

		return domain.TrialAssetDisplayStatusAvailable
	} else if tad.TrialAssetStatus == domain.TrialAssetStatusInvalid { // 无效的分为两种, 回收跟过期
		if tad.TrialTicketType == domain.TrialAssetTypeOnce && tad.AmountUsed.IsPositive() {
			return domain.TrialAssetDisplayStatusRecycled
		}

		return domain.TrialAssetDisplayStatusExpired
	}

	if tad.TrialAssetStatus == domain.TrialAssetStatusNoEffect {
		return domain.TrialAssetDisplayStatusPending
	}

	return domain.TrialAssetDisplayStatusFinished
}
