package entity

import (
	"encoding/json"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

// RivalBurst contract swap burst log
type RivalBurst struct {
	Id          int64             `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	BurstId     string            `gorm:"burst_id;type:varchar(30);not null"`                         // 爆仓ID
	UID         string            `gorm:"user_id;type:varchar(20);not null"`                          // 用户ID
	RivalUserId string            `gorm:"rival_user_id;type:varchar(20);not null"`                    // 对手用户ID
	Base        string            `gorm:"base;type:varchar(10);not null"`                             // 交易币
	Quote       string            `gorm:"quote;type:varchar(10);not null"`                            // 计价币
	Currency    string            `gorm:"currency;type:varchar(10);not null"`                         // 资产币种
	PosType     int               `gorm:"pos_type;type:int(5);not null"`                              // 仓位类型 多:1 空:2
	TradePrice  decimal.Decimal   `gorm:"trade_price" sql:"type:decimal(30,15);"`                     // 成交价
	TradeAmount decimal.Decimal   `gorm:"trade_amount" sql:"type:decimal(30,15);"`                    // 成交数量
	Level       int               `gorm:"level;type:int(5);not null"`                                 // 风险等级
	MarginMode  domain.MarginMode `gorm:"margin_mode"`                                                // 保证金模式
	BurstTime   int64             `gorm:"burst_time"`                                                 // 强平时间
	Status      int               `gorm:"status"`                                                     // 状态
	CreateTime  int64             `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"` // 创建时间
}

func CreateRivalBurstSwapTable() error {
	db, err := sqllib.Db()
	if err != nil {
		return err
	}

	rivalBurst := &RivalBurst{}
	tableName := rivalBurst.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !db.HasTable(tableName) {
			err := db.Table(tableName).CreateTable(rivalBurst).Error
			if err != nil {
				return err
			}
		}
	}
	util.SetNewTableName(tableName)
	return nil
}

// TableName get bill swap table name
func (slf *RivalBurst) TableName() string {
	return "rival_burst_" + util.DayLayout(time.Now().UnixNano(), util.EnumNanosecond)
}

// Insert insert swap bill
func (slf *RivalBurst) Insert(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	tableName := slf.TableName()
	if !db.HasTable(tableName) {
		err := db.Table(tableName).CreateTable(slf).Error
		if err != nil {
			return err
		}
	}

	if err := db.Table(tableName).Create(slf).Error; err != nil {
		return err
	}
	return nil
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *RivalBurst) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *RivalBurst) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
