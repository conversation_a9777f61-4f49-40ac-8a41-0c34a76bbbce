package entity

import (
	"fmt"

	"github.com/shopspring/decimal"
)

type ProfitLoss struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	UID         string          `gorm:"user_id;type:varchar(20);not null"`      // 用户ID
	Currency    string          `gorm:"currency;type:varchar(25);not null"`     // 资产币种
	NetIn       decimal.Decimal `gorm:"net_in" sql:"type:decimal(30,15);"`      // 单日净转入
	ProfitLoss  decimal.Decimal `gorm:"profit_loss" sql:"type:decimal(30,15);"` // 当日盈亏
	OperateTime int64           `gorm:"operate_time"`                           // 统计日的0：0：0 的时间戳

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *ProfitLoss) TableName() string {
	return "profit_loss"
}

func (slf *ProfitLoss) CreateTableWithName(_db *gorm.DB, _tableName string) (err error) {
	if _db == nil {
		_db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	if ok := util.TableIsExit(_tableName); !ok {
		if !_db.HasTable(_tableName) {
			err := _db.Table(_tableName).CreateTable(slf).Error
			if err != nil {
				logrus.Error(fmt.Sprintf("create table err.tableName:%s", _tableName))
				return err
			}
			err = _db.Table(_tableName).AddUniqueIndex("idx_profit_loss", "user_id", "currency", "operate_time").Error
			if err != nil {
				logrus.Error(err)
			}
		}
	}
	util.SetNewTableName(_tableName)
	return nil
}

// UpsertProfitLoss insert or update user profit and loss
func (slf *ProfitLoss) UpsertProfitLoss(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	tableName := slf.TableName()
	_ = slf.CreateTableWithName(db, tableName)

	insertColumns := "`user_id`, `currency`, `net_in`,`profit_loss`,`operate_time`,`create_time`"
	updateColumns := "`net_in`=?,`profit_loss`=?,`update_time`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		slf.TableName(), insertColumns, updateColumns)
	if err := db.Exec(sql,
		slf.UID, slf.Currency, slf.NetIn, slf.ProfitLoss, slf.OperateTime, slf.CreateTime,
		slf.NetIn, slf.ProfitLoss, slf.UpdateTime,
	).Error; err != nil {
		return err
	}

	return nil
}

func (slf *ProfitLoss) GetData() error {
	db, err := sqllib.Db()
	if err != nil {
		return err
	}
	tableName := slf.TableName()
	err = db.Table(tableName).Where("user_id = ? and currency = ? and operate_time = ?",
		slf.UID, slf.Currency, slf.OperateTime).Find(slf).Error
	return err
}
