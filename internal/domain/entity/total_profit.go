package entity

import (
	"fmt"

	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

type TotalProfit struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	UID         string          `gorm:"user_id;type:varchar(20);not null"`       // 用户ID
	Currency    string          `gorm:"currency;type:varchar(25);not null"`      // 资产币种
	TotalProfit decimal.Decimal `gorm:"total_profit" sql:"type:decimal(30,15);"` // 累计盈亏
	Subsidy     decimal.Decimal `gorm:"subsidy" sql:"type:decimal(30,15);"`      // 穿仓补贴数量
	CreateTime  int64           `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime  int64           `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *TotalProfit) TableName() string {
	return "total_profit"
}

func (slf *TotalProfit) CreateTable(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	tableName := slf.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !db.HasTable(tableName) {
			err := db.Table(tableName).CreateTable(slf).Error
			if err != nil {
				return err
			}
			db.Table(tableName).AddUniqueIndex("idx_profit_loss", "user_id", "currency")
		}
	}
	util.SetNewTableName(tableName)
	return nil
}

func (slf *TotalProfit) GetData(_userId, _currency string) error {
	db, err := sqllib.Db()
	if err != nil {
		return err
	}
	tableName := slf.TableName()
	err = db.Table(tableName).Where("user_id = ? and currency = ?", _userId, _currency).Find(slf).Error
	return err
}

// UpsertTotalProfit UpsertPos insert or update swap pos
func (slf *TotalProfit) UpsertTotalProfit(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	insertColumns := "`user_id`,`currency`,`total_profit`, `subsidy`, `create_time`"

	updateColumns := "`total_profit`=?, `subsidy` = ?, `update_time`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		slf.TableName(), insertColumns, updateColumns)
	if err := db.Exec(sql,
		slf.UID, slf.Currency, slf.TotalProfit, slf.Subsidy, slf.CreateTime,
		slf.TotalProfit, slf.Subsidy, slf.UpdateTime,
	).Error; err != nil {
		return err
	}

	return nil
}

func (slf *TotalProfit) TotalSubsidy() (err error) {
	db, err := sqllib.Db()
	if err != nil {
		return err
	}
	tableName := slf.TableName()
	err = db.Table(tableName).Select("sum(subsidy) as subsidy").Find(slf).Error
	return err
}
