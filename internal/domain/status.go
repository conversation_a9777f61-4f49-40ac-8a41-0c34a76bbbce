package domain

import (
	"errors"
	"strconv"
)

type Code int

func (c Code) ToString() string {
	return strconv.Itoa(int(c))
}

func (c Code) ToInt() int {
	return int(c)
}

const (
	CodeOk           = Code(102000)
	InternalError    = Code(255000)
	CodeExists       = Code(254001)
	CodeNotFound     = Code(254040)
	CodeParamInvalid = Code(254000) // 参数错误

	// 2500xx
	Code250002 = Code(250002) // 从Setting获取合约币对配置失败
	Code250005 = Code(250005) // 开仓数大于可开仓数
	Code250006 = Code(250006) // 签名错误
	Code250007 = Code(250007) // 仓位只能是多仓或空仓
	Code250009 = Code(250009) // 联合保证金 汇率错误
	Code250008 = Code(250008) // 普通用户领取了体验金禁止其API下单
	Code250010 = Code(250010) // 报表导出错误

	// 杠杆倍数-2510xx
	Code251001 = Code(251001) // 调整杠杆倍数-用户有未完成的委托单
	Code251002 = Code(251002) // 调整杠杆倍数-仓位保证金不足 (杠杆倍数过低, 没有足够的可用保证金可以追加, 请重新调整杠杆倍数)
	Code251003 = Code(251003) // 未开通合约或未同意合约协议
	Code251004 = Code(251004) // 调整杠杆倍数-保证金率小于或者等于初始保证金率时, 不能调小杠杆倍数
	Code251005 = Code(251005) // 调整杠杆倍数-逐仓不能调小杠杆倍数
	Code251006 = Code(251006) // 调整杠杆倍数-超过当前持仓仓位的最大杠杆倍数 不能调整
	Code251007 = Code(251007) // 调整杠杆倍数-杠杆倍数超过币对可调整最大杠杆倍数
	Code251008 = Code(251008) // 调整杠杆倍数-失败
	Code251020 = Code(251020) // 调整仓位模式-当前合约存在持仓或挂单，不支持调整保证金模式
	Code251021 = Code(251021) // 调整仓位模式-用户仓位模式错误
	Code251022 = Code(251022) // 调整仓位模式-失败
	Code251023 = Code(251023) // 调整杠杆倍数和仓位模式-联合保证金 仓位模式不能修改为逐仓
	Code251024 = Code(251024) // 调整杠杆倍数和仓位模式-联合保证金 仓位模式不能修逐仓杠杆倍数
	Code251025 = Code(251025) // 调整杠杆倍数时保证金模式错误
	Code251026 = Code(251026) // 调整杠杆倍数和仓位模式-持仓模式错误
	Code251027 = Code(251027) // 调整杠杆倍数和仓位模式-单向持仓不能修改逐仓多仓杠杆倍数和逐仓空仓杠杆倍数
	Code251028 = Code(251028) // 调整杠杆倍数和仓位模式-双向持仓不能修改逐仓单向持仓的杠杆倍数
	Code251029 = Code(251029) // 调整杠杆倍数和仓位模式-联合保证金 全仓 强后杠杆倍数相等不用调整
	Code251030 = Code(251030) // 一键修改全仓失败-逐仓有仓位或更新缓存杠杆倍数失败

	// 2511xx
	Code251101 = Code(251101) // Redis 添加用户锁失败
	Code251102 = Code(251102) // Redis 获取空仓仓位失败
	Code251103 = Code(251103) // Redis 获取多仓仓位失败
	Code251104 = Code(251104) // Redis 更新空仓仓位失败
	Code251105 = Code(251105) // Redis 更新多仓仓位失败
	Code251106 = Code(251106) // Redis 开空仓失败
	Code251107 = Code(251107) // Redis 开多仓失败
	Code251108 = Code(251108) // Redis 平空仓失败
	Code251109 = Code(251109) // Redis 平多仓失败
	Code251110 = Code(251110) // Redis 可平空仓仓位不足
	Code251111 = Code(251111) // Redis 可平多仓仓位不足
	Code251112 = Code(251112) // Redis 获取币对是否结算中状态失败
	Code251113 = Code(251113) // Redis 获取标记价格失败
	Code251114 = Code(251114) // Redis 获取用户杠杆倍数失败
	Code251115 = Code(251115) // Redis 冻结下单的杠杆倍数与用户设置不匹配
	Code251116 = Code(251116) // Redis 冻结仓位价值扣减不足
	Code251117 = Code(251117) // Redis 下单的仓位模式不匹配
	Code251118 = Code(251118) // Redis 解锁仓位超过持有仓位数(冻结仓位大于持有仓位)
	Code251119 = Code(251119) // Redis 暗成交减仓时可平仓位不足
	Code251120 = Code(251120) // Redis 更新空仓+多仓仓位失败
	Code251121 = Code(251121) // Redis 获取所有标记价格出错
	Code251122 = Code(251122) // Redis 更新用户资产及仓位失败
	Code251123 = Code(251123) // Redis 更新单向持仓仓位失败
	Code251124 = Code(251124) // 持仓模式错误
	Code251125 = Code(251125) // Redis 可平单向仓位不足

	// 2520xx
	Code252001 = Code(252001) // 逐仓追加或减少保证金-仓位保证金不足
	Code252002 = Code(252002) // Redis 可用资金不足
	Code252003 = Code(252003) // Redis 操作后账户余额小于0

	// 2522xx
	Code252200 = Code(252200) // 币对正在结算中
	Code252201 = Code(252201) // 计算解冻资金错误
	Code252202 = Code(252202) //
	Code252203 = Code(252203) //

	// 2523xx 盈亏记录
	ErrUserIdNull   = Code(252300) // 用户id 为空
	ErrDayParam     = Code(252301) // 时间类型错误
	ErrTimeParamErr = Code(252302) // 时间范围错误
	ErrGetPLTrade   = Code(252303) // 获取盈亏记录错误

	// 2523xx 体验金
	ErrOpIdNull          = Code(252350) // 手动回收体验金 领取id 错误
	ErrCurrencyErr       = Code(252351) // 币种不能为空
	ErrRecycleIdNull     = Code(252352) // 回收id 没有
	ErrOpIdNotFind       = Code(252353) // 在当前生效的体验金中没有找到对应的领取id
	TrialFinishOrInvalid = Code(252354) // 体验金已失效或者已完全使用
	ErrUserBurst         = Code(252355) // 爆仓中
	ErrPageErr           = Code(252356) // pageNum or pageSize is 0
	ErrActivityIdErr     = Code(252357) // activity id = 0
	ErrValType           = Code(252358) // 估值类型错误
	ErrTrialAssetNotFind = Code(252359) // 没有要回收的体验金
	ErrGetAllCfg         = Code(252360) // 从redis 获取所有合约配置错误
	ErrMaxPos            = Code(252361) // 开的仓位大于当前杠杆倍数可开仓位
	ErrRedisGetCfg       = Code(252362) // 获取合约配置错误
	ErrRedisData         = Code(252363) // 从redis中获取的配置数据错误
	ErrPlatPosList       = Code(252364) // 获取合约持仓列表错误
	ErrPlatPosDetail     = Code(252365) // 获取合约持仓详情错误
	ErrEsSearch          = Code(252366) // es 查询错误
	ErrSqlErr            = Code(252367) // sql err

	// 2524xx
	Code252404     = Code(252404) // Redis 获取资产失败
	Code252405     = Code(252405) // Redis 操作可用或冻结资产失败
	RedisUpdateErr = Code(252406) // redis 数据更新失败
	Code252407     = Code(252407) // 获取资金异常
	Code252408     = Code(252408) // 获取资金异常
	Code252409     = Code(252409) // 获取汇率异常

	// 2525xx 爆仓记录
	Code252500 = Code(252500) // 服务错误

	UserHoldMoldErr            = Code(252501) // Redis 获取仓位模式失败
	HoldModeHavePos            = Code(252502) // 调整持仓模式-存在仓位
	HoldModeHaveFrozen         = Code(252503) // 调整持仓模式-存在冻结
	HoldModeRedisErr           = Code(252504) // 调整持仓模式-失败
	JoinMarginErr              = Code(252505) // 获取保证金模式失败-失败
	ChangeJoinMarginHaveFrozen = Code(252506) // 调整保证金模式失败-有冻结
	ChangeJoinMarginHavePos    = Code(252507) // 调整保证金模式-存在仓位
	ChangeJoinMarginRedisErr   = Code(252508) // 调整保证金模式失败-redis失败
	OrderConfirmErr            = Code(252509) // 获取二次确认模式失败-失败
	ChangeOrderConfirmErr      = Code(252510) // 调整二次确认模式失败-通用失败
	ChangeOrderConfirmRedisErr = Code(252511) // 调整二次确认模式失败-redis失败
	OrderConfigErr             = Code(252512) // 获取交易设置失败-失败
	ChangeOrderConfigErr       = Code(252513) // 调整交易设置失败-通用失败
	ChangeOrderConfigRedisErr  = Code(252514) // 调整交易设置失败-redis失败
	Code252515                 = Code(252515) // 调整保证金模式失败-有逐仓

	// 2526xx 体验金相关
	TrialOrderTypeErr             = Code(252601) // 体验金订单类型错误
	TrialMarginModeErr            = Code(252602) // 体验金持仓模式错误，比如位全仓
	TrialHoldModeErr              = Code(252603) // 体验金保证金模式错误，比如位单向持仓
	TrialMaxLeverageErr           = Code(252604) // 体验金杠杆倍数错误, msg=最大杠杆倍数为
	TrialMinHoldTimeErr           = Code(252605) // 体验金最小持仓时间错误, msg=剩余可平仓时间为
	TrialOpIdErr                  = Code(252606) // 体验金不存在
	TrialInsufficientErr          = Code(252607) // 体验金不足
	TrialSubMarginErr             = Code(252608) // 体验金不能减少保证金
	TrialHaveLongShortPositionErr = Code(252609) // 体验金不能同时开多仓和空仓

	// 2530xx 期权相关
	ErrLoadOption    = Code(253000) // Redis获取期权失败
	ErrLoadDemoAsset = Code(253001) // Redis获取模拟盘资金错误
	ErrUpdateOption  = Code(253002) // Redis更新取期权失败
	ErrOptionAsset   = Code(253003) // 获取期权资产失败
)

var ErrLockPos = errors.New("lock pos err")
var ErrInsufficientOpenBuy = errors.New("open buy insufficient funds available")    // 可用资金不足
var ErrInsufficientOpenSell = errors.New("open sell insufficient funds available")  // 可用资金不足
var ErrInsufficientBoth = errors.New("both insufficient funds available")           // 可用资金不足
var ErrInsufficientPos = errors.New("open pos greater than pos that can be opened") // 开仓数大于可开仓数
var ErrOrderOrPos = errors.New("has order or pos")                                  // 当前合约存在持仓或挂单，不支持调整保证金模式

var ErrMsg = map[Code]string{
	ErrSqlErr:            "sql err",
	Code251121:           "get all mark price err",
	ErrEsSearch:          "es search error",
	CodeParamInvalid:     "param err",
	ErrPlatPosDetail:     "get plat pos detail err",
	ErrPlatPosList:       "get plat pos list err",
	Code251003:           "Not agree contract",
	ErrRedisData:         "redis data err",
	ErrRedisGetCfg:       "get contract cfg redis err",
	ErrMaxPos:            "lever can not open pos",
	ErrGetAllCfg:         "get all contract cfg by redis err",
	ErrTrialAssetNotFind: "user not have no invalid trial asset",
	ErrValType:           "val type err",
	Code251002:           "leverage to low",
	Code251006:           "over max leverage",
	Code251005:           "Cannot be adjusted down leverage",
	ErrActivityIdErr:     "activity id = 0",
	ErrPageErr:           "pageNum or pageSize is 0",
	TrialFinishOrInvalid: "trial asset finish or invalid",
	ErrUserBurst:         "user in bursting",
	ErrOpIdNotFind:       "op id not find in used",
	ErrRecycleIdNull:     "Recycle id null",
	Code252404:           "get user asset err",
	Code251101:           "lock pos err",
	ErrCurrencyErr:       "currency null",
	ErrOpIdNull:          "op id is null",
	ErrUserIdNull:        "user id must not null",
	ErrDayParam:          "day param type err",
	ErrTimeParamErr:      "startTime or endTime err",
	ErrGetPLTrade:        "get profit loss data sql err",
}
