package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
)

// mockgen -source./asset.go -destination=../../mock/usecase/
//
//nolint:interfacebloat
type AssetUseCase interface {
	UserAsset(ctx context.Context, param *repository.SwapParam) (*repository.AssetSwap, error)
	BatchUserAsset(ctx context.Context, param AssetParam) ([]repository.BatchAssetSwap, error)
	GetUserAssetAndPos(ctx context.Context, param *repository.SwapParam) (domain.Code, repository.ReqUserAssetAndPos)
	SumUserTotalAsset(ctx context.Context) (repository.ResSumUserTotalAsset, error)
	TotalBalance(ctx context.Context, param ReqTotalBalance) (domain.Code, repository.ResTotalBalance)
	AssetDetail(ctx context.Context, param ReqAssetDetail) (domain.Code, repository.ResAssetDetail)
}

type AssetParam struct {
	UserIds  []string `json:"userIds"`
	Currency string   `json:"currency"`
}

type ReqTotalBalance struct {
	UID      string `json:"uid"`
	Currency string `json:"currency"` // 折合的币种
}

type ReqAssetDetail struct {
	UID string `json:"uid"`
}
