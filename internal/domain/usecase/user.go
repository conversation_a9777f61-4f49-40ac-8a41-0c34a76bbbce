package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
)

// mockgen -source./user.go -destination=../../mock/usecase/
//
//nolint:interfacebloat
type UserUseCase interface {
	// AdjustCross 一键切换到全仓模式
	AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error)

	// AdjustLeverage 调整杠杆倍数
	AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, error)

	// AdjustLeverageMargin 调整杠杆和保证金
	AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error)

	// AdjustMargin 调整保证金
	AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error)

	// AdjustMarginCfg 调整保证金配置
	AdjustMarginCfg(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error)

	// AdjustHoldMode 调整持仓模式
	AdjustHoldMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error)

	// ChangeJoinMargin 修改参与保证金
	ChangeJoinMargin(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error)

	// ChangeOrderConfirm 修改下单确认
	ChangeOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error)

	// ChangeOrderConfig 修改订单配置
	ChangeOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error)

	// LoadJoinMargin 获取参与保证金
	LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error)

	// GetOrderConfirm 获取下单确认
	GetOrderConfirm(ctx context.Context, param *repository.CommonParam) (repository.OrderConfirmRes, error)

	// GetOrderConfig 获取订单配置
	GetOrderConfig(ctx context.Context, param *repository.CommonParam) (repository.OrderConfigRes, error)

	// GetUserAsset 获取用户资产
	GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset)

	// SwapInit 初始化用户合约账户
	SwapInit(ctx context.Context, param *repository.SwapInit) domain.Code

	// UserHoldMode 获取用户持仓模式
	UserHoldMode(ctx context.Context, param *repository.CommonParam) (repository.HoldModeRes, error)

	// UserLeverage 获取用户配置的合约币对杠杆倍数
	UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error)

	GetUserOpenCloseTimes(ctx context.Context, req *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error)

	GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error)
}
