package usecase

import (
	"context"

	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
)

type PositionUseCase interface {
	UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error)
	QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error)
	PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error)
	PosTotal(ctx context.Context, contractCode string) decimal.Decimal
	UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error)

	PlatPosList(ctx context.Context) (repository.PlatPosList, error)
	PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error)
}
