package usecase

import (
	"fmt"
	"net/http"

	"futures-asset/internal/domain"

	"google.golang.org/grpc/codes"
)

type ServiceError interface {
	Error() string
	ErrorCode() domain.Code
	HTTPStatusCode() int
	GRPCStatusCode() codes.Code
	ErrorParam() interface{}
}

type NoneErrorParam struct{}

func (e NoneErrorParam) ErrorParam() interface{} {
	return nil
}

type InternalError struct {
	Err error

	NoneErrorParam
}

func (e InternalError) Error() string {
	return fmt.Sprintf("internal error: %v", e.Err)
}

func (e InternalError) ErrorCode() domain.Code {
	return domain.InternalError
}

func (e InternalError) HTTPStatusCode() int {
	return http.StatusOK
}

func (e InternalError) GRPCStatusCode() codes.Code {
	return codes.Internal
}

type ParamInvalidError struct {
	Msg string

	NoneErrorParam
}

func (e ParamInvalidError) Error() string {
	return e.Msg
}

func (e ParamInvalidError) ErrorCode() domain.Code {
	return domain.CodeParamInvalid
}

func (e ParamInvalidError) HTTPStatusCode() int {
	return http.StatusOK
}

func (e ParamInvalidError) GRPCStatusCode() codes.Code {
	return codes.InvalidArgument
}

type NotFoundError struct {
	NoneErrorParam
}

func (e NotFoundError) Error() string {
	return "not found"
}

func (e NotFoundError) ErrorCode() domain.Code {
	return domain.CodeNotFound
}

func (e NotFoundError) HTTPStatusCode() int {
	return http.StatusOK
}

func (e NotFoundError) GRPCStatusCode() codes.Code {
	return codes.NotFound
}

type ResourceExhaustedError struct {
	Err error

	NoneErrorParam
}

func (e ResourceExhaustedError) Error() string {
	return fmt.Sprintf("reosurce exhausted, err: %s", e.Err.Error())
}

func (e ResourceExhaustedError) ErrorCode() domain.Code {
	return domain.CodeNotFound
}

func (e ResourceExhaustedError) HTTPStatusCode() int {
	return http.StatusOK
}

func (e ResourceExhaustedError) GRPCStatusCode() codes.Code {
	return codes.ResourceExhausted
}
