package swap

import (
	"strings"

	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

// LogPos pos change log
type LogPos struct {
	Id           int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	UID          string          `gorm:"user_id;type:varchar(20);not null"`                  // 用户ID
	PosId        string          `gorm:"pos_id;type:varchar(50);"`                           // 持仓ID
	TradeId      string          `gorm:"trade_id;type:varchar(50);not null"`                 // 成交单ID
	OrderId      string          `gorm:"order_id;type:varchar(50);not null"`                 // 委托单ID
	ContractCode string          `gorm:"contract_code;type:varchar(50);not null"`            // 合约代码
	MarginMode   int32           `gorm:"margin_mode" sql:"type:SMALLINT;" json:"marginMode"` // 保证金模式 (1:全仓模式 2:追仓模式)
	Side         int32           `gorm:"side;not null"`                                      // 买或卖 (1:买单 2:卖单)
	Pos          decimal.Decimal `gorm:"pos" sql:"type:decimal(30,15);"`                     // 仓位
	ProfitReal   decimal.Decimal `gorm:"profit_real" sql:"type:decimal(30,15);"`             // 平仓已实现盈亏
	PosSide      int32           `gorm:"pos_side" sql:"type:TINYINT;"`                       // 多或空 (1:多 2:空)
	OperateTime  int64           `gorm:"operate_time;not null"`                              // 操作时间 (撮合提供)
}

func (slf *LogPos) TableName() string {
	return "log_pos" + util.DayLayout(slf.OperateTime, util.EnumNanosecond)
}

// Insert insert pos log
func (slf *LogPos) Insert(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	tableName := slf.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !db.HasTable(tableName) {
			db.Table(tableName).CreateTable(slf)
			util.SetNewTableName(tableName)
		}
	}

	if err := db.Table(tableName).Create(slf).Error; err != nil {
		return err
	}

	return nil
}

// GetLogPos 获取仓位日志记录
func (slf *LogPos) GetLogPos(db *gorm.DB, tradeId, orderId string) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	if err := db.Table(slf.TableName()).Where("trade_id = ? and order_id = ?", tradeId, orderId).First(slf).Error; err != nil && err != gorm.ErrRecordNotFound {
		if strings.Contains(err.Error(), "doesn't exist") { //  如果表不存在则创建表
			db.Table(slf.TableName()).CreateTable(slf)
			util.SetNewTableName(slf.TableName())
			err = nil
		}
		return err
	}

	return nil
}
