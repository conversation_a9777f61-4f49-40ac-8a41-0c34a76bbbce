package swap

import (
	"encoding/json"
	"fmt"
	"futures-asset/pkg/sqllib"
	"futures-asset/util"

	"github.com/jinzhu/gorm"
	"github.com/shopspring/decimal"
)

// UserStatistics 用户数据统计
type UserStatistics struct {
	Id            string          `gorm:"id;PRIMARY_KEY;" json:"id"`
	UID           string          `gorm:"user_id;type:varchar(20);not null" json:"uid"`              // 用户ID
	AccountType   string          `gorm:"account_type;type:varchar(20);not null" json:"accountType"` // 账户类型
	FirstOpenTime int64           `gorm:"first_open_time;comment:'首笔开仓时间'" json:"firstOpenTime"`
	HoldPosTimes  int             `gorm:"hold_pos_times;comment:'持仓总次数'" sql:"type:int(11);" json:"holdPosTimes"`
	HoldPosValue  decimal.Decimal `gorm:"hold_pos_value;comment:'持仓总价值'" sql:"type:decimal(35,10);" json:"holdPosValue"`
	LatestPosId   string          `gorm:"latest_pos_id;type:varchar(50);default:'';comment:'最新持仓的PosId'" json:"latestPosId"`
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *UserStatistics) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *UserStatistics) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

func NewUserStatistics() *UserStatistics {
	return &UserStatistics{}
}

func (slf *UserStatistics) CreateTable(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}
	tableName := slf.TableName()
	if !db.HasTable(tableName) {
		err := db.Table(tableName).CreateTable(slf).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (slf *UserStatistics) TableName() string {
	return "user_statistics"
}

func (slf *UserStatistics) GetId() string {
	slf.Id = util.StatisticsId(slf.UID, slf.AccountType)
	return slf.Id
}

// Upsert update user statistics
func (slf *UserStatistics) Upsert(db *gorm.DB) (err error) {
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return err
		}
	}

	insertColumns := "`id`, `user_id`,`account_type`,`first_open_time`,`hold_pos_times`,`hold_pos_value`,`latest_pos_id`"
	updateColumns := "`hold_pos_times`=?,`hold_pos_value`=?,`latest_pos_id`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		slf.TableName(), insertColumns, updateColumns)
	if err := db.Exec(sql,
		slf.GetId(), slf.UID, slf.AccountType, slf.FirstOpenTime, slf.HoldPosTimes, slf.HoldPosValue, slf.LatestPosId,
		slf.HoldPosTimes, slf.HoldPosValue, slf.LatestPosId).Error; err != nil {
		return err
	}

	return nil
}

// GetAll 获取所有用户统计数据
func (slf *UserStatistics) GetAll(db *gorm.DB) (result []UserStatistics, err error) {
	result = make([]UserStatistics, 0)
	if db == nil {
		db, err = sqllib.Db()
		if err != nil {
			return result, err
		}
	}

	if err := db.Model(slf).Find(&result).Error; err != nil {
		return result, err
	}

	return result, nil
}
