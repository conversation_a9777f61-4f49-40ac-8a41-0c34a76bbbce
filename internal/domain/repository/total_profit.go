package repository

import (
	"github.com/shopspring/decimal"
)

type TotalProfit struct {
	UID          string          `json:"uid"`          // 用户ID
	ContractType int32           `json:"contractType"` // 合约类型 (1:当周 2:下周 3:当季 4:次季)
	ContractCode string          `json:"contractCode"` // 合约代码
	Currency     string          `json:"currency"`     // 资产币种
	TotalProfit  decimal.Decimal `json:"totalProfit"`  // 累计盈亏
}
