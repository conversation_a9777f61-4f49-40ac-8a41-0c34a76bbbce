package repository

import (
	"context"
	"log"
	"strings"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/libs/pager"
	"futures-asset/pkg/match"
	"futures-asset/pkg/setting"
	"futures-asset/util"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type PositionRepository interface {
	UserPos(ctx context.Context, req *SwapParam) ([]PosSwap, error)
	QueryUserPos(ctx context.Context, req *UserPosParam) (UserPosReply, error)
	PosInfo(ctx context.Context, param *UserPosParam) (PosQuery, error)
	PosTotal(ctx context.Context, contractCode string) decimal.Decimal
	UserHoldPos(ctx context.Context, req *UserHoldPosReq) (HoldPosReply, error)

	PlatPosList(ctx context.Context) (PlatPosList, error)
	PlatPosDetail(ctx context.Context, req *PlatPosDetailReq) (PlatPosDetail, error)
}

type SwapParam struct {
	UID          string `json:"uid" binding:"required"`
	Currency     string `json:"currency"`
	ContractCode string `json:"contractCode"` // 合约代码
}

type UserPosParam struct {
	pager.Condition
	PosId           string `json:"posId"` // 持仓ID
	UID             string `json:"uid"`
	Base            string `json:"base"`
	Quote           string `json:"quote"`
	Order           int    `json:"order"`    // 排序 时间排序(1:升序 2:降序(默认))
	PosSide         int32  `json:"posSide"`  // 仓位类型 1:多仓 2:空仓
	IsHasPos        bool   `json:"isHasPos"` // 是否只看持有仓位
	AccountType     string `json:"accountType"`
	UserType        []int  `json:"userType"`
	StartTime       int64  `json:"startTime"`
	EndTime         int64  `json:"endTime"`
	PosStatus       int32  `json:"posStatus"`
	MarginMode      int32  `json:"marginMode"`
	LiquidationType []int  `json:"liquidationType"`
	IsExcel         int    `json:"isExcel"` // 是否导出
}

type UserPosReply struct {
	pager.Page
	LongPosTotal  decimal.Decimal `json:"longPosTotal"`  // 多仓合计
	ShortPosTotal decimal.Decimal `json:"shortPosTotal"` // 空仓合计
	Total         int64           `json:"total"`
	List          []PosQuery      `json:"list"`
}

type PosSwap struct {
	// calculate base fields
	Currency       string          `json:"currency"`       // 币种
	IsolatedMargin decimal.Decimal `json:"isolatedMargin"` // 逐仓仓位保证金 (全仓保证金需要前端自己计算(实时变化): (MarkPrice*Pos)/Leverage = [四舍五入])
	TrialMargin    decimal.Decimal `json:"trialMargin"`    // 体验金保证金
	Leverage       int             `json:"leverage"`       // 杠杆倍数
	Liquidation    decimal.Decimal `json:"liquidation"`    // 预估强评价
	MarginMode     int32           `json:"marginMode"`     // 仓位类型 1:全仓 2:逐仓
	OpenPriceAvg   decimal.Decimal `json:"openPriceAvg"`   // 开仓均价
	OpenTime       int64           `json:"openTime"`       // 开仓时间
	Pos            decimal.Decimal `json:"pos"`            // 仓位数
	PosAvailable   decimal.Decimal `json:"posAvailable"`   // 可平仓位
	PosSide        int32           `json:"posSide"`        // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓 3:单向)
	PosValue       decimal.Decimal `json:"posValue"`       // 仓位价值
	ProfitReal     decimal.Decimal `json:"profitReal"`     // 已实现盈亏
	ProfitUnreal   decimal.Decimal `json:"profitUnreal"`   // 未实现盈亏(回报率)
	ContractCode   string          `json:"contractCode"`   // 合约代码
	// 持仓记录参数
	PosId           string                 `json:"posId"`           // 持仓唯一ID
	UID             string                 `json:"uid"`             // 用户ID
	UserType        int32                  `json:"userType"`        // 用户类型
	AccountType     string                 `json:"accountType"`     // 合约类型
	PosStatus       domain.PosStatus       `json:"posStatus"`       // 持仓状态 (1:持仓中 2:已结束)
	Subsidy         decimal.Decimal        `json:"subsidy"`         // 穿仓补贴金额
	RebornId        string                 `json:"rebornId"`        // 重生卡id
	LiquidationType domain.LiquidationType `json:"liquidationType"` // 强平类型
	AwardOpIds      []string               `json:"awardOpIds"`      // 奖励操作id
	MarkPrice       decimal.Decimal        `json:"markPrice"`       // 标记价格
}

type PosQuery struct {
	PosSwap
	ReturnRate         decimal.Decimal `json:"returnRate"`         // 回报率
	Margin             decimal.Decimal `json:"margin"`             // 仓位保证金
	MarginRate         decimal.Decimal `json:"marginRate"`         // 保证金率
	MarginBalance      decimal.Decimal `json:"marginBalance"`      // 保证金余额
	MaintainMargin     decimal.Decimal `json:"maintainMargin"`     // 维持保证金
	MaintainMarginRate decimal.Decimal `json:"maintainMarginRate"` // 维持保证金率
	RivalScoreRate     decimal.Decimal `json:"rivalScoreRate"`     // 强制减仓指数风险率(小灯)
	RiskRate           decimal.Decimal `json:"riskRate"`           // 风险率
	IsWarning          int             `json:"isWarning"`          // 是否预警
	MarkPrice          decimal.Decimal `json:"markPrice"`          // 标记价格
	RivalScore         decimal.Decimal `json:"rivalScore"`         // 对手方减仓指数
}

type UserHoldPosReq struct {
	Base    string `json:"base"`    // 交易币
	Quote   string `json:"quote"`   // 计价币
	PosSide int32  `json:"posSide"` // 仓位类型 1:多仓 2:空仓
	UID     string `json:"uid"`     // 用户ID
}
type HoldPosReply struct {
	PositionMode futuresassetpb.PositionMode `json:"position_mode"`
	PosAmount    decimal.Decimal             `json:"posAmount"`
}

type (
	PlatPosDetailReq struct {
		Base  string `json:"base"`  // 交易币
		Quote string `json:"quote"` // 计价币
	}

	PlatPosDetail struct {
		Base                 string          `json:"base"`                 // 交易币
		Quote                string          `json:"quote"`                // 计价币
		TotalUserCount       int64           `json:"totalUserCount"`       // 总持仓人数 (含机器人)
		LongUserCount        int64           `json:"longUserCount"`        // 多仓持仓人数 (含机器人)
		ShortUserCount       int64           `json:"shortUserCount"`       // 空仓持仓人数 (含机器人)
		LongUserPercent      decimal.Decimal `json:"longUserPercent"`      // 多仓人数占比 (含机器人)
		ShortUserPercent     decimal.Decimal `json:"shortUserPercent"`     // 空仓人数占比 (含机器人)
		TotalPos             decimal.Decimal `json:"totalPos"`             // 总持仓数 (含机器人)
		LongPos              decimal.Decimal `json:"longPos"`              // 总多持仓数 (含机器人)
		UserLongPos          decimal.Decimal `json:"userLongPos"`          // 用户多仓持仓数
		RobotLongPos         decimal.Decimal `json:"robotLongPos"`         // 机器人多仓持仓数
		UserLongPosPercent   decimal.Decimal `json:"userLongPosPercent"`   // 用户多仓占比
		RobotLongPosPercent  decimal.Decimal `json:"robotLongPosPercent"`  // 机器人多仓占比
		ShortPos             decimal.Decimal `json:"shortPos"`             // 总空持仓数 (含机器人)
		UserShortPos         decimal.Decimal `json:"userShortPos"`         // 用户空仓持仓数
		RobotShortPos        decimal.Decimal `json:"robotShortPos"`        // 机器人空仓持仓数
		UserShortPosPercent  decimal.Decimal `json:"userShortPosPercent"`  // 用户空仓占比
		RobotShortPosPercent decimal.Decimal `json:"robotShortPosPercent"` // 机器人空仓占比
		UserAveragePos       decimal.Decimal `json:"userAveragePos"`       // 人均持仓数 (不含机器人)
		UpdateTime           int64           `json:"updateTime"`           // 更新时间 秒
		TotalUserPos         decimal.Decimal `json:"totalUserPos"`         // 用户多空持仓合计
		TotalRobotPos        decimal.Decimal `json:"totalRobotPos"`        // 机器人多空持仓合计
	}
	PlatPosList struct {
		PosList    []PlatPosDetail `json:"posList"`
		UpdateTime int64           `json:"updateTime"` // 列表数据中最新的更新时间
	}
)

// ExcelPos 运营后台导出实体
type ExcelPos struct {
	OpenTime string `json:"openTime"` // 开仓时间
	UID      string `json:"uid"`      // 用户ID
	UserType string `json:"userType"` // 用户类型
	PosId    string `json:"posId"`    // 持仓唯一ID

	AccountType     string `json:"accountType"`     // 合约类型
	ContractCode    string `json:"contractCode"`    // 合约代码
	MarginMode      string `json:"marginMode"`      // 仓位类型 1:全仓 2:逐仓
	PosSide         string `json:"posSide"`         // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
	Leverage        string `json:"leverage"`        // 杠杆倍数
	PosStatus       string `json:"posStatus"`       // 持仓状态 (1:持仓中 2:已结束)
	LiquidationType string `json:"liquidationType"` // 强平类型
	Subsidy         string `json:"subsidy"`         // 穿仓补贴金额

	Pos            string          `json:"pos"`            // 仓位数
	OpenPriceAvg   string          `json:"openPriceAvg"`   // 开仓均价
	MarkPrice      string          `json:"markPrice"`      // 标记价格
	Liquidation    string          `json:"liquidation"`    // 预估强评价
	Margin         string          `json:"margin"`         // 仓位保证金
	ProfitUnreal   string          `json:"profitUnreal"`   // 未实现盈亏(回报率)
	ProfitReal     string          `json:"profitReal"`     // 已实现盈亏
	ReturnRate     string          `json:"returnRate"`     // 回报率
	RiskRate       string          `json:"riskRate"`       // 风险率
	MaintainMargin string          `json:"maintainMargin"` // 维持保证金
	MarginBalance  string          `json:"marginBalance"`  // 保证金余额
	RivalScore     decimal.Decimal `json:"rivalScore"`     // 对手方减仓指数
}

func NewExcelPos() ExcelPos {
	return ExcelPos{
		OpenTime:        domain.DefaultNoData,
		UserType:        domain.DefaultNoData,
		AccountType:     domain.DefaultNoData,
		MarginMode:      domain.DefaultNoData,
		PosSide:         domain.DefaultNoData,
		Leverage:        domain.DefaultNoData,
		PosStatus:       domain.DefaultNoData,
		LiquidationType: domain.DefaultNoData,
		Subsidy:         domain.DefaultNoData,
		Pos:             domain.DefaultNoData,
		OpenPriceAvg:    domain.DefaultNoData,
		MarkPrice:       domain.DefaultNoData,
		Liquidation:     domain.DefaultNoData,
		Margin:          domain.DefaultNoData,
		ProfitUnreal:    domain.DefaultNoData,
		ProfitReal:      domain.DefaultNoData,
		ReturnRate:      domain.DefaultNoData,
		RiskRate:        domain.DefaultNoData,
		MaintainMargin:  domain.DefaultNoData,
		MarginBalance:   domain.DefaultNoData,
	}
}

// GetIsolatedHoldingMargin 获取逐仓维持保证金
func (slf *PosSwap) GetIsolatedHoldingMargin(markPrice decimal.Decimal) decimal.Decimal {
	base, quote := util.BaseQuote(slf.ContractCode)
	marginLevel, _, _ := setting.FetchMarginLevel(base, quote, slf.CalcPosValue(markPrice))
	posValue := slf.CalcPosValue(markPrice)
	holdingMargin := posValue.Mul(marginLevel.HoldingMarginRate)

	return holdingMargin
}

// ContainsAwardOpId 是否包含奖励操作ID
func (slf *PosSwap) ContainsAwardOpId(awardOpId string) bool {
	for _, id := range slf.AwardOpIds {
		if id == awardOpId {
			return true
		}
	}

	return false
}

// AddAwardOpIds 添加体验金ID
func (slf *PosSwap) AddAwardOpIds(awardOpIds ...string) {
	slf.AwardOpIds = lo.Uniq(append(slf.AwardOpIds, awardOpIds...))
}

func (slf *PosSwap) RemoveAwardOpIds(awardOpIds ...string) {
	slf.AwardOpIds, _ = lo.Difference(slf.AwardOpIds, awardOpIds)
}

// UpdateOpenPriceAvg 更新开仓均价
func (slf *PosSwap) UpdateOpenPriceAvg(amount, price decimal.Decimal) {
	// 首先计算价值，然后除以数量
	openAvg := slf.Pos.Mul(slf.OpenPriceAvg).Add(amount.Mul(price))
	latestPos := slf.Pos.Add(amount)
	if !latestPos.IsZero() {
		openAvg = openAvg.Div(latestPos)
	}
	slf.OpenPriceAvg = openAvg.Truncate(domain.PricePrecision)
}

// UpdatePos 更新仓位
func (slf *PosSwap) UpdatePos(amount decimal.Decimal, updateAvail bool) {
	slf.Pos = slf.Pos.Add(amount)
	if updateAvail {
		slf.PosAvailable = slf.PosAvailable.Add(amount)
	}
}

// UpdateTrialMargin 更新体验金保证金
// 体验金必然是逐仓
func (slf *PosSwap) UpdateTrialMargin(amount decimal.Decimal) {
	slf.TrialMargin = slf.TrialMargin.Add(amount)
	slf.IsolatedMargin = slf.IsolatedMargin.Add(amount)
}

// HasPosStatus 只要存在其中一个状态就返回true
func (slf *PosSwap) HasPosStatus(statuses ...domain.PosStatus) bool {
	for _, s := range statuses {
		if slf.PosStatus == s {
			return true
		}
	}
	return false
}

// NewPos 新开仓仓位参数初始设置(已存在历史仓位, 历史仓位平完后再新开仓位)
func (slf *PosSwap) NewPos(uid string, userType, marginMode int32, leverage int) {
	// slf.PosId = util.GenerateId() // TODO snowflakeID
	slf.UID = uid
	slf.UserType = userType
	slf.PosStatus = domain.PosStatusHolding
	slf.AccountType = match.AccountTypeSwap
	slf.OpenTime = time.Now().UnixNano()
	slf.LiquidationType = domain.LiquidationTypeNone
	slf.ProfitReal = decimal.Zero
	slf.Subsidy = decimal.Zero
	slf.IsolatedMargin = decimal.Zero
	slf.TrialMargin = decimal.Zero
	slf.MarginMode = marginMode
	slf.Leverage = leverage
}

// Init 初始化仓位实体(没有仓位时)
func (slf *PosSwap) Init(base, quote, uid string, posSid int32) {
	slf.UID = uid
	slf.PosSide = posSid
	slf.ContractCode = util.ContractCode(base, quote)
	slf.OpenTime = time.Now().UnixNano()
	slf.Currency = strings.ToUpper(quote)
}

// Clear 仓位平空或爆仓后重置仓位参数(已存在历史仓位)
func (slf *PosSwap) Clear() {
	_, quote := util.BaseQuote(slf.ContractCode)
	slf.Currency = quote
	// 此处不能清除 UID, UserType, PosId, OpenTime, LiquidationType, ProfitReal 和 Subsidy (新开仓的时候重新赋值)
	slf.Liquidation = decimal.Zero
	slf.OpenPriceAvg = decimal.Zero
	slf.Pos = decimal.Zero
	slf.PosAvailable = decimal.Zero
	slf.PosValue = decimal.Zero
	slf.ProfitUnreal = decimal.Zero
	slf.PosStatus = domain.PosStatusEnd
	slf.AwardOpIds = nil
}

// GetMarginMode 仓位类型 1:全仓 2:逐仓
func (slf *PosSwap) GetMarginMode() domain.MarginMode {
	return domain.MarginMode(slf.MarginMode)
}

// Isolated 是否逐仓
func (slf *PosSwap) Isolated() bool {
	return slf.GetMarginMode() == domain.MarginModeIsolated
}

// GetPosWithSide 带方向获取仓位数 空仓为:负
func (slf *PosSwap) GetPosWithSide() decimal.Decimal {
	if slf.PosSide == domain.ShortPos {
		return slf.Pos.Neg()
	}
	return slf.Pos
}

// GetSide 获取仓位方向 1:多仓 2:空仓 3:单向。单向持仓 仓位数大于等于0 为多 小于0 为空
func (slf *PosSwap) GetSide() int32 {
	if slf.PosSide == domain.BothPos {
		if slf.Pos.IsNegative() {
			return domain.ShortPos
		} else {
			return domain.LongPos
		}
	}
	return slf.PosSide
}

// IsTrial 是否体验金仓位 true:是 false:否
func (slf *PosSwap) IsTrial() bool {
	return len(slf.AwardOpIds) > 0
}

// CalcPosValue 计算持仓时仓位价值 新版公式 改名为仓位市值
func (slf *PosSwap) CalcPosValue(markPrice decimal.Decimal) decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}

	return slf.Pos.Mul(markPrice).Abs()
}

// CalcPosHoldValue 计算持仓时仓位价值 新版公式 改名为持仓价值
func (slf *PosSwap) CalcPosHoldValue() decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}
	return slf.Pos.Mul(slf.OpenPriceAvg).Abs()
}

// HoldMargin 持仓仓位保证金
// 持仓保证金跟持仓成本的区别：全仓：保证金使用标记价格算市值  成本使用开仓均价算成本  逐仓：相等
func (slf *PosSwap) HoldMargin(markPrice decimal.Decimal) decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}
	if slf.Isolated() {
		return slf.IsolatedMargin
	}

	if slf.Leverage > 0 {
		margin := slf.CalcPosValue(markPrice).Div(decimal.NewFromInt(int64(slf.Leverage)))
		return margin.Abs()
	}

	return decimal.Zero
}

// FrozenPos 冻结仓位数
func (slf *PosSwap) FrozenPos() decimal.Decimal {
	return slf.Pos.Abs().Sub(slf.PosAvailable)
}

// OpenHoldCost 按照持仓均价计算的持仓成本
func (slf *PosSwap) OpenHoldCost() decimal.Decimal {
	if slf.Leverage == 0 {
		return decimal.Zero
	}
	leverage := decimal.NewFromInt(int64(slf.Leverage))
	return slf.Pos.Mul(slf.OpenPriceAvg).Div(leverage).Abs()
}

// HoldCost 持仓成本
func (slf *PosSwap) HoldCost() decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}
	if slf.Isolated() {
		return slf.IsolatedMargin
	}
	return slf.OpenHoldCost()
}

// IsolatedMarginBalance 返回逐仓保证金余额
func (slf *PosSwap) IsolatedMarginBalance(markPrice decimal.Decimal) decimal.Decimal {
	return slf.IsolatedMargin.Add(slf.CalcProfitUnreal(markPrice))
}

// CalcProfitUnreal 计算未实现盈亏
func (slf *PosSwap) CalcProfitUnreal(markPrice decimal.Decimal) decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}

	return markPrice.Sub(slf.OpenPriceAvg).Mul(slf.GetPosWithSide()).Truncate(domain.CurrencyPrecision)
}

// CalcOpenPriceAvg 计算开仓均价
func (slf *PosSwap) CalcOpenPriceAvg(newPos, newPosPrice decimal.Decimal) decimal.Decimal {
	// （原持仓仓位6*原持仓均价2+新开仓位6*新开仓成交均价2）/（原仓位6+新开仓位6）
	openAvg := slf.Pos.Mul(slf.OpenPriceAvg).Add(newPos.Mul(newPosPrice))
	if latestPos := slf.Pos.Add(newPos); !latestPos.IsZero() {
		openAvg = openAvg.Div(latestPos)
	}
	return openAvg.Truncate(domain.PricePrecision)
}

// CalcPosMargin 开仓成本（开仓时的仓位保证金）
func (slf *PosSwap) CalcPosMargin(amount, price, fee decimal.Decimal) decimal.Decimal {
	if slf.Isolated() {
		return slf.isolatedMargin(amount, price, fee).Truncate(domain.CurrencyPrecision)
	}

	return slf.crossMargin(amount, price).Truncate(domain.CurrencyPrecision)
}

// isolatedMargin 计算逐仓位仓保证金(开仓时)
func (slf *PosSwap) isolatedMargin(amount, price, fee decimal.Decimal) decimal.Decimal {
	if slf.Leverage <= 0 {
		return decimal.Zero
	}
	basicMargin, _ := util.RoundCeil(amount.Mul(price).Div(decimal.NewFromInt(int64(slf.Leverage))), domain.CurrencyPrecision)
	basicMargin = basicMargin.Sub(fee)
	log.Printf("pos isolated margin is: %s", basicMargin.String())
	return basicMargin
}

// crossMargin 计算全仓仓位保证金(开仓时)
func (slf *PosSwap) crossMargin(amount, price decimal.Decimal) decimal.Decimal {
	if slf.Leverage <= 0 {
		return decimal.Zero
	}
	basicMargin, _ := util.RoundCeil(amount.Mul(price).Div(decimal.NewFromInt(int64(slf.Leverage))), domain.CurrencyPrecision)
	return basicMargin
}

// CalcProfitReal 计算已实现盈亏
func (slf *PosSwap) CalcProfitReal(price, amount decimal.Decimal) decimal.Decimal {
	if slf.Pos.IsZero() {
		return decimal.Zero
	}
	amount = amount.Abs()
	switch slf.PosSide {
	case domain.ShortPos:
		amount = amount.Neg()

	case domain.BothPos:
		if slf.Pos.LessThan(decimal.Zero) {
			amount = amount.Neg()
		}

	}

	return price.Sub(slf.OpenPriceAvg).Mul(amount).Truncate(domain.PricePrecision)
}

// -------------以上为已确认公式-------------------------------

type PosSwapSlice []PosSwap

func (slf PosSwapSlice) Len() int           { return len(slf) }
func (slf PosSwapSlice) Less(i, j int) bool { return slf[i].OpenTime > slf[j].OpenTime }
func (slf PosSwapSlice) Swap(i, j int)      { slf[i], slf[j] = slf[j], slf[i] }
