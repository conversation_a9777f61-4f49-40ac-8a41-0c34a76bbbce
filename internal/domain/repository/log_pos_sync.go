package repository

import (
	"encoding/json"

	"futures-asset/internal/domain/db/swap"
	"futures-asset/internal/domain/entity"
)

type LogPosSync struct {
	LogPos  swap.LogPos     // 日志记录
	PosSwap entity.Position // 存库
	Pos     PosSwap         // 用于推送
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf LogPosSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf LogPosSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
