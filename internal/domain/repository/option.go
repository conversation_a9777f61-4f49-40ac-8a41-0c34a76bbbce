package repository

import (
	"context"

	"github.com/shopspring/decimal"
)

type OptionRepository interface {
	OptionPnlRecord(ctx context.Context, req *ReqPLRecord) (OptionPnlReply, error)

	OptionHashKey(uid string, optionType int) string
	LoadUserOption(ctx context.Context, uid string, optionType int) ([]Option, error)
	LoadUserOptions(ctx context.Context, uid string, optionType int, optionKeys []string) (map[string][]Option, error)
	UpdateOption(ctx context.Context, uid string, optionType int, optionKey string, options []Option) error
	UpdateMultiOption(ctx context.Context, uid string, optionType int, options map[string]Option) error
	RemoveOption(ctx context.Context, uid string, optionType int) error
	GetDemoAsset(ctx context.Context, uid string) (decimal.Decimal, error)
	UpdateDemoAsset(ctx context.Context, uid string, balance decimal.Decimal) error
	GetAllDemoUsers(ctx context.Context) ([]string, error)
}

type Option struct {
	OrderId string          `json:"orderId"` // 委托单ID
	Premium decimal.Decimal `json:"premium"` // 权利金(委托总额)
	Fee     decimal.Decimal `json:"fee"`     // 手续费
}

type ReqPLRecord struct {
	UID       string `json:"uid"`       // 用户id
	DaysParam int    `json:"daysParam"` // 默认传0；1.代表最近7天；2.代表最近30天
	StartTime int64  `json:"startTime"` // 起始时间
	EndTime   int64  `json:"endTime"`   // 结束时间
	ValType   int    `json:"valType"`   // 估值类型1.CNY 2.USDT 3.BTC
}

type (
	OptionPnlReply struct {
		TotalProfitLoss   decimal.Decimal   `json:"totalProfitLoss"`   // 自开通合约的累计盈亏
		DaysAnalysis      OptionPnlAnalysis `json:"daysAnalysis"`      // 盈亏分析 (7日, 30日盈亏分析)
		ProfitLossRecords []OptionPnlItem   `json:"profitLossRecords"` // 盈亏数据
	}
	OptionPnlAnalysis struct {
		Days                 int             `json:"days"`                 // 统计天数
		DaysCumulativeProfit decimal.Decimal `json:"daysCumulativeProfit"` // 累计盈利
		DaysCumulativeLoss   decimal.Decimal `json:"daysCumulativeLoss"`   // 累计亏损
		NetProfitLoss        decimal.Decimal `json:"netProfitLoss"`        // 净盈亏
		ProfitCount          int64           `json:"profitCount"`          // 盈利订单数
		LossCount            int64           `json:"lossCount"`            // 亏损订单数
		ProfitDays           int64           `json:"profitDays"`           // 盈利天数
		LossDays             int64           `json:"lossDays"`             // 亏损天数
		EqualDays            int64           `json:"equalDays"`            // 盈利和亏损相等的天数
		AverageProfit        decimal.Decimal `json:"averageProfit"`        // 平均盈利
		AverageLoss          decimal.Decimal `json:"averageLoss"`          // 平均亏损
	}
	OptionPnlItem struct {
		DayTime         int64           `json:"dayTime"`         // 日期时间戳
		NetIn           decimal.Decimal `json:"netIn"`           // 当日净转入
		TotalFee        decimal.Decimal `json:"totalFee"`        // 当日手续费
		ProfitPremium   decimal.Decimal `json:"profitPremium"`   // 当日盈利订单权利金总和
		ProfitCount     int64           `json:"profitCount"`     // 当日盈利订单数
		LossCount       int64           `json:"lossCount"`       // 当日亏损订单数
		DayProfit       decimal.Decimal `json:"dayProfit"`       // 当日盈利
		DayLoss         decimal.Decimal `json:"dayLoss"`         // 当日亏损
		DayProfitLoss   decimal.Decimal `json:"dayProfitLoss"`   // 当日盈亏
		TotalProfitLoss decimal.Decimal `json:"totalProfitLoss"` // 累计总盈亏
	}
)
