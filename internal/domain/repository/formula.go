package repository

import (
	"context"
	"futures-asset/pkg/setting"

	"github.com/shopspring/decimal"
)

type FormulaRepository interface {
	CalcLiquidationPrice(basePos *PosQuery, longPos, shortPos, bothPos *PosSwap,
		totalIsoMargin, balance, crossOther, crossOtherMargin, markPrice decimal.Decimal,
	) decimal.Decimal
	IsolatedCollapsePrice(pos *PosSwap, takerFeeRate, makerFeeRate decimal.Decimal) (decimal.Decimal, string)
	CrossCollapsePrice(ctx context.Context, asset *AssetSwap, pos *PosSwap, isolatedAllMargin, otherCrossUnreal, otherCrossMargin, takerFeeRate, makeFeeRate decimal.Decimal) (decimal.Decimal, string, error)

	PremiumIndex(ctx context.Context, symbol string, cfg setting.ContractPair) decimal.Decimal
	SetOneFundRate(ctx context.Context, fundRateListKey, symbol string, indexPrice, interest decimal.Decimal)
	GetMaxPosValueWithLeverage(ctx context.Context, base, quote string, leverage int) (decimal.Decimal, error)
	MakeMarkPrice(ctx context.Context, contractCode string)
	FundingRate(ctx context.Context, contractCode string, cfg setting.ContractPair) decimal.Decimal
}
