package repository

import (
	"encoding/json"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/util"

	"github.com/shopspring/decimal"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type ReqAsset struct {
	Currency string `json:"currency"`
	UID      string `json:"uid"`
	ValType  int    `json:"valType"` // 估值类型1.CNY 2.USDT 3.BTC
}

type CommonParam struct {
	UID          string `json:"uid" binding:"required"` // 用户ID
	ContractCode string `json:"contractCode"`           // 合约代码
}

type SwapInit struct {
	UID string `json:"uid" binding:"required"` // 用户ID
}

type IncrParam struct {
	UID         string          `json:"uid" binding:"required"`     // 用户ID
	OrderId     string          `json:"orderId" binding:"required"` // 订单ID
	FromPair    string          `json:"fromPair"`                   // 转出币对
	ToPair      string          `json:"toPair"`                     // 转入币对
	Currency    string          `json:"currency"`                   // 币种
	FromAccount int             `json:"fromAccount"`                // 转出账户 0:现货账户 1:杠杆账户 2:USDT永续合约账户 3:法币账户
	ToAccount   int             `json:"toAccount"`                  // 转入账户 0:现货账户 1:杠杆账户 2:USDT永续合约账户 3:法币账户
	Amount      decimal.Decimal `json:"amount"`                     // 转账数量
}

type (
	Trade struct {
		UID              string                 `json:"uid"`              // 用户ID
		UserType         int32                  `json:"userType"`         // 用户类型
		OrderId          string                 `json:"orderId"`          // 委托单ID
		OrderType        int32                  `json:"orderType"`        // 委托类型 (1:限价 2:市价 3-限价止盈 4-市价止盈 5-限价止损 6-市价止损)
		Side             int32                  `json:"side"`             // 委托单方向 (1:买 2:卖)
		PosSide          int32                  `json:"pos_side"`         // 多或空 (1:多 2:空)
		OrderPrice       decimal.Decimal        `json:"orderPrice"`       // 原委托单价格
		Fee              decimal.Decimal        `json:"fee"`              // 手续费
		OriginFeeRate    decimal.Decimal        `json:"originFeeRate"`    // 原始手续费率
		FeeRate          decimal.Decimal        `json:"feeRate"`          // 实际手续费率
		Leverage         int                    `json:"leverage"`         // 杠杆倍数
		LiquidationType  domain.LiquidationType `json:"liquidationType"`  // 爆仓类型 (1:爆仓 2:减仓 3:止盈减仓)
		AccountType      string                 `json:"accountType"`      // 账户类型
		FeeCurrency      string                 `json:"feeCurrency"`      // 手续费币种
		StrategyId       string                 `json:"strategyId"`       // 策略ID
		StrategyName     string                 `json:"strategyName"`     // 策略名称
		StrategyType     int32                  `json:"strategyType"`     // 策略类型
		State            int32                  `json:"state"`            // 成交状态
		MarginMode       int32                  `json:"marginMode"`       // 仓位模式 (1:全仓 2:逐仓)
		UnfrozenMargin   decimal.Decimal        `json:"unfrozenMargin"`   // 成交后解锁保证金
		Platform         string                 `json:"platform"`         // 来源 (WEB,APP,H5)
		ChannelCode      string                 `json:"channelCode"`      // 用户渠道码
		RegisterLanguage string                 `json:"registerLanguage"` // 用户注册语言
		BurstId          string                 `json:"burstId"`          // 爆仓ID
		BurstTime        int64                  `json:"burstTime"`        // 爆仓时间
		DeductFee        decimal.Decimal        `json:"deductFee"`        // 抵扣手续费
		DeductAwardId    string                 `json:"deductAwardId"`    // 抵扣奖品id
		DeductType       int                    `json:"deductType"`       // 抵扣类型
		AgentUserId      string                 `json:"agentUserId"`      // 代理人id
		AgentStatus      int                    `json:"agentStatus"`      // 代理人状态-1:启用,2:禁用
		AgentChannelCode string                 `json:"agentChannelCode"` // 代理人渠道码
		AwardOpIds       []string               `json:"awardOpIds"`       // 体验金资产ID
		IsRobotSelfTrade bool                   // 是否机器人类型自成交
	}
	// TradeCommon contains trade common fields.
	TradeCommon struct {
		Base         string          `json:"base"`
		Quote        string          `json:"quote"`
		TradeId      string          `json:"tradeId"` // 成交ID
		Price        decimal.Decimal `json:"price"`   // 成交价格
		Amount       decimal.Decimal `json:"amount"`  // 成交数量
		ConvertMoney decimal.Decimal `json:"convertMoney"`
		TradeType    int32           `json:"tradeType"`   // 是否暗成交(撮合不进深度)
		OperateTime  int64           `json:"operateTime"` // 操作时间
		IsErr        bool            `json:"isErr"`       // 是否成交错误单
	}
	// TradeParam contains binded and validated data.
	TradeParam struct {
		TradeCommon
		Taker Trade `json:"taker"` // Taker
		Maker Trade `json:"maker"` // Maker
	}
	TradeReply struct {
		TradeId      string `json:"tradeId"`   // 成交ID
		TradeType    int32  `json:"tradeType"` // 是否暗成交(撮合不进深度)
		ContractCode string `json:"contractCode"`
		Taker        Reply  `json:"taker"`
		Maker        Reply  `json:"maker"`
	}
	FeeDetail struct {
		Currency string          `json:"currency"`
		Amount   decimal.Decimal `json:"amount"`
		Price    decimal.Decimal `json:"price"`
	}
	Reply struct {
		UID                   string          `json:"uid"`                // 用户id
		PosId                 string          `json:"posId"`              // 仓位(持仓)唯一ID
		ProfitReal            decimal.Decimal `json:"profitReal"`         // 平仓已实现盈亏
		Pos                   decimal.Decimal `json:"pos"`                // pos
		OpenPriceAvg          decimal.Decimal `json:"openPriceAvg"`       // 开仓均价
		PosSide               int32           `json:"posSide"`            // 仓位方向
		FeeDetail             []FeeDetail     `json:"feeDetail"`          // 手续费明细
		HaveTrial             int             `json:"haveTrial"`          // 使用体验金
		IsReverse             bool            `json:"isReverse"`          // 成交是否有先平仓后开仓的类反手操作
		ReverseClosePosAmount decimal.Decimal `json:"reverseCloseAmount"` // 单向持仓时平仓数量
	}
	BalanceUpdate struct {
		ContractCode     string          `json:"contractCode"`     // 合约代码
		Currency         string          `json:"currency"`         // 资产币种
		OrderId          string          `json:"orderId"`          // 订单ID
		UID              string          `json:"uid"`              // 用户id
		UserType         int32           `json:"userType"`         // 用户类型
		ChannelCode      string          `json:"channelCode"`      // 用户渠道码
		AgentUserId      string          `json:"agentUserId"`      // 代理人id
		AgentStatus      int             `json:"agentStatus"`      // 代理人状态-1:启用,2:禁用
		AgentChannelCode string          `json:"agentChannelCode"` // 代理人渠道码
		RegisterLanguage string          `json:"registerLanguage"` // 用户注册语言
		Platform         string          `json:"platform"`         // 来源 (WEB,APP,H5)
		StrategyType     int32           `json:"strategyType"`     // 策略类型 (0:合约交易)
		DealAmount       decimal.Decimal `json:"dealAmount"`       // 成交仓位数量
		DealPrice        decimal.Decimal `json:"dealPrice"`        // 成交价格
		Amount           decimal.Decimal `json:"amount"`           // 财务记账数量(按照domain.ContractTradeOpenFee等交易类型区分)
		OperateType      int             `json:"operateType"`      // 操作类型
		OperateTime      int64           `json:"operateTime"`      // 操作时间
		AwardOpIds       []string        `json:"awardOpIds"`       // 体验金资产ID
		// 只有期权行权时,wallet返佣逻辑中会使用
		OptionId string          `json:"optionId"` // option-期权ID
		Premium  decimal.Decimal `json:"premium"`  // option-权利金总额 (只有期权行权时,wallet返佣逻辑中会使用(通知枚举值为:OptionRealFee-20))
		Period   string          `json:"period"`   // 期权时间粒度 (3分钟, 5分钟) (只有期权行权时,wallet返佣逻辑中使用)
	}
	BalanceRes struct {
		AssetLogs      []*MqCmsAsset        // 财务日志记录
		BillAssetLogs  []BillAssetSync      // 账单日志记录
		TrialAssetLogs []*entity.TrialAsset // 账单日志记录
	}
)

type (
	InnerTransfer struct {
		FromUserId string          `json:"fromUserId"`
		ToUserId   string          `json:"toUserId"`
		Currency   string          `json:"currency"`
		Amount     decimal.Decimal `json:"amount"`
	}
)

func (slf TradeCommon) ContractCode() string {
	return util.ContractCode(slf.Base, slf.Quote)
}

type AssetReply struct {
	// Before  decimal.Decimal `json:"before"`
	// After   decimal.Decimal `json:"after"`
	OrderId string `json:"orderId"`
}

type (
	MarginParam struct {
		CommonParam
		Amount     decimal.Decimal   `json:"amount"`
		MarginMode domain.MarginMode `json:"marginMode"`
		PosSide    int32             `json:"posSide"`
		AwardOpIds []string          `json:"awardOpIds"`
	}
	HoldModeParam struct {
		CommonParam
		PositionMode int `json:"position_mode"` // 保证金模式 1.双向持仓 2.单向持仓
	}
	LeverageMarginAdAdjust struct {
		CommonParam
		MarginMode domain.MarginMode `json:"marginMode"`
		Leverage   int               `json:"leverage"`
		LLeverage  int               `json:"lLeverage"` // 逐仓 多仓杠杆倍数
		SLeverage  int               `json:"sLeverage"` // 逐仓 空仓杠杆倍数
		BLeverage  int               `json:"bLeverage"` // 单向持仓 逐仓杠杆倍数
	}
	HoldModeRes struct {
		UID          string `json:"uid"`           // 用户ID
		PositionMode int    `json:"position_mode"` // positionMode
	}
	JoinMarginRes struct {
		UID       string `json:"uid"`        // 用户ID
		AssetMode int    `json:"asset_mode"` // 保证金模式 1.单币保证金 2.联合保证金
	}
	ChangeJoinMarginParam struct {
		CommonParam
		AssetMode futuresassetpb.AssetMode `json:"asset_mode"`
	}
	OrderConfirm struct {
		LimitConfirm    int `json:"limitConfirm"`    // 限价二次确认
		MarketConfirm   int `json:"marketConfirm"`   // 市价二次确认
		TriggerConfirm  int `json:"triggerConfirm"`  // 止盈止损二次确认
		PostOnlyConfirm int `json:"postOnlyConfirm"` // 只做Maker二次确认
		BackhandConfirm int `json:"backhandConfirm"` // 反手二次确认
	}
	OrderConfirmRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfirm
	}
	ChangeOrderConfirmParam struct {
		CommonParam
		OrderConfirm
	}
	OrderConfig struct {
		RebornCard int `json:"rebornCard"` // 复活卡
		Trial      int `json:"trial"`      // 体验金
	}
	OrderConfigRes struct {
		UID string `json:"uid"` // 用户ID
		OrderConfig
	}
	ChangeOrderConfigParam struct {
		CommonParam
		OrderConfig
	}
	AdjustCrossParam struct {
		CommonParam
	}
	MarginAdjust struct {
		CommonParam
		MarginMode domain.MarginMode `json:"marginMode"`
	}
	LeverageAdjust struct {
		CommonParam
		Leverage  int `json:"leverage"`
		LLeverage int `json:"lLeverage"` // 逐仓 多仓杠杆倍数
		SLeverage int `json:"sLeverage"` // 逐仓 空仓杠杆倍数
		BLeverage int `json:"bLeverage"` // 单向持仓 逐仓杠杆倍数
	}
)

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf TradeParam) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf TradeParam) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

type TranCny struct {
	Msg      string          `json:"msg"`
	Code     int             `json:"code"`
	UsdToCny decimal.Decimal `json:"data"`
}

type Asset struct {
	Currency       string          `json:"currency"`       // 币种名称
	TotalTotal     decimal.Decimal `json:"totalTotal"`     // 币种总量 加上总的未实现盈亏
	BTCValuation   decimal.Decimal `json:"btcValuation"`   // 币种BTC总量折合
	TotalValuation decimal.Decimal `json:"totalValuation"` // 币种折合：根据valType决定
	TotalProfit    decimal.Decimal `json:"totalProfit"`    // 累计盈亏
}
