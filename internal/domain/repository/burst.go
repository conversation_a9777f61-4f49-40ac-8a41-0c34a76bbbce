package repository

import (
	"context"
	"time"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type BurstRepository interface {
	IsBursting(ctx context.Context, param CheckBurstParam) (bool, error)

	// db
	GetBurstInfoByTableNameAndId(ctx context.Context, id string) (*entity.BurstSwap, error)
	SearchBurstInfos(ctx context.Context, conditions map[string]interface{}, ranges map[string]map[string]interface{}, pageNum, pageSize int) (int64, []entity.BurstSwap)
	StatBurstTimesByTableNameList(ctx context.Context, startTime, endTime time.Time) []entity.StatBurstInfo

	TrialUserId(uid string) string
	RemoveUserIdTrial(userId string) string
	UserIdIsContainTrial(userId string) bool
	RivalScoreRate(ctx context.Context, uid, symbol string, posSide int32, isTrialPos bool) decimal.Decimal
	GetUserBurstLevel(ctx context.Context, uid, symbol string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) string
	SaveUserBurstLevel(ctx context.Context, uid, symbol, level string, marginMode futuresassetpb.MarginMode, posSide futuresassetpb.PosSide) error
	RivalScore(ctx context.Context, uid, symbol string, posSide int32, isTrialPos bool) decimal.Decimal
	FetchUserAllRivalScore(ctx context.Context, uid string) (map[string]string, map[string]string)
	RemoveUserAllRivalScore(ctx context.Context, uid string)
	RemoveRivalScore(ctx context.Context, uid string, symbol string, posSide int32, isTrial bool)

	GetBurstServerContracts(ctx context.Context) []string
}

type CheckBurstParam struct {
	UID          string
	ContractCode string
	MarginMode   futuresassetpb.MarginMode
	IsTrialPos   bool // 是否体验金仓位
}
