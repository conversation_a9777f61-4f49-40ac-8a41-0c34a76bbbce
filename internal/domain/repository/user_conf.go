package repository

import (
	"context"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

// UserRepository 定义了用户仓储层接口
type UserRepository interface {
	// SwapInit 初始化用户合约账户
	SwapInit(ctx context.Context, param *SwapInit) domain.Code

	// UserLeverage 获取用户配置的合约币对杠杆倍数
	UserLeverage(ctx context.Context, param *CommonParam) ([]*Leverage, error)

	// AdjustLeverageMargin 调整杠杆和保证金
	AdjustLeverageMargin(ctx context.Context, p *LeverageMarginAdAdjust) (domain.Code, error)

	// AdjustLeverage 调整杠杆倍数
	AdjustLeverage(ctx context.Context, p *LeverageAdjust) (domain.Code, error)

	// AdjustMarginCfg 调整保证金配置
	AdjustMarginCfg(ctx context.Context, p *MarginAdjust) (domain.Code, error)

	// AdjustMargin 调整保证金
	AdjustMargin(ctx context.Context, p *MarginParam) (domain.Code, error)

	// GetUserAsset 获取用户资产
	GetUserAsset(ctx context.Context, param *ReqAsset) (domain.Code, []Asset)

	// UserHoldMode 获取用户持仓模式
	UserHoldMode(ctx context.Context, param *CommonParam) (HoldModeRes, error)

	// AdjustHoldMode 调整持仓模式
	AdjustHoldMode(ctx context.Context, p *HoldModeParam) (domain.Code, error)

	// LoadJoinMargin 获取参与保证金
	LoadJoinMargin(ctx context.Context, param *CommonParam) (JoinMarginRes, error)

	// ChangeJoinMargin 修改参与保证金
	ChangeJoinMargin(ctx context.Context, p *ChangeJoinMarginParam) (domain.Code, error)

	// GetOrderConfirm 获取下单确认
	GetOrderConfirm(ctx context.Context, param *CommonParam) (OrderConfirmRes, error)

	// ChangeOrderConfirm 修改下单确认
	ChangeOrderConfirm(ctx context.Context, p *ChangeOrderConfirmParam) (domain.Code, error)

	// GetOrderConfig 获取订单配置
	GetOrderConfig(ctx context.Context, param *CommonParam) (OrderConfigRes, error)

	// ChangeOrderConfig 修改订单配置
	ChangeOrderConfig(ctx context.Context, p *ChangeOrderConfigParam) (domain.Code, error)

	// AdjustCross 一键切换到全仓模式
	AdjustCross(ctx context.Context, p *AdjustCrossParam) ([]string, error)

	GetUserOpenCloseTimes(ctx context.Context, req *OpenCloseTimesReq) (*OpenCloseTimesRes, error)

	GetUserStatistics(ctx context.Context, param *UserStatistics) ([]UserStatisticsReply, error)
}

type (
	OpenCloseTimesReq struct {
		UID string `json:"uid"`
	}

	OpenCloseTimesRes struct {
		UID        string `json:"uid"`
		OpenTimes  int    `json:"openTimes"`
		CloseTimes int    `json:"closeTimes"`
	}
)

type (
	UserStatistics struct {
		AccountType string `json:"accountType"` // 合约类型
	}

	UserStatisticsReply struct {
		UID              string          `json:"uid"`              // 用户ID
		TotalCloseMargin decimal.Decimal `json:"totalCloseMargin"` // 总平仓仓位保证金
		WinRate          decimal.Decimal `json:"winRate"`          // 交易胜率
		TxDays           int             `json:"txDays"`           // 交易时长
		Balance          decimal.Decimal `json:"balance"`          // 当前合约账户余额
		HoldPosValue     decimal.Decimal `json:"holdPosValue"`     // 持仓仓位价值总额
		HoldPosTimes     int             `json:"holdPosTimes"`     // 持仓总次数
	}
)
