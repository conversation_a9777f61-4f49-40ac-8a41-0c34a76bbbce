package domain

import (
	"fmt"
	"strconv"
	"strings"
)

// BurstStatus 爆仓状态
type BurstStatus int

func (bs BurstStatus) Val() int {
	if val, ok := burstStatusMap[bs]; ok {
		return val
	}
	return 0
}

func (bs BurstStatus) String() string {
	return strconv.Itoa(bs.Val())
}

func (bs BurstStatus) Valid() bool {
	_, ok := burstStatusMap[bs]
	return ok
}

const (
	Bursting      BurstStatus = 1 // 爆仓中
	BurstFinished BurstStatus = 2 // 已爆仓

	BurstReset      = 0
	BurstTrigged    = 20
	BurstReady      = 21
	BurstProcessing = 30
	BurstProcessed  = 31
	BurstReChecking = 32
	BurstTaskError  = 40

	NotOverflow   = 1 // 未穿仓
	NotForceRival = 1 // 未对手方强制减仓
	Overflowed    = 2 // 已穿仓
	ForceRivaled  = 2 // 对手方强制减仓
)

var burstStatusMap map[BurstStatus]int

func init() {
	initBurstStatus := []BurstStatus{
		Bursting,
		BurstFinished,
		BurstTrigged,
		BurstReady,
		BurstProcessing,
		BurstProcessed,
		BurstReChecking,
		BurstTaskError,
	}
	burstStatusMap = make(map[BurstStatus]int)
	for _, status := range initBurstStatus {
		burstStatusMap[status] = int(status)
	}
}

const (
	RivalScoreSuffix = ":rival_score" // rival score suffix

	TaskTypeNewBurst    = "new_burst"
	TaskTypeDoBurst     = "do_burst"
	TaskTypeUpdateBurst = "update_burst"
	TaskTypeBurstEvent  = "burst_event"
)

// GetBurstRivalListRedisKey 获取对手方爆仓log list redis key
func GetBurstRivalListRedisKey(_contractCode string) string {
	return fmt.Sprintf("%s:%s:force_rival_list", MutexSwapBurst, _contractCode)
}

// GetBurstRivalLongRankRedisKey 获取对手方多仓 redis key
// eg: fasset:BTC-USDT:long:rival_score
func GetBurstRivalLongRankRedisKey(_contractCode string) string {
	return AssetPrefix.Key(fmt.Sprintf("%s:long%s", _contractCode, RivalScoreSuffix))
}

// GetBurstRivalShortRankRedisKey 获取对手方空仓 redis key
// eg: fasset:BTC-USDT:short:rival_score
func GetBurstRivalShortRankRedisKey(_contractCode string) string {
	return AssetPrefix.Key(fmt.Sprintf("%s:short%s", _contractCode, RivalScoreSuffix))
}

// GetBurstListRedisKey 获取爆仓list的redis key
func GetBurstListRedisKey(_contractCode string, marginMode MarginMode) string {
	return fmt.Sprintf("%s:%d:%s:list", MutexSwapBurst, marginMode, _contractCode)
}

// GetBurstScanLockRedisKey 获取扫描爆仓锁的redis key
func GetBurstScanLockRedisKey(_userId string, marginMode MarginMode, _contractCode string) string {
	if marginMode == MarginModeCross {
		return fmt.Sprintf("%s:%s:%d:lock", MutexScanBurst, _userId, marginMode)
	} else {
		return fmt.Sprintf("%s:%s:%d:%s:lock", MutexScanBurst, _userId, marginMode, strings.ToLower(_contractCode))
	}
}

// GetTrialBurstScanLockRedisKey 获取体验金扫描爆仓锁的redis key
func GetTrialBurstScanLockRedisKey(_userId string, marginMode MarginMode, _contractCode string) string {
	if marginMode == MarginModeCross {
		return fmt.Sprintf("%s:%s:%d:lock", MutexTrialScanBurst, _userId, marginMode)
	} else {
		return fmt.Sprintf("%s:%s:%d:%s:lock", MutexTrialScanBurst, _userId, marginMode, strings.ToLower(_contractCode))
	}
}

// GetBurstLogListRedisKey 获取爆仓log list redis key
func GetBurstLogListRedisKey(_contractCode string) string {
	return fmt.Sprintf("%s:%s:log_list", MutexSwapBurst, _contractCode)
}

// GetBurstIndexListRedisKey 获取爆仓list的redis key
func GetBurstIndexListRedisKey(_contractCode string, marginMode MarginMode, _index string) string {
	return fmt.Sprintf("%s:%d:%s:%s_list", MutexSwapBurst, marginMode, _contractCode, _index)
}

// GetBurstLockRedisKey 获取爆仓锁的redis key
func GetBurstLockRedisKey(_userId string, marginMode MarginMode) string {
	return fmt.Sprintf("%s:%s:%d:lock", MutexSwapBurst, _userId, marginMode)
}

// GetRecoverBurstListRedisKey 获取爆仓list的recover redis key
// hash key=fasset:burst:1:BTC-USDT:recover_list key=burstId value= liquidationData
func GetRecoverBurstListRedisKey(_contractCode string, marginMode MarginMode) string {
	return fmt.Sprintf("%s:%d:%s:recover_list", MutexSwapBurst, marginMode, _contractCode)
}

// GetBurstUnlockListRedisKey 爆仓解锁队列
// list key=fasset:burst:unlock value= liquidationData
func GetBurstUnlockListRedisKey() string {
	return fmt.Sprintf("%s:unlock", MutexSwapBurst)
}

// GetBurstWorkingTaskRedisKey 获取爆仓进行中状态 redis key
// list key=fasset:burst:working:1:task:BTC-USDT value= liquidationData
// list key=fasset:burst:working:1:task:BTC-USDT_1 value= liquidationData
func GetBurstWorkingTaskRedisKey(marginMode MarginMode, _contractCode string) string {
	return fmt.Sprintf("%s:working:%d:task:%s", MutexSwapBurst, marginMode, _contractCode)
}

// GetBurstWorkingStatusRedisKey 获取爆仓进行中状态 redis key
// hash key=fasset:burst:working:1:status:uid key=contractCode|key=contractCode_side value=burst status
func GetBurstWorkingStatusRedisKey(marginMode MarginMode, _userId string) string {
	return fmt.Sprintf("%s:working:%d:status:%s", MutexSwapBurst, marginMode, _userId)
}

// GetBurstWorkingUsersRedisKey 获取爆仓进行中状态 redis key
// set key=fasset:burst:working:1:users value=uid
func GetBurstWorkingUsersRedisKey(marginMode MarginMode) string {
	return fmt.Sprintf("%s:working:%d:users", MutexSwapBurst, marginMode)
}

// GetBurstWorkingRecheckRedisKey 获取爆仓进行中状态 redis key
// set key=fasset:burst:working:1:recheck value=uid
func GetBurstWorkingRecheckRedisKey(marginMode MarginMode) string {
	return fmt.Sprintf("%s:working:%d:recheck", MutexSwapBurst, marginMode)
}

// GetTrialBurstLockRedisKey 获取体验金爆仓锁的redis key
func GetTrialBurstLockRedisKey(_userId string, marginMode MarginMode) string {
	return fmt.Sprintf("%s:%s:%d:lock", MutexSwapTrialBurst, _userId, marginMode)
}

// GetTrialBurstWorkingStatusRedisKey 获取爆仓进行中状态 redis key
// hash key=fasset:trial:burst:working:1:status:uid key=contractCode|key=contractCode_side value=burst status
func GetTrialBurstWorkingStatusRedisKey(marginMode MarginMode, _userId string) string {
	return fmt.Sprintf("%s:working:%d:status:%s", MutexSwapTrialBurst, marginMode, _userId)
}

// GetTrialBurstWorkingUsersRedisKey 获取体验金爆仓进行中状态 redis key
// set key=fasset:trial:burst:working:1:users value=uid
func GetTrialBurstWorkingUsersRedisKey(marginMode MarginMode) string {
	return fmt.Sprintf("%s:working:%d:users", MutexSwapTrialBurst, marginMode)
}

// GetTrialBurstWorkingRecheckRedisKey 获取爆仓进行中状态 redis key
// set key=fasset:trial:burst:working:1:recheck value=uid
func GetTrialBurstWorkingRecheckRedisKey(marginMode MarginMode) string {
	return fmt.Sprintf("%s:working:%d:recheck", MutexSwapTrialBurst, marginMode)
}

// GetBurstServerContractsRedisKey 获取爆仓服务支持币对 redis key
func GetBurstServerContractsRedisKey(serverId string) string {
	return fmt.Sprintf("%s:server:%s:contracts", MutexSwapBurst, serverId)
}

// 获取爆仓短信锁的redis key
func GetBurstSMSLockRedisKey(userId, coinPair, codeType string) string {
	return fmt.Sprintf("%s:%s:%s:%s_lock", MutexSwapBurst, userId, coinPair, codeType)
}

// 获取对手方等级推送锁redis key
func GetContractRivalRatePushLockRedisKey(base string, quote string, userId string, posSide int32) string {
	return fmt.Sprintf("%s:%s:%s-%s_%d:rival_cooldown", MutexSwapBurst, userId, strings.ToUpper(base), strings.ToUpper(quote), posSide)
}

// 获取对手方等级体验金仓位推送锁redis key
func GetContractTrialRivalRatePushLockRedisKey(base string, quote string, userId string, posSide int32) string {
	return fmt.Sprintf("%s:%s:%s-%s_%d:rival_cooldown", MutexSwapTrialBurst, userId, strings.ToUpper(base), strings.ToUpper(quote), posSide)
}
