package usecase

import (
	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
)

// AssetUseCase 资产用例实现
type AssetUseCase struct {
	config         *configs.Config
	assetRepo      repository.AssetRepository
	profitLossRepo repository.ProfitLossRepository
	priceRepo      repository.PriceRepository
	cacheRepo      repository.CacheRepository
	formulaRepo    repository.FormulaRepository
}

// AssetUseCaseParam 资产用例参数
type AssetUseCaseParam struct {
	dig.In

	Config         *configs.Config `name:"config"`
	AssetRepo      repository.AssetRepository
	ProfitLossRepo repository.ProfitLossRepository
	PriceRepo      repository.PriceRepository
	CacheRepo      repository.CacheRepository
	FormulaRepo    repository.FormulaRepository
}

// NewAssetUseCase 创建资产用例实例
func NewAssetUseCase(param AssetUseCaseParam) usecase.AssetUseCase {
	return &AssetUseCase{
		config:         param.Config,
		assetRepo:      param.AssetRepo,
		profitLossRepo: param.ProfitLossRepo,
		priceRepo:      param.PriceRepo,
		cacheRepo:      param.CacheRepo,
		formulaRepo:    param.FormulaRepo,
	}
}
