package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"
)

// PositionUseCase 仓位用例实现
type PositionUseCase struct {
	config       *configs.Config
	positionRepo repository.PositionRepository
}

// PositionUseCaseParam 仓位用例参数
type PositionUseCaseParam struct {
	dig.In

	Config       *configs.Config `name:"config"`
	PositionRepo repository.PositionRepository
}

// NewPositionUseCase 创建仓位用例实例
func NewPositionUseCase(param PositionUseCaseParam) usecase.PositionUseCase {
	return &PositionUseCase{
		config:       param.Config,
		positionRepo: param.PositionRepo,
	}
}

// PlatPosDetail implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error) {
	return uc.positionRepo.PlatPosDetail(ctx, req)
}

// PlatPosList implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosList(ctx context.Context) (repository.PlatPosList, error) {
	return uc.positionRepo.PlatPosList(ctx)
}

// PosInfo implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error) {
	return uc.positionRepo.PosInfo(ctx, param)
}

// PosTotal implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosTotal(ctx context.Context, contractCode string) decimal.Decimal {
	return uc.positionRepo.PosTotal(ctx, contractCode)
}

// QueryUserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error) {
	return uc.positionRepo.QueryUserPos(ctx, req)
}

// UserHoldPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error) {
	return uc.positionRepo.UserHoldPos(ctx, req)
}

// UserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error) {
	return uc.positionRepo.UserPos(ctx, req)
}
