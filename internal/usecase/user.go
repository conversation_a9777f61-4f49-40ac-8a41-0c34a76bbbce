package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
)

// UserUseCase 用户用例实现
type UserUseCase struct {
	config   *configs.Config
	userRepo repository.UserRepository
}

// UserUseCaseParam 用户用例参数
type UserUseCaseParam struct {
	dig.In

	Config   *configs.Config `name:"config"`
	UserRepo repository.UserRepository
}

// NewUserUseCase 创建用户用例实例
func NewUserUseCase(param UserUseCaseParam) usecase.UserUseCase {
	return &UserUseCase{
		config:   param.Config,
		userRepo: param.UserRepo,
	}
}

// AdjustCross implements usecase.UserUseCase.
func (use *UserUseCase) AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error) {
	return use.userRepo.AdjustCross(ctx, p)
}

// AdjustHoldMode implements usecase.UserUseCase.
func (use *UserUseCase) AdjustHoldMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error) {
	return use.userRepo.AdjustHoldMode(ctx, p)
}

// AdjustLeverage implements usecase.UserUseCase.
func (use *UserUseCase) AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, error) {
	return use.userRepo.AdjustLeverage(ctx, p)
}

// AdjustLeverageMargin implements usecase.UserUseCase.
func (use *UserUseCase) AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error) {
	return use.userRepo.AdjustLeverageMargin(ctx, p)
}

// AdjustMargin implements usecase.UserUseCase.
func (use *UserUseCase) AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error) {
	return use.userRepo.AdjustMargin(ctx, p)
}

// AdjustMarginCfg implements usecase.UserUseCase.
func (use *UserUseCase) AdjustMarginCfg(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error) {
	return use.userRepo.AdjustMarginCfg(ctx, p)
}

// ChangeJoinMargin implements usecase.UserUseCase.
func (use *UserUseCase) ChangeJoinMargin(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error) {
	return use.userRepo.ChangeJoinMargin(ctx, p)
}

// ChangeOrderConfig implements usecase.UserUseCase.
func (use *UserUseCase) ChangeOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error) {
	return use.userRepo.ChangeOrderConfig(ctx, p)
}

// ChangeOrderConfirm implements usecase.UserUseCase.
func (use *UserUseCase) ChangeOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error) {
	return use.userRepo.ChangeOrderConfirm(ctx, p)
}

// LoadJoinMargin implements usecase.UserUseCase.
func (use *UserUseCase) LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error) {
	return use.userRepo.LoadJoinMargin(ctx, param)
}

// GetOrderConfig implements usecase.UserUseCase.
func (use *UserUseCase) GetOrderConfig(ctx context.Context, param *repository.CommonParam) (repository.OrderConfigRes, error) {
	return use.userRepo.GetOrderConfig(ctx, param)
}

// GetOrderConfirm implements usecase.UserUseCase.
func (use *UserUseCase) GetOrderConfirm(ctx context.Context, param *repository.CommonParam) (repository.OrderConfirmRes, error) {
	return use.userRepo.GetOrderConfirm(ctx, param)
}

// GetUserAsset implements usecase.UserUseCase.
func (use *UserUseCase) GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset) {
	return use.userRepo.GetUserAsset(ctx, param)
}

// SwapInit implements usecase.UserUseCase.
func (use *UserUseCase) SwapInit(ctx context.Context, param *repository.SwapInit) domain.Code {
	return use.userRepo.SwapInit(ctx, param)
}

// UserHoldMode implements usecase.UserUseCase.
func (use *UserUseCase) UserHoldMode(ctx context.Context, param *repository.CommonParam) (repository.HoldModeRes, error) {
	return use.userRepo.UserHoldMode(ctx, param)
}

// UserLeverage implements usecase.UserUseCase.
func (use *UserUseCase) UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error) {
	return use.userRepo.UserLeverage(ctx, param)
}

// GetUserOpenCloseTimes implements usecase.UserUseCase.
func (use *UserUseCase) GetUserOpenCloseTimes(ctx context.Context, param *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error) {
	return use.userRepo.GetUserOpenCloseTimes(ctx, param)
}

// GetUserStatistics implements usecase.UserUseCase.
func (use *UserUseCase) GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error) {
	return use.userRepo.GetUserStatistics(ctx, param)
}
