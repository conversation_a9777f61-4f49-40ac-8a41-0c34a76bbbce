package usecase

import (
	"reflect"
	"testing"

	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/setting"
)

func TestFilterByWhiteList(t *testing.T) {
	type args struct {
		uid       string
		asset     []repository.AssetDetail
		configMap map[string]setting.CurrencyWhiteListConfig
	}
	tests := []struct {
		name string
		args args
		want []repository.AssetDetail
	}{
		{
			name: "",
			args: args{
				uid: "00000001",
				asset: []repository.AssetDetail{{
					Currency: "btc",
				}},
				configMap: map[string]setting.CurrencyWhiteListConfig{
					"BTC": {
						Currency: "btc",
						CurrencyDetail: map[string]setting.WhiteList{
							"btc20": {
								UserWhiteState: true,
								UserWhiteList:  []string{"00000001"},
							},
							"erc20": {
								UserWhiteState: false,
								UserWhiteList:  nil,
							},
						},
					},
				},
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := filterByWhiteList(tt.args.uid, tt.args.asset, tt.args.configMap); !reflect.DeepEqual(got, tt.want) {
				t.Log(got)
			}
		})
	}
}
