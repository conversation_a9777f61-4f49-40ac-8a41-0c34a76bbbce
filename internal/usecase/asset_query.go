package usecase

import (
	"context"
	"net/http"
	"sort"
	"strings"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/pkg/setting"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"github.com/thoas/go-funk"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// UserAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) UserAsset(ctx context.Context, param *repository.SwapParam) (*repository.AssetSwap, error) {
	// 获取用户资产
	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Currency)
	if err != nil {
		return asset, err
	}

	return asset, nil
}

// BatchUserAsset implements usecase.AssetUseCase
func (use *AssetUseCase) BatchUserAsset(ctx context.Context, param usecase.AssetParam) ([]repository.BatchAssetSwap, error) {
	data := make([]repository.BatchAssetSwap, 0, len(param.UserIds))

	assetList, err := use.assetRepo.GetBtachUserAsset(ctx, repository.BalanceReq{
		UIDs:       param.UserIds,
		Currencies: []string{strings.ToUpper(param.Currency)},
	})
	if err != nil {
		return data, err
	}

	for _, asset := range assetList {
		data = append(data, repository.BatchAssetSwap{
			UID:          asset.UID,
			Currency:     asset.Currency,
			TotalBalance: asset.Balance,
		})
	}

	return data, nil
}

// GetUserAssetAndPos implements usecase.AssetUseCase
func (use *AssetUseCase) GetUserAssetAndPos(ctx context.Context, param *repository.SwapParam) (domain.Code, repository.ReqUserAssetAndPos) {
	res := repository.ReqUserAssetAndPos{}
	res.PosList = []repository.PosData{}
	// 获取用户资产
	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Currency)
	if err != nil {
		logrus.Errorf("query asset err: %+v", err)
		return domain.Code252404, res
	}

	res.Asset.UID = asset.UID
	res.Asset.Balance = asset.Balance
	crossMarginBalance, err := use.assetRepo.CrossMarginBalance(ctx, asset, param.UID, param.Currency)
	if err != nil {
		logrus.Errorf("GetUserAssetAndPos CrossMarginBalance err: %+v", err)
		return domain.Code252407, res
	}
	res.Asset.Available = crossMarginBalance.Sub(use.assetRepo.HoldCostTotalCross(ctx, asset, "")).
		Sub(use.assetRepo.FrozenTotal(ctx, asset, param.Currency)).Truncate(domain.CurrencyPrecision)

	uTotalBalance := decimal.Zero
	for _, currency := range domain.CurrencyList {
		if currency == domain.CurrencyUSDT {
			uTotalBalance = uTotalBalance.Add(asset.CBalance(currency))
		} else {
			uRate := use.priceRepo.SpotURate(ctx, currency)
			uTotalBalance = uTotalBalance.Add(asset.CBalance(currency).Mul(uRate))
		}
	}

	btcRate := use.priceRepo.SpotURate(ctx, "BTC")
	res.Asset.BtcBalance = uTotalBalance.Mul(btcRate)

	res.Asset.CnyBalance = uTotalBalance.Mul(domain.CnyRate).Truncate(domain.CnyPrecision)
	res.Asset.ProfitUnreal = use.assetRepo.CrossUnrealTotal(ctx, asset, "")
	res.Asset.MarginBalance = crossMarginBalance.Add(use.assetRepo.HoldCostTotalIsolated(ctx, asset, "", true)).Truncate(domain.CurrencyPrecision)
	if !res.Asset.MarginBalance.IsZero() {
		res.Asset.ProfitRate = res.Asset.ProfitUnreal.Div(res.Asset.MarginBalance).Truncate(domain.RatePrecision).Mul(decimal.NewFromInt(100))
	}

	// 更新爆仓价格
	updateLiquidation := func(asset *repository.AssetSwap, poss []repository.PosSwap, isTrial bool) (posList []repository.PosData, code domain.Code) {
		shortPos := repository.PosSwap{}
		longPos := repository.PosSwap{}
		bothPos := repository.PosSwap{}
		for _, pos := range poss {
			if pos.IsTrial() == isTrial {
				if pos.PosSide == domain.LongPos {
					longPos = pos
				} else if pos.PosSide == domain.ShortPos {
					shortPos = pos
				} else if pos.PosSide == domain.BothPos {
					bothPos = pos
				}
			}
		}

		for _, pos := range []repository.PosSwap{longPos, shortPos, bothPos} {
			if pos.Pos.Sign() == 0 {
				continue
			}
			posData, err := use.LoadPosDataWithPosSwap(ctx, pos, asset)
			if err != nil {
				return nil, domain.Code252407
			}

			posSwap := repository.PosQuery{
				PosSwap: repository.PosSwap{
					Currency:        pos.Currency,
					Leverage:        pos.Leverage,
					IsolatedMargin:  pos.IsolatedMargin,
					Liquidation:     pos.Liquidation,
					LiquidationType: domain.LiquidationType(pos.LiquidationType),
					MarginMode:      pos.MarginMode,
					OpenPriceAvg:    pos.OpenPriceAvg.Truncate(domain.PricePrecision),
					OpenTime:        pos.OpenTime / 1e9,
					PosAvailable:    pos.PosAvailable,
					Pos:             pos.Pos,
					PosSide:         pos.PosSide,
					ContractCode:    pos.ContractCode,
					UID:             pos.UID,
					UserType:        int32(pos.UserType),
					PosStatus:       domain.PosStatus(pos.PosStatus),
					ProfitReal:      pos.ProfitReal,
					Subsidy:         pos.Subsidy,
				},
				Margin: pos.IsolatedMargin,
			}

			if !isTrial {
				totalBalance := asset.CBalance(pos.Currency)
				rate := decimal.NewFromInt(1)
				if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
					totalBalance, err = use.assetRepo.TotalJoinBalance(ctx, asset)
					if err != nil {
						logrus.Errorf("GetUserAssetAndPos longPos TotalJoinBalance err: %+v", err)
						return nil, domain.Code252407
					}
					rate = use.priceRepo.SpotURate(ctx, pos.Currency)
				}

				markPrice := use.priceRepo.GetMarkPrice(ctx, posData.ContractCode)
				posData.Liquidation = use.formulaRepo.CalcLiquidationPrice(&posSwap, &longPos, &shortPos, &bothPos,
					use.assetRepo.HoldCostTotalIsolated(ctx, asset, longPos.Currency, true).Mul(rate),
					totalBalance, use.assetRepo.OtherCrossUnreal(ctx, asset, longPos.Currency).Mul(rate),
					use.assetRepo.OtherCrossMaintainMargin(ctx, asset, longPos.Currency).Mul(rate), markPrice)
			} else {
				markPrice := use.priceRepo.GetMarkPrice(ctx, posData.ContractCode)
				posData.Liquidation = use.formulaRepo.CalcLiquidationPrice(&posSwap, &longPos, &shortPos, &bothPos,
					decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, markPrice)
			}
			posList = append(posList, posData)
		}
		code = http.StatusOK

		return
	}

	for _, isolated := range asset.IsolatedList {
		if len(isolated) == 0 {
			continue
		}

		poss, code := updateLiquidation(asset, isolated, false)
		if code != http.StatusOK {
			return code, res
		}
		res.PosList = append(res.PosList, poss...)

		poss, code = updateLiquidation(asset, isolated, true)
		if code != http.StatusOK {
			return code, res
		}
		res.PosList = append(res.PosList, poss...)
	}

	for _, cross := range asset.CrossList {
		if len(cross) == 0 {
			continue
		}

		poss, code := updateLiquidation(asset, cross, false)
		if code != http.StatusOK {
			return code, res
		}
		res.PosList = append(res.PosList, poss...)

		poss, code = updateLiquidation(asset, cross, true)
		if code != http.StatusOK {
			return code, res
		}
		res.PosList = append(res.PosList, poss...)
	}

	return http.StatusOK, res
}

// SumUserTotalAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) SumUserTotalAsset(ctx context.Context) (repository.ResSumUserTotalAsset, error) {
	// 获取机器人用户id
	userIds := domain.RobotUsers.Keys()

	res := repository.ResSumUserTotalAsset{}
	userAsset, err := use.assetRepo.SumUserTotalAsset(ctx, repository.TotalAssetReq{
		UIDs:      nil,
		UIDsNotIn: userIds,
	})
	if err != nil {
		return res, err
	}

	res.UserAsset = LoadAssetToRes(userAsset)
	if len(userIds) > 0 {
		robotAsset, err := use.assetRepo.SumUserTotalAsset(ctx, repository.TotalAssetReq{
			UIDs:      userIds,
			UIDsNotIn: nil,
		})
		if err != nil {
			return res, err
		}
		res.RobotAsset = LoadAssetToRes(robotAsset)
	}

	return res, nil
}

func (use *AssetUseCase) LoadPosDataWithPosSwap(ctx context.Context, pos repository.PosSwap, asset *repository.AssetSwap) (repository.PosData, error) {
	posData := repository.PosData{}
	posData.ContractCode = pos.ContractCode
	posData.Currency = pos.Currency
	posData.UID = pos.UID
	posData.Leverage = pos.Leverage
	posData.Pos = pos.Pos
	posData.OpenPriceAvg = pos.OpenPriceAvg
	posData.MarginMode = pos.MarginMode
	posData.PosSide = pos.PosSide
	posData.AwardOpIds = pos.AwardOpIds

	posData.Margin = pos.HoldCost().Truncate(domain.CurrencyPrecision)
	posData.ProfitUnreal = pos.CalcProfitUnreal(pos.MarkPrice)

	posData.MarkPrice = use.priceRepo.GetMarkPrice(ctx, posData.ContractCode)
	marginBalance, maintenanceMargin, err := use.assetRepo.GetMarginBalanceAndMaintainMargin(ctx, asset, pos)
	if err != nil {
		return posData, errors.Wrap(err, "GetMarginBalanceAndMaintainMargin")
	}
	if marginBalance.Sign() != 0 {
		posData.RiskRate = maintenanceMargin.Div(marginBalance).Truncate(domain.RatePrecision).Mul(decimal.NewFromInt(100))
	}

	return posData, nil
}

// LoadAssetToRes 结构转换
func LoadAssetToRes(assets []entity.Wallet) []repository.ResSumUserTotalAssetItem {
	res := make([]repository.ResSumUserTotalAssetItem, 0)
	for _, v := range assets {
		am := repository.ResSumUserTotalAssetItem{Currency: v.Currency, Balance: v.Balance}
		res = append(res, am)
	}

	return res
}

func (use *AssetUseCase) TotalBalance(ctx context.Context, param usecase.ReqTotalBalance) (domain.Code, repository.ResTotalBalance) {
	// todo 没有体验金逻辑
	res := repository.ResTotalBalance{
		Currency: param.Currency,
	}
	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Currency)
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, res
	}

	uTotalBalance, err := use.assetRepo.TotalJoinBalance(ctx, asset)
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, res
	}
	uTotalTrialBalance, err := asset.TotalJoinTrialBalance()
	if err != nil {
		logrus.Errorf("query trial asset err: %s", err.Error())
		return domain.Code252404, res
	}

	res.TotalTotal = uTotalBalance.Add(uTotalTrialBalance).Add(use.assetRepo.CrossUnrealTotal(ctx, asset, param.Currency)).Truncate(domain.CurrencyPrecision)
	res.TrialBalance = uTotalTrialBalance
	rate := use.priceRepo.SpotURate(ctx, param.Currency)
	res.TotalProfit = use.profitLossRepo.GetTotalProfit(ctx, param.UID)
	if param.Currency != domain.CurrencyUSDT {
		if !rate.IsZero() {
			res.TotalProfit = res.TotalProfit.Div(rate).Truncate(domain.CurrencyPrecision)
			res.TotalTotal = res.TotalTotal.Div(rate).Truncate(domain.CurrencyPrecision)
			res.TrialBalance = res.TrialBalance.Div(rate).Truncate(domain.CurrencyPrecision)
		}
	}

	return http.StatusOK, res
}

func (use *AssetUseCase) AssetDetail(ctx context.Context, param usecase.ReqAssetDetail) (domain.Code, repository.ResAssetDetail) {
	// todo 体验金需要等规则
	res := repository.ResAssetDetail{
		UID:     param.UID,
		PosList: []repository.PosSwap{},
	}
	asset, err := use.cacheRepo.Load(ctx, param.UID, "")
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		return domain.Code252404, res
	}
	res.AssetMode = futuresassetpb.AssetMode_ASSET_MODE_MULTI
	res.Frozen = asset.Frozen

	for i := range domain.CurrencyList {
		assetAmount, ok := asset.Balance[domain.CurrencyList[i]]
		if !ok {
			assetAmount = decimal.Zero
		}
		assetTrialAmount, ok := asset.TrialBalance[domain.CurrencyList[i]]
		if !ok {
			assetTrialAmount = decimal.Zero
		}
		assetD := repository.AssetDetail{
			Currency:     domain.CurrencyList[i],
			TrialBalance: assetTrialAmount,
			Balance:      assetAmount,
		}
		res.AssetList = append(res.AssetList, assetD)
	}
	for _, posList := range asset.CrossList {
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			v.IsolatedMargin = v.HoldCost()

			res.PosList = append(res.PosList, v)
		}
	}
	for _, posList := range asset.IsolatedList {
		for _, v := range posList {
			if v.Pos.IsZero() {
				continue
			}
			res.PosList = append(res.PosList, v)
		}
	}
	sort.Sort(repository.PosSwapSlice(res.PosList))
	configMap, err := setting.Service.GetCoinWhiteListConfigMap()
	if err == nil {
		assetDetails := filterByWhiteList(param.UID, res.AssetList, configMap)
		res.AssetList = assetDetails
	}

	return domain.CodeOk, res
}

func filterByWhiteList(uid string, asset []repository.AssetDetail, configMap map[string]setting.CurrencyWhiteListConfig) []repository.AssetDetail {
	r := make([]repository.AssetDetail, 0)
	for i := 0; i < len(asset); i++ {
		config, ok := configMap[strings.ToUpper(asset[i].Currency)]
		if ok {
			// 遍历全部链上的白名单
			for _, whiteListconfig := range config.CurrencyDetail {
				// 如果某个链上没配白名单，可以看见
				if !whiteListconfig.UserWhiteState {
					r = append(r, asset[i])

					break
				}
				// 配了白名单，并且在白名单里，可以看见
				if whiteListconfig.UserWhiteState && funk.ContainsString(whiteListconfig.UserWhiteList, uid) {
					r = append(r, asset[i])

					break
				}
			}
		} else {
			r = append(r, asset[i])
		}
	}

	return r
}
