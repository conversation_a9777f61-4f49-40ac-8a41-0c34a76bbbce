package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
)

// TrialUseCase 资产用例实现
type TrialUseCase struct {
	config    *configs.Config
	trialRepo repository.TrialRepository
}

// TrialAssetUseCaseParam 资产用例参数
type TrialAssetUseCaseParam struct {
	dig.In

	Config    *configs.Config `name:"config"`
	TrialRepo repository.TrialRepository
}

// NewTrialAssetUseCase 创建资产用例实例
func NewTrialAssetUseCase(param TrialAssetUseCaseParam) usecase.TrialUseCase {
	return &TrialUseCase{
		config:    param.Config,
		trialRepo: param.TrialRepo,
	}
}

// GetNoInvalidTrial implements usecase.TrialUseCase.
func (t *TrialUseCase) GetNoInvalidTrial(ctx context.Context, req *repository.GetNoInvalidTrialReq) ([]*entity.TrialAsset, error) {
	return t.trialRepo.GetNoInvalidTrial(ctx, req)
}

// GetTrialAssetDetailList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetDetailList(ctx context.Context, req *repository.GetTrialAssetDetailListReq) ([]*entity.TrialAssetDetailInfo, int64, error) {
	return t.trialRepo.GetTrialAssetDetailList(ctx, req)
}

// GetTrialAssetList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetList(ctx context.Context, req *repository.TAListReq) (repository.TAListRes, error) {
	return t.trialRepo.GetTrialAssetList(ctx, req)
}

// GetTrialAssetSummaryList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetSummaryList(ctx context.Context, req *repository.GetTrialAssetSummaryListReq) ([]*entity.TrialAssetSummaryInfo, int64, error) {
	return t.trialRepo.GetTrialAssetSummaryList(ctx, req)
}

// TrialAssetList implements usecase.TrialUseCase.
func (t *TrialUseCase) TrialAssetList(ctx context.Context, req *entity.TrialAssetQuery, param *repository.TAListReq) ([]*entity.TrialAsset, int64, error) {
	return t.trialRepo.TrialAssetList(ctx, req, param)
}

func (t *TrialUseCase) GetLastUpdateTime(ctx context.Context, uid string) (int64, error) {
	return t.trialRepo.GetLastUpdateTime(ctx, uid)
}

func (t *TrialUseCase) AddTrialAsset(ctx context.Context, req *repository.TrialBase) error {
	return t.trialRepo.AddTrialAsset(ctx, req)
}

func (t *TrialUseCase) RecycleTrialAsset(ctx context.Context, req *repository.OperateRecycleTrialAsset) error {
	return t.trialRepo.RecycleTrialAsset(ctx, req)
}
