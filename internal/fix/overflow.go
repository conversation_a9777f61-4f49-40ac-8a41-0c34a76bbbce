package fix

import (
	"encoding/json"
	"futures-asset/handler/monitor"
	"futures-asset/internal/domain/db/swap"
	"futures-asset/pkg/sqllib"
	"strings"

	"github.com/sirupsen/logrus"
)

func FixOverflow() {
	db, err := sqllib.Db()
	if err != nil {
		logrus.Errorf("get database error: %v", err)
		return
	}
	var bursts []swap.BurstSwap
	db.Where("overflow_value = 1 AND overflow_time > 0").Find(&bursts)
	for _, burst := range bursts {
		if !strings.Contains(burst.OverflowInfo, "[") {
			continue
		}
		var fees monitor.BurstOverflow
		err = json.Unmarshal([]byte(burst.OverflowInfo), &fees)
		if err != nil {
			logrus.Errorf("unmarshal %v-%v error: %v", burst.UID, burst.OverflowInfo, err)
			continue
		}
		if len(fees) == 0 {
			continue
		}
		burst.OverflowValue = fees.TotalAmount()
		db.Debug().Model(&burst).Update("overflow_value", fees.TotalAmount())
	}
}
