package fix

import (
	"time"

	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/sqllib"

	"github.com/sirupsen/logrus"
)

func FixHistoryTrial() {
	db, err := sqllib.Db()
	if err != nil {
		logrus.Error("db connect error", err)
		return
	}

	db.Debug().Model(&entity.TrialAsset{}).Where("type = 1 AND max_leverage = 1 AND min_hold_time = 0").Updates(map[string]interface{}{
		"type":         0,
		"max_leverage": 0,
	})

	var trials []entity.TrialAsset
	db.Where("(type = 0 OR type IS NULL) AND trial_asset_status <= ?", domain.TrialAssetStatusWarn).Find(&trials)
	logrus.Infof("get trials total : %d", len(trials))

	userTrialMap := make(map[string][]entity.TrialAsset)
	for _, trial := range trials {
		userTrialMap[trial.UID] = append(userTrialMap[trial.UID], trial)
	}

	// 按照用户ID分组
	for uid, trials := range userTrialMap {
		var opIds []string
		for _, trial := range trials {
			opIds = append(opIds, trial.AwardOpId)
		}

		mutex := redislib.NewMutex(domain.MutexSwapPosLock+uid, 2*time.Second)
		if mutex.Lock() != nil {
			logrus.Error("get lock error: %v", uid)
			return
		}
		defer mutex.Unlock()

		userCache := swapcache.NewPosCache(swapcache.CacheParam{
			TradeCommon: repository.TradeCommon{Quote: "USDT"},
		}, uid)
		asset, err := userCache.Load()
		if err != nil {
			logrus.Error("load user asset error", err)
			continue
		}

		trialDetails := asset.TrialDetail.Gets(opIds...)
		if trialDetails == nil {
			logrus.Errorf("get trial(%v-%v) detail error", uid, opIds)
			continue
		}
		for idx := range trialDetails {
			trialDetails[idx].Type = domain.TrialAssetTypeOnce
			trialDetails[idx].MaxLeverage = 20
			trialDetails[idx].MinHoldTime = 30 * 60
		}
		err = db.Model(&entity.TrialAsset{}).Where("award_op_id IN (?)", opIds).Updates(map[string]interface{}{
			"type":          domain.TrialAssetTypeOnce,
			"max_leverage":  20,
			"min_hold_time": 30 * 60,
		}).Error
		if err != nil {
			logrus.Error("update trial asset error", err)
			continue
		} else {
			logrus.Infof("update trial asset success: %v", uid)
		}

		err = userCache.UpdateTrialDetail(asset)
		if err != nil {
			logrus.Error("update user asset error", err)
			continue
		} else {
			logrus.Infof("update user asset success: %v", uid)
		}

	}
}
