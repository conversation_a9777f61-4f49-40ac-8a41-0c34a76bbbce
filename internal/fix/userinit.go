package fix

import (
	"fmt"
	"futures-asset/cache/cachekey"
	"futures-asset/cache/swapcache"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/setting"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 修复用户默认值问题
func FixUserInit() {
	spotkey := "CONTRACT_HAS_AGREE"

	userIds, err := redislib.RedisSpot().HGetAll(spotkey)
	if err != nil {
		logrus.Errorf("get user list from redis error: %v", err)
		return
	}
	logrus.Infof("FixUserInit get user list from redis success, user count: %d", len(userIds))

	contractSettingMap, err := setting.Service.GetAllPairSettingInfo()
	if err != nil {
		logrus.Error("makeFundRates GetAllPairSettingInfo err.")
		return
	}
	usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	for uid, value := range userIds {
		if value == "0" {
			redislib.Redis().HDel(usersKey, uid)
			logrus.Infof("del user to redis success, user id: %s", uid)
			continue
		}
		val, _ := redislib.Redis().HGet(usersKey, uid)

		if val == "" {
			err = redislib.Redis().HSet(usersKey, uid, "{}")
			if err != nil {
				logrus.Errorf("set user  to redis error: %v", err)
				continue
			}
			logrus.Infof("set user to redis success, user id: %s", uid)

			mutex := redislib.NewMutex(domain.MutexSwapPosLock+uid, 30*time.Second)
			if mutex.Lock() != nil {
				continue
			}
			defer mutex.Unlock()

			userCache := swapcache.NewPosCache(swapcache.CacheParam{}, uid)
			asset, err := userCache.Load()
			if err != nil {
				logrus.Errorf("query asset err: %s", err.Error())
				continue
			}
			for contractCode, contractSetting := range contractSettingMap {
				asset.Leverage = append(asset.Leverage, &repository.Leverage{
					ContractCode: strings.ToUpper(contractCode),
					MarginMode:   domain.MarginModeCross,
					Leverage:     contractSetting.DefaultLeverage,
					LLeverage:    contractSetting.DefaultLeverage,
					SLeverage:    contractSetting.DefaultLeverage,
					BLeverage:    contractSetting.DefaultLeverage,
				})
			}

			err = userCache.FixInitUser(asset.Leverage)
			if err != nil {
				logrus.Error(fmt.Sprintf("init base info err:%+v, userCache:%+v, leverage:%+v", err, userCache, asset.Leverage))
			}
		}

	}
}

// 检查用户默认值问题
func CheckUserInit() {
	spotkey := "CONTRACT_HAS_AGREE"

	userIds, err := redislib.RedisSpot().HGetAll(spotkey)
	if err != nil {
		logrus.Errorf("get user list from redis error: %v", err)
		return
	}
	logrus.Infof("FixUserInit get user list from redis success, user count: %d", len(userIds))

	usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	var deleteTotal int
	var initTotal int
	for uid, value := range userIds {
		if value == "0" {
			deleteTotal++
			continue
		}
		val, _ := redislib.Redis().HGet(usersKey, uid)
		if val == "" {
			initTotal++
			logrus.Infof("CheckUserInit userid:%s no init", uid)
		}
	}
	logrus.Infof("CheckUserInit deleteTotal:%d, initTotal:%d", deleteTotal, initTotal)
}
