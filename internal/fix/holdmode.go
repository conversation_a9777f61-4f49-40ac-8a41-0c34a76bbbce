package fix

import (
	"futures-asset/cache"
	"futures-asset/internal/domain"
	"strconv"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

// 修复持仓模式的默认值问题
// 1. 从redis中获取所有用户
// 2. 判断redis里面是不是存在hold:mode的字段
// 3. 如果不存在，那么就设置为1
func FixHoldMode() {
	usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	userIds, err := redislib.Redis().HGetAll(usersKey)
	if err != nil {
		logrus.Errorf("get user list from redis error: %v", err)
		return
	}
	logrus.Infof("get user list from redis success, user count: %d", len(userIds))

	for _, uid := range userIds {
		key := cache.Prefix + uid
		val, err := redislib.Redis().HGet(key, cache.HoldModeSuffix)
		isExists := true
		if err != nil {
			if err != redis.Nil {
				logrus.Errorf("get user hold mode from redis error: %v", err)
				continue
			}
			if err == redis.Nil {
				isExists = false
			}
		}

		if isExists && val == "" {
			logrus.Infof("user hold mode is empty, user id: %s", uid)
			isExists = false
		}

		if !isExists {
			err = redislib.Redis().HSet(key, cache.HoldModeSuffix, strconv.Itoa(domain.HoldModeHedge))
			if err != nil {
				logrus.Errorf("set user hold mode to redis error: %v", err)
				continue
			}
			logrus.Infof("set user hold mode to redis success, user id: %s", uid)
		}
	}
}
