/**
 * asset message push
 */
package message

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"futures-asset/internal/domain"
	"futures-asset/pkg/mqlib"

	"github.com/sirupsen/logrus"
)

type message struct {
	amqp       *mqlib.Amqp
	queueIndex string
	uid        string
	topic      string
	routeKey   string
}

type AccountMsg struct {
	MsgType string      `json:"msgType"`
	MsgData interface{} `json:"msgData"`
}

const (
	pushKeyPrefix   = "contract_push_bindkey_"
	SwapAccount     = "swap" // 永续合约账户
	AssetQueueIndex = "0"    // 资产推送队列索引
	PosQueueIndex   = "1"    // 仓位推送队列索引
	BillQueueIndex  = "1"    // 账单推送队列索引

	AccountMsgTypeAsset       = "asset"       // 资产推送类型
	AccountMsgTypeTrialAsset  = "trialAsset"  // 体验金资产推送类型
	AccountMsgTypeTrialDetail = "trialDetail" // 体验金详情
	AccountMsgTypeAgree       = "agree"       // 开通合约推送类型
	AccountMsgTypeHoldMode    = "hold_mode"   // 持仓模式推送类型
	AccountMsgTypeJoinMode    = "join_mode"   // 混合保证金模式推送类型
)

func New(uid, index string, _amqp *mqlib.Amqp) *message {
	if _amqp == nil {
		var err error
		_amqp, err = mqlib.New()
		if err != nil {
			logrus.Error(fmt.Sprintf("mqlib.New err:%+v", err))
		}
	}
	return &message{
		uid:        uid,
		queueIndex: index,
		routeKey:   pushKeyPrefix + index,
		amqp:       _amqp,
	}
}

// TopicAsset 资产推送
func (slf *message) TopicAsset(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.accounts", accountType)
	return slf
}

// TopicAgree 开通推送
func (slf *message) TopicAgree(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.accounts", accountType)
	return slf
}

// TopicBill 账单推送
func (slf *message) TopicBill(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.bills", accountType)
	return slf
}

// TopicPos 仓位推送
func (slf *message) TopicPos(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.positions", accountType)
	return slf
}

// TopicMarkPrice 标记价格推送
func (slf *message) TopicMarkPrice(accountType, code string) *message {
	slf.topic = fmt.Sprintf("%s.market.mark_price:%s", accountType, strings.ToUpper(code))
	return slf
}

// TopicRivalRatePrice 对手方排名率推送
func (slf *message) TopicRivalRatePrice(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.positions.rival_rate", accountType)
	return slf
}

// TopicLeverage 杠杆倍数和仓位模式推送
func (slf *message) TopicLeverage(accountType string) *message {
	slf.topic = fmt.Sprintf("%s.leverage", accountType)
	return slf
}

// TopicFundRate 资金费用推送
func (slf *message) TopicFundRate(accountType, code string) *message {
	slf.topic = fmt.Sprintf("%s.market.funding_rate:%s", accountType, strings.ToUpper(code))
	return slf
}

// TopicTotalPos 平台总持仓
func (slf *message) TopicTotalPos(accountType, code string) *message {
	slf.topic = fmt.Sprintf("%s.market.total_pos:%s", accountType, strings.ToUpper(code))
	return slf
}

// TopicUserWinRate 用户平仓胜率
func (slf *message) TopicUserWinRate(accountType string, code string) *message {
	slf.topic = fmt.Sprintf("%s.user.win_rate:%s", accountType, strings.ToUpper(code))
	return slf
}

func (slf *message) Push(data interface{}) {
	pushData := PushData{
		QueueIndex: slf.queueIndex,
		Topic:      slf.topic,
		UID:        slf.uid,
		Data:       data,
	}

	bytes, err := json.Marshal(pushData)
	if err != nil {
		log.Printf("push asset err when marshal: %s", err.Error())
	}

	if slf.amqp == nil {
		logrus.Error(fmt.Sprintf("push message to mq amqp is nil, slf:%+v data:%+v", slf, data))
		return
	}

	err = slf.amqp.Send(domain.PushServerExchangeName, slf.routeKey, bytes)
	if err != nil {
		log.Printf("push asset err: %s", err.Error())
	}
}
