package rabbit

import (
	"fmt"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/sirupsen/logrus"

	"futures-asset/internal/domain"
	"futures-asset/pkg/mqlib"
)

func InitExchangeAndQueue() {
	_amp, err := mqlib.New()
	if err != nil {
		logrus.Error(fmt.Sprintf("mqlib.New err:%+v", err))
		return
	}

	// 创建 exchange
	createExchange(_amp, domain.ContractExchange, amqp.ExchangeDirect)

	// 创建 queue
	createQueue(_amp, domain.ContractOperateQueue)
	createQueue(_amp, domain.ContractAddTrialAssetQueue)

	// 绑定 queue
	bindQueue(_amp, domain.ContractOperateQueue, domain.ContractAssetBindKey, domain.ContractExchange)
	bindQueue(_amp, domain.ContractAddTrialAssetQueue, domain.ContractAddTrialAssetKey, domain.ContractExchange)
}

func createExchange(_amp *mqlib.Amqp, _exchange, _exchangeKind string) {
	err := _amp.DeclareExchange(_exchange, _exchangeKind)
	if err != nil {
		logrus.Error(fmt.Sprintf("Declare Exchange err:%+v,_exchange:%s,_exchangeKind:%s", err, _exchange, _exchangeKind))
	}
}

func createQueue(_amp *mqlib.Amqp, _queue string) {
	err := _amp.DeclareQueue(_queue)
	if err != nil {
		logrus.Error(fmt.Sprintf("Declare Queue err:%+v,_queue:%s", err, _queue))
	}
}

func bindQueue(_amp *mqlib.Amqp, _queueName, _key, _exchange string) {
	err := _amp.BindQueue(_queueName, _key, _exchange)
	if err != nil {
		logrus.Error(fmt.Sprintf("Bind Queue err:%+v,_queueName:%s, _key:%s, _exchange:%s", err, _queueName, _key, _exchange))
	}
}
